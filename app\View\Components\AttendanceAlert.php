<?php

namespace App\View\Components;

use Carbon\Carbon;
use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\Component;

class AttendanceAlert extends Component
{
    public $type;

    public $text;

    public $textColor = 'text-danger';

    public $isReasonRequired = false;

    public function __construct($type = 'checkin')
    {
        $this->type = $type;
    }

    public function render(): View|Closure|string
    {
        $this->prepare();

        return view('components.attendance-alert');
    }

    protected function prepare()
    {
        $type = $this->type;

        if (in_array($type, ['checkin', 'checkout'])) {
            $data = $this->getInOutStatus($type);

            $color = match ($data['status']) {
                'late' => 'text-danger',
                'leave_early' => 'text-warning',
                'on_time', 'timely' => 'text-success',
                default => 'text-dark',
            };

            $status = ucfirst(str_replace('_', ' ', strtolower($data['status'])));

            $this->text = "You $status ".$data['time'];
            $this->textColor = $color;
            $this->isReasonRequired = $this->isReasonRequired($type);
        } elseif ($type === 'break_start') {
            $this->text = 'Break Start '.now()->format('h:i A');
        } elseif ($type === 'leave_apply') {
            $leaveSummary = Auth::user()->leaveSummary();

            $totalLeave = $leaveSummary->sum('assigns_days');
            $remainingLeave = $leaveSummary->sum('remaining_days');

            $this->text = "Total : {$totalLeave} | Available : {$remainingLeave}";
        }
    }

    private function getInOutStatus($type)
    {
        $dutySchedule = Auth::user()?->attendanceConfig?->dutySchedules?->first();

        if (! $dutySchedule) {
            return ['status' => 'no_schedule', 'time' => null];
        }

        $now = Carbon::now();
        $startTime = Carbon::parse($dutySchedule->start_time);
        $endTime = Carbon::parse($dutySchedule->end_time);

        if ($type === 'checkin') {
            if ($now->lt($startTime)) {
                $diff = $startTime->diff($now);

                return [
                    'status' => 'Early',
                    'time' => $diff->format('%h h %i m'),
                ];
            } elseif ($now->gt($startTime)) {
                $diff = $now->diff($startTime);

                return [
                    'status' => 'Late',
                    'time' => $diff->format('%h h %i m'),
                ];
            } else {
                return [
                    'status' => 'on_time',
                    'time' => '0 h 0 m',
                ];
            }
        } elseif ($type === 'checkout') {
            if ($now->lt($endTime)) {
                $diff = $endTime->diff($now);

                return [
                    'status' => 'leave_early',
                    'time' => $diff->format('%h h %i m'),
                ];
            } elseif ($now->gt($endTime)) {
                $diff = $now->diff($endTime);

                return [
                    'status' => 'later',
                    'time' => $diff->format('%h h %i m'),
                ];
            } else {
                return [
                    'status' => 'timely',
                    'time' => '0 h 0 m',
                ];
            }
        }
    }

    private function isReasonRequired($type = 'checkin')
    {
        $dutySchedule = Auth::user()->dutySchedules()?->first();

        if (! $dutySchedule) {
            return false;
        }

        $now = Carbon::now();

        if ($type === 'checkin') {
            $checkinLimit = Carbon::parse($dutySchedule->start_time)->addMinutes((int) $dutySchedule->checkin_consider_time);

            return $now->gt($checkinLimit);
        } elseif ($type === 'checkout') {
            $checkoutLimit = Carbon::parse($dutySchedule->end_time)->subMinutes((int) $dutySchedule->checkout_consider_time);

            return $now->lt($checkoutLimit);
        }

        return false;
    }
}
