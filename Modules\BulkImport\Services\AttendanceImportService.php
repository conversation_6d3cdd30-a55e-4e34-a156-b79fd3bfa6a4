<?php

declare(strict_types=1);

namespace Modules\BulkImport\Services;

use App\Models\Attendance\Attendance;
use App\Models\User;
use App\Repositories\AttendanceRepository;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class AttendanceImportService
{
    protected Attendance $attendance;

    public function __construct(
        protected AttendanceRepository $attendanceRepository
    ) {
        // Initialize the attendance property (kept for compatibility)
        $this->attendance = new Attendance;
    }

    /**
     * Process attendance import data
     */
    public function processAttendanceData(array $data, array $mapping): array
    {
        $processedData = [];
        $errors = [];
        $successCount = 0;
        $errorCount = 0;

        foreach ($data as $rowIndex => $row) {
            try {
                $processedRow = $this->processRow($row, $mapping, $rowIndex);

                if ($processedRow['success']) {
                    $processedData[] = $processedRow['data'];
                    $successCount++;
                } else {
                    $errors[] = [
                        'row' => $rowIndex + 1,
                        'errors' => $processedRow['errors'],
                    ];
                    $errorCount++;
                }
            } catch (Exception $e) {
                $errors[] = [
                    'row' => $rowIndex + 1,
                    'errors' => ['General error: '.$e->getMessage()],
                ];
                $errorCount++;
            }
        }

        return [
            'processed_data' => $processedData,
            'errors' => $errors,
            'success_count' => $successCount,
            'error_count' => $errorCount,
            'total_count' => count($data),
        ];
    }

    /**
     * Process a single row of attendance data
     */
    protected function processRow(array $row, array $mapping, int $rowIndex): array
    {
        $mappedData = [];
        $errors = [];

        // Map CSV columns to database fields
        foreach ($mapping as $csvIndex => $dbField) {
            if ($dbField && isset($row[$csvIndex])) {
                $mappedData[$dbField] = trim($row[$csvIndex]);
            }
        }

        // Handle employee lookup by employee_id
        if (isset($mappedData['employee_id'])) {
            $user = User::where('employee_id', $mappedData['employee_id'])->first();
            if ($user) {
                $mappedData['user_id'] = $user->id;
            } else {
                $errors[] = 'Employee not found with the provided employee_id: '.$mappedData['employee_id'];
            }
        } else {
            $errors[] = 'Employee ID is required';
        }

        // Validate the mapped data
        $validationResult = $this->validateAttendanceData($mappedData);
        if (! $validationResult['valid']) {
            $errors = array_merge($errors, $validationResult['errors']);
        }

        // Process and format the data
        if (empty($errors)) {
            $processedData = $this->formatAttendanceData($mappedData);

            return [
                'success' => true,
                'data' => $processedData,
            ];
        }

        return [
            'success' => false,
            'errors' => $errors,
        ];
    }

    /**
     * Validate attendance data
     */
    protected function validateAttendanceData(array $data): array
    {
        $rules = [
            'user_id' => 'required|exists:users,id',
            'date' => 'required|date_format:Y-m-d',
            'time' => 'required|date_format:H:i', // Changed from H:i:s to H:i
            'type' => 'required|in:check_in,check_out,break,back',
        ];

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            return [
                'valid' => false,
                'errors' => $validator->errors()->all(),
            ];
        }

        return ['valid' => true, 'errors' => []];
    }

    /**
     * Format attendance data for the repository methods
     */
    protected function formatAttendanceData(array $data): array
    {
        $formattedData = [];

        // Basic fields needed for repository methods
        $formattedData['user_id'] = $data['user_id'];
        $formattedData['date'] = $data['date'];
        $formattedData['time'] = $data['time'];
        $formattedData['type'] = $data['type'];

        // Add import metadata
        $formattedData['imported'] = true;
        $formattedData['imported_at'] = now()->toDateTimeString();

        return $formattedData;
    }

    /**
     * Import attendance records using the repository methods
     */
    public function importAttendanceRecords(array $processedData): array
    {
        $importedCount = 0;
        $errors = [];

        // Process each record individually to prevent cascade failures
        foreach ($processedData as $data) {
            try {
                // Validate time format before processing
                if (! preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $data['time'])) {
                    throw new Exception('Invalid time format: '.$data['time'].'. Expected format: HH:MM');
                }

                // Create a comprehensive request object with all required data
                $request = $this->createComprehensiveRequest($data);

                // Debug: Log the final time value before calling repository
                Log::info('Processing attendance record', [
                    'user_id' => $data['user_id'],
                    'date' => $data['date'],
                    'original_time' => $data['time'],
                    'final_time' => $request->time,
                    'type' => $data['type'],
                ]);

                // Use repository methods based on attendance type
                switch ($data['type']) {
                    case 'check_in':
                        $this->attendanceRepository->checkIn($request);
                        break;
                    case 'break':
                        $this->attendanceRepository->breakStart($request);
                        break;
                    case 'back':
                        $this->attendanceRepository->breakEnd($request);
                        break;
                    case 'check_out':
                        $this->attendanceRepository->checkOut($request);
                        break;
                    default:
                        throw new Exception('Invalid attendance type: '.$data['type']);
                }

                $importedCount++;

            } catch (Exception $e) {
                // Log only errors for debugging
                Log::error('Attendance import failed for record', [
                    'user_id' => $data['user_id'] ?? 'unknown',
                    'date' => $data['date'] ?? 'unknown',
                    'type' => $data['type'] ?? 'unknown',
                    'time' => $data['time'] ?? 'unknown',
                    'error' => $e->getMessage(),
                ]);

                $errors[] = [
                    'user_id' => $data['user_id'] ?? 'unknown',
                    'date' => $data['date'] ?? 'unknown',
                    'error' => $e->getMessage(),
                ];
            }
        }

        return [
            'success' => true,
            'imported_count' => $importedCount,
            'errors' => $errors,
        ];
    }

    /**
     * Create a comprehensive request object that satisfies all repository requirements
     */
    protected function createComprehensiveRequest(array $data): Request
    {
        $request = new Request;

        // Format time to match what the repository expects (H:i format)
        $formattedTime = $this->formatTimeForTrait($data['time']);

        // Basic attendance data
        $request->merge([
            'user_id' => $data['user_id'],
            'date' => $data['date'],
            'time' => $formattedTime,
            'imported' => $data['imported'] ?? false,
            'imported_at' => $data['imported_at'] ?? now()->toDateTimeString(),
        ]);

        // Get user details to provide required context
        $user = User::find($data['user_id']);
        if ($user) {
            // Add location data (default values for imports)
            $request->merge([
                'latitude' => '0.0', // Default latitude for imports
                'longitude' => '0.0', // Default longitude for imports
            ]);

            // Add IP address (default for imports)
            $request->merge([
                'ip_address' => '127.0.0.1',
            ]);

            // Add selfie image if available (null for imports)
            $request->merge([
                'selfie_image' => null,
            ]);

            // Add reason if available
            $request->merge([
                'reason' => 'Bulk import',
            ]);

            // Keep original H:i format to avoid time corruption issues

            // Temporarily disabled to prevent time corruption
            // $this->adjustTimeToValidSchedule($request, $user);
        }

        return $request;
    }

    /**
     * Format time to match what the LogAttendance trait expects (H:i format)
     */
    protected function formatTimeForTrait(string $time): string
    {
        // If time has seconds (e.g., "09:00:00"), remove them to get "09:00"
        if (strlen($time) > 5) {
            return substr($time, 0, 5);
        }

        return $time;
    }

    /**
     * Adjust the time to be within the user's valid duty schedule to avoid validation errors
     */
    protected function adjustTimeToValidSchedule(Request $request, User $user): void
    {
        try {
            $attendanceConfig = $user->attendanceConfig;
            if (! $attendanceConfig) {
                return;
            }

            $dutySchedule = $attendanceConfig->dutySchedules()->first();
            if (! $dutySchedule) {
                return;
            }

            $requestTime = $request->time;

            // Validate that requestTime is a valid time format
            if (! preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9](:00)?$/', $requestTime)) {
                // If time is corrupted, don't adjust it
                return;
            }

            $scheduleStart = $dutySchedule->start_time;
            $scheduleEnd = $dutySchedule->end_time;

            // Ensure schedule times are in H:i format for comparison
            $scheduleStartFormatted = $this->formatTimeForTrait($scheduleStart);
            $scheduleEndFormatted = $this->formatTimeForTrait($scheduleEnd);

            // Validate schedule times
            if (! preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $scheduleStartFormatted) ||
                ! preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $scheduleEndFormatted)) {
                return;
            }

            // Simple string comparison for time validation (safer than Carbon parsing)
            if ($requestTime < $scheduleStartFormatted) {
                // If time is before schedule start, adjust to schedule start
                $request->merge(['time' => $scheduleStartFormatted]);
            } elseif ($requestTime > $scheduleEndFormatted) {
                // If time is after schedule end, adjust to schedule end
                $request->merge(['time' => $scheduleEndFormatted]);
            }

        } catch (Exception $e) {
            // If there's any error in time adjustment, keep the original time
            // Don't log here to avoid cluttering logs
        }
    }
}
