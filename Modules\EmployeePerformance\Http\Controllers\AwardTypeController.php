<?php

namespace Modules\EmployeePerformance\Http\Controllers;

use App\Http\Controllers\Controller;
use Exception;
use Illuminate\Http\Request;
use Modules\EmployeePerformance\Http\Requests\AwardTypeRequest;
use Modules\EmployeePerformance\Repositories\AwardTypeRepository;

use function redirect;
use function view;

class AwardTypeController extends Controller
{
    protected $repository;

    public function __construct(AwardTypeRepository $repository)
    {
        $this->repository = $repository;
    }

    public function index(Request $request)
    {
        try {
            $data['title'] = _trans('common.Reward Types');
            $data['formTitle'] = _trans('common.Add New Reward Type');
            $data['class'] = 'award_type_table_class';
            $data['collection'] = $this->repository->getPaginateData($request);

            if ($request->filled('id')) {
                $data['formTitle'] = _trans('common.Edit Reward Type');
                $data['award_type'] = $this->repository->show($request->id);
            }

            return view('employeeperformance::award_type.index')->with($data);
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function store(AwardTypeRequest $request)
    {
        try {
            $this->repository->store($request->validated());

            return redirect()->route('award_type.index')->with('success', _trans('alert.Award Type Added Successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function update(AwardTypeRequest $request, $id)
    {
        try {
            $this->repository->update($id, $request->validated());

            return redirect()->route('award_type.index')->with('success', _trans('alert.Award Type Updated Successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function delete($id)
    {
        try {
            $this->repository->delete($id);

            return redirect()->route('award_type.index')->with('success', _trans('alert.Award Type Deleted Successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }
}
