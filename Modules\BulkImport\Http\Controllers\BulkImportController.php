<?php

declare(strict_types=1);

namespace Modules\BulkImport\Http\Controllers;

use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use Modules\BulkImport\Jobs\BulkImportJob;
use Modules\BulkImport\Services\BulkImportService;
use Modules\BulkImport\Services\ImportHistoryService;

class BulkImportController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct(
        protected BulkImportService $importService,
        protected ImportHistoryService $historyService) {}

    /**
     * Show the bulk import index page.
     */
    public function index(): View
    {
        $models = array_keys(config('bulkimport.models', []));
        $recentImports = $this->historyService->getRecentImports(Auth::id(), 5);

        return view('bulk-import::index', [
            'models' => $models,
            'recentImports' => $recentImports,
            'title' => 'Bulk Import',
        ]);
    }

    /**
     * Upload and process the file for preview.
     */
    public function upload(Request $request): View|RedirectResponse
    {
        try {
            $supportedFormats = implode(',', array_keys(array_filter(config('bulkimport.supported_formats', []))));

            $request->validate([
                'csv_file' => "required|file|mimes:{$supportedFormats}",
                'model' => 'required|string|in:'.implode(',', array_keys(config('bulkimport.models', []))),
                'has_header' => 'boolean',
            ]);

            // Store the file
            $disk = config('bulkimport.disk', 'public');
            $directory = config('bulkimport.directory', 'imports');
            $path = $request->file('csv_file')->store($directory, $disk);
            $fullPath = storage_path("app/{$disk}/{$path}");

            if (! file_exists($fullPath)) {
                return redirect()->route('bulk-import.index')
                    ->withErrors(['errors' => 'Failed to upload file. Please try again.']);
            }

            $modelClass = config("bulkimport.models.{$request->model}");
            $model = app($modelClass);

            try {
                $preview = $this->importService->previewFile($fullPath, $request->boolean('has_header'));

                // Use model-specific fields and mapping
                if ($request->model === 'employee') {
                    $modelFieldsWithTypes = $this->importService->getEmployeeFieldsWithTypes();
                    $columnMapping = $this->importService->getEmployeeColumnMapping();
                    $mappedHeadings = $this->importService->mapUserColumnsToFields($preview['headings']);
                } elseif ($request->model === 'attendance') {
                    $modelFieldsWithTypes = $this->importService->getAttendanceFieldsWithTypes();
                    $columnMapping = $this->importService->getAttendanceColumnMapping();
                    $mappedHeadings = $this->importService->mapAttendanceColumnsToFields($preview['headings']);
                } elseif ($request->model === 'leave') {
                    $modelFieldsWithTypes = $this->importService->getLeaveRequestFieldsWithTypes();
                    $columnMapping = $this->importService->getLeaveRequestColumnMapping();
                    $mappedHeadings = $this->importService->mapLeaveRequestColumnsToFields($preview['headings']);
                } else {
                    $modelFieldsWithTypes = $this->importService->getModelFieldsWithTypes($model);
                    $columnMapping = [];
                    $mappedHeadings = $preview['headings'];
                }

                return view('bulk-import::map-fields', [
                    'filePath' => $path,
                    'csvHeadings' => $preview['headings'],
                    'mappedHeadings' => $mappedHeadings,
                    'csvRows' => $preview['rows'],
                    'modelFields' => array_keys($modelFieldsWithTypes),
                    'modelFieldsWithTypes' => $modelFieldsWithTypes,
                    'columnMapping' => $columnMapping,
                    'model' => $request->model,
                    'title' => 'Map Fields for Import',
                ]);
            } catch (Exception $e) {
                return redirect()->route('bulk-import.index')
                    ->withErrors(['errors' => 'Error processing file: '.$e->getMessage()]);
            }
        } catch (Exception $e) {
            return redirect()->route('bulk-import.index')
                ->withErrors(['errors' => 'Error: '.$e->getMessage()]);
        }
    }

    /**
     * Import the data based on mapping.
     */
    public function import(Request $request): RedirectResponse
    {
        try {
            $request->validate([
                'file_path' => 'required|string',
                'model' => 'required|string|in:'.implode(',', array_keys(config('bulkimport.models', []))),
                'mapping' => 'required|array',
            ]);

            $filePath = $request->file_path;
            $disk = config('bulkimport.disk', 'public');
            $fullPath = storage_path("app/{$disk}/{$filePath}");

            if (! file_exists($fullPath)) {
                return redirect()->route('bulk-import.index')
                    ->withErrors(['errors' => 'Import file not found. Please upload again.']);
            }

            $modelClass = config("bulkimport.models.{$request->model}");
            $mapping = $request->mapping;
            $hasHeader = $request->has('has_header');
            $userId = Auth::id();

            // For employee imports, map user-friendly column names to database fields
            if ($modelClass === 'App\Models\User' || $request->model === 'employee') {
                $columnMapping = $this->importService->getEmployeeColumnMapping();
                $mappedMapping = [];

                foreach ($mapping as $index => $field) {
                    if ($field !== null && $field !== '') {
                        // Find the user-friendly column name and map it to database field
                        $csvHeadings = $request->csv_headings ?? [];
                        if (isset($csvHeadings[$index])) {
                            $userFriendlyName = $csvHeadings[$index];
                            $mappedMapping[$index] = $columnMapping[$userFriendlyName] ?? $field;
                        } else {
                            $mappedMapping[$index] = $field;
                        }
                    } else {
                        $mappedMapping[$index] = $field;
                    }
                }
                $mapping = $mappedMapping;
            }

            // For attendance imports, map user-friendly column names to database fields
            if ($modelClass === 'App\Models\Attendance\Attendance' || $request->model === 'attendance') {
                $columnMapping = $this->importService->getAttendanceColumnMapping();
                $mappedMapping = [];

                foreach ($mapping as $index => $field) {
                    if ($field !== null && $field !== '') {
                        // Find the user-friendly column name and map it to database field
                        $csvHeadings = $request->csv_headings ?? [];
                        if (isset($csvHeadings[$index])) {
                            $userFriendlyName = $csvHeadings[$index];
                            $mappedMapping[$index] = $columnMapping[$userFriendlyName] ?? $field;
                        } else {
                            $mappedMapping[$index] = $field;
                        }
                    } else {
                        $mappedMapping[$index] = $field;
                    }
                }
                $mapping = $mappedMapping;
            }

            // For leave imports, map user-friendly column names to database fields
            if ($modelClass === 'App\Models\Leave\LeaveRequest' || $request->model === 'leave') {
                $columnMapping = $this->importService->getLeaveRequestColumnMapping();
                $mappedMapping = [];

                foreach ($mapping as $index => $field) {
                    if ($field !== null && $field !== '') {
                        // Find the user-friendly column name and map it to database field
                        $csvHeadings = $request->csv_headings ?? [];
                        if (isset($csvHeadings[$index])) {
                            $userFriendlyName = $csvHeadings[$index];
                            $mappedMapping[$index] = $columnMapping[$userFriendlyName] ?? $field;
                        } else {
                            $mappedMapping[$index] = $field;
                        }
                    } else {
                        $mappedMapping[$index] = $field;
                    }
                }
                $mapping = $mappedMapping;
            }

            // Create import history record
            $importHistory = $this->historyService->create($filePath, $modelClass, $userId);

            // Get queue configuration with fallbacks to default values
            $queueConnection = config('bulkimport.queue.connection', 'sync');
            $queueName = config('bulkimport.queue.name', 'default');

            // Log the queue configuration
            Log::info('Dispatching bulk import job', [
                'model_class' => $modelClass,
                'file_path' => $filePath,
                'mapping_count' => count($mapping),
                'queue_connection' => $queueConnection,
                'queue_name' => $queueName,
            ]);

            // Dispatch the job with the queue settings defined in the job class
            // When using dispatch(), onQueue() and onConnection() must be explicitly called
            BulkImportJob::dispatch($filePath, $modelClass, $mapping, $hasHeader, $userId, $importHistory->id)
                ->onConnection($queueConnection)
                ->onQueue($queueName);

            return redirect()->route('bulk-import.history')
                ->with('success', 'Import has started. You can track its progress on this page.');
        } catch (Exception $e) {
            Log::error('Error starting import: '.$e->getMessage(), [
                'exception' => $e,
            ]);

            return redirect()->route('bulk-import.index')
                ->withErrors(['errors' => 'Error starting import: '.$e->getMessage()]);
        }
    }

    /**
     * Generate attendance import template CSV
     */
    public function generateAttendanceTemplate()
    {
        $filename = 'attendance_import_template.csv';
        $filepath = storage_path("app/{$filename}");
        $handle = fopen($filepath, 'w');

        // Define headers based on user requirements
        $headers = [
            'Employee ID', // employee_id (for lookup)
            'Date', // date (YYYY-MM-DD format)
            'Time', // time (HH:MM format - no seconds for trait compatibility)
            'Type', // type (check_in, check_out, break, back)
        ];
        fputcsv($handle, $headers, ',', '"', '\\');

        // Get actual data from database for sample values
        $users = \App\Models\User::where('status', 'active')->take(5)->get(['id', 'name', 'email', 'employee_id']);

        // If no users found, create sample data
        if ($users->isEmpty()) {
            // Generate comprehensive sample data with all attendance types
            $this->generateComprehensiveSampleData($handle);
        } else {
            // Generate sample rows from actual users with all attendance types
            $this->generateUserBasedSampleData($handle, $users);
        }

        fclose($handle);

        return Response::download($filepath)->deleteFileAfterSend(true);
    }

    /**
     * Generate comprehensive sample data with all attendance types
     */
    private function generateComprehensiveSampleData($handle)
    {
        $employeeIds = ['EMP001', 'EMP002'];

        // Generate 2 employees with complete daily attendance sequences for 2 dates each
        foreach ($employeeIds as $employeeIndex => $employeeId) {
            // Generate attendance for one starting date (the method will generate 2 consecutive dates)
            $date = date('Y-m-d', strtotime('-1 days'));

            // Skip weekends (Saturday = 6, Sunday = 0)
            $dayOfWeek = date('w', strtotime($date));
            if ($dayOfWeek == 0 || $dayOfWeek == 6) {
                // If it's weekend, use the previous Friday
                $date = date('Y-m-d', strtotime('last friday'));
            }

            // Generate complete daily sequence for this employee (will generate 2 dates)
            $this->generateDailyAttendanceSequence($handle, $employeeId, $date, $employeeIndex);
        }
    }

    /**
     * Generate user-based sample data with complete daily sequences
     */
    private function generateUserBasedSampleData($handle, $users)
    {
        // Take only the first 2 users for sample data
        $users = $users->take(2);
        foreach ($users as $userIndex => $user) {
            // Generate attendance for one starting date (the method will generate 2 consecutive dates)
            $date = date('Y-m-d', strtotime('-1 days'));

            // Skip weekends
            $dayOfWeek = date('w', strtotime($date));
            if ($dayOfWeek == 0 || $dayOfWeek == 6) {
                // If it's weekend, use the previous Friday
                $date = date('Y-m-d', strtotime('last friday'));
            }

            // Generate complete daily sequence for this user (will generate 2 dates)
            $this->generateDailyAttendanceSequence($handle, $user->employee_id ?? (string) $user->id, $date, $userIndex);
        }
    }

    /**
     * Generate a complete daily attendance sequence for one employee on two dates
     */
    private function generateDailyAttendanceSequence($handle, $employeeId, $date, $employeeIndex)
    {
        // Define different shifts based on employee index
        if ($employeeIndex == 0) {
            // EMP001: 10:00-18:00 shift
            $scheduleTimes = [
                'check_in' => '10:00', // 10:00 AM (shift start)
                'break' => '13:00', // 1:00 PM (lunch break)
                'back' => '14:00', // 2:00 PM (back from lunch)
                'check_out' => '18:00', // 6:00 PM (shift end)
            ];
        } else {
            // EMP002: 15:00-23:00 shift (night shift)
            $scheduleTimes = [
                'check_in' => '15:00', // 3:00 PM (shift start)
                'break' => '18:00', // 6:00 PM (dinner break)
                'back' => '19:00', // 7:00 PM (back from dinner)
                'check_out' => '23:00', // 11:00 PM (shift end)
            ];
        }

        // Generate attendance for two dates: the provided date and the next day
        $dates = [$date, date('Y-m-d', strtotime($date.' +1 day'))];

        foreach ($dates as $currentDate) {
            // Add random variation to make times more realistic
            foreach ($scheduleTimes as $type => $baseTime) {
                // Add random variation: -15 to +15 minutes for check_in/check_out, -10 to +10 for break/back
                $variationRange = in_array($type, ['check_in', 'check_out']) ? 15 : 10;
                $randomVariation = rand(-$variationRange, $variationRange);

                $time = $this->addTimeVariation($baseTime, $randomVariation);

                $row = [
                    $employeeId,
                    $currentDate, // Different date for each set of records
                    $time,
                    $type,
                ];

                fputcsv($handle, $row, ',', '"', '\\');
            }
        }
    }

    /**
     * Add time variation to base time
     */
    private function addTimeVariation(string $baseTime, int $variationMinutes): string
    {
        $time = Carbon::createFromFormat('H:i', $baseTime);
        $time->addMinutes($variationMinutes);

        return $time->format('H:i');
    }

    /**
     * Generate employee import template CSV
     */
    public function generateEmployeeTemplate()
    {
        $filename = 'employee_import_template.csv';
        $filepath = storage_path("app/{$filename}");
        $handle = fopen($filepath, 'w');

        // Define comprehensive headers for employee import (matching form field names exactly)
        $headers = [
            // Employee Setup (Step 1)
            'Employee Name', 'Email Address', 'Phone Number', 'Employee ID', 'Job Type',
            'Department', 'Designation', 'Role', 'Status', 'Joining Date',
            'Duty Schedule', 'Attendance Method',

            // Personal Information (Step 2)
            'Country', 'Religion', 'Blood Group', 'Speak Language', 'Gender',
            'Address', 'Marital Status', 'Date of Birth',

            // Official Setup (Step 3)
            'Manager', 'Weekend',

            // Personal Documents (Step 4)
            'Passport Number', 'Driving License', 'PAN Number', 'Aadhar Number',

            // Salary Configuration (Step 5)
            'Contract Start Date', 'Payslip Type', 'Gross Salary', 'Basic Salary',
            'Payment Method', 'Bank Name', 'Bank Account Number',
        ];
        fputcsv($handle, $headers, ',', '"', '\\');

        // Get actual data from database
        $departments = \App\Models\Department::where('status', 'active')->pluck('id')->toArray();
        $designations = \App\Models\Designation::where('status', 'active')->pluck('id')->toArray();
        $managers = \App\Models\User::where('status', 'active')->pluck('id')->toArray();
        $roles = \App\Models\Role::where('status', 'active')->pluck('id')->toArray();
        $countries = \App\Models\Country::pluck('id')->toArray();
        $dutySchedules = \App\Models\Attendance\DutySchedule::pluck('id')->toArray();

        // Fallback values if no data exists
        if (empty($departments)) {
            $departments = [1];
        }

        if (empty($designations)) {
            $designations = [1];
        }

        if (empty($managers)) {
            $managers = [1];
        }

        if (empty($roles)) {
            $roles = [1];
        }

        if (empty($countries)) {
            $countries = [1];
        }

        if (empty($dutySchedules)) {
            $dutySchedules = [1];
        }

        // Form field options
        $genders = ['Male', 'Female'];
        $statuses = ['Active', 'Inactive'];
        $jobTypes = ['Full Time', 'Part Time'];
        $religions = ['Christianity', 'Islam', 'Hinduism', 'Buddhism', 'Judaism', 'Other'];
        $bloodGroups = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];
        $maritalStatus = ['Single', 'Married', 'Divorced', 'Widowed'];
        $languages = ['English', 'Spanish', 'French', 'German', 'Chinese', 'Arabic'];
        $weekends = ['Saturday', 'Sunday', 'Friday'];
        $attendanceMethods = ['Normal Attendance', 'Qr Based Attendance', 'Selfie Based Attendance'];
        $payslipTypes = ['Monthly', 'Weekly', 'Daily', 'Hourly'];
        $paymentMethods = ['Bank Transfer', 'Cash', 'Check'];

        // Generate 50 sample rows
        for ($i = 1; $i <= 50; $i++) {
            $id = str_pad((string) $i, 3, '0', STR_PAD_LEFT);
            $row = [
                // Employee Setup (Step 1)
                "Employee {$i}", // Employee Name
                "employee{$i}@company.com", // Email Address
                '01'.str_pad((string) ($i % *********), 8, '0', STR_PAD_LEFT), // Phone Number
                "EMP{$id}", // Employee ID
                $jobTypes[array_rand($jobTypes)], // Job Type
                (string) $departments[array_rand($departments)], // Department
                (string) $designations[array_rand($designations)], // Designation
                (string) $roles[array_rand($roles)], // Role
                $statuses[array_rand($statuses)], // Status
                date('Y-m-d', strtotime('-'.rand(1, 10).' years')), // Joining Date
                (string) $dutySchedules[array_rand($dutySchedules)], // Duty Schedule
                $attendanceMethods[array_rand($attendanceMethods)], // Attendance Method

                // Personal Information (Step 2)
                (string) $countries[array_rand($countries)], // Country
                $religions[array_rand($religions)], // Religion
                $bloodGroups[array_rand($bloodGroups)], // Blood Group
                $languages[array_rand($languages)], // Speak Language
                $genders[array_rand($genders)], // Gender
                "Address {$i}, City, State", // Address
                $maritalStatus[array_rand($maritalStatus)], // Marital Status
                date('Y-m-d', strtotime('-'.rand(25, 50).' years')), // Date of Birth

                // Official Setup (Step 3)
                (string) $managers[array_rand($managers)], // Manager
                implode(', ', array_slice($weekends, 0, rand(1, 2))), // Weekend

                // Personal Documents (Step 4)
                "PASS{$id}", // Passport Number
                "DL{$id}", // Driving License
                "PAN{$id}", // PAN Number
                "AADHAR{$id}", // Aadhar Number

                // Salary Configuration (Step 5)
                date('Y-m-d', strtotime('-'.rand(1, 5).' years')), // Contract Start Date
                $payslipTypes[array_rand($payslipTypes)], // Payslip Type
                (string) rand(50000, 100000), // Gross Salary
                (string) rand(30000, 60000), // Basic Salary
                $paymentMethods[array_rand($paymentMethods)], // Payment Method
                "Bank Name {$i}", // Bank Name
                "BANK{$id}", // Bank Account Number
            ];

            fputcsv($handle, $row, ',', '"', '\\');
        }

        fclose($handle);

        return Response::download($filepath)->deleteFileAfterSend(true);
    }

    /**
     * Generate leave import template CSV
     */
    public function generateLeaveTemplate()
    {
        $filename = 'leave_import_template.csv';
        $filepath = storage_path("app/{$filename}");
        $handle = fopen($filepath, 'w');

        // Define headers based on leave request requirements
        $headers = [
            'Leave Assign ID', // leave_assign_id (optional, for specific leave type assignment)
            'Employee ID', // user_id (required, the employee requesting leave)
            'Apply Date', // apply_date (optional, defaults to current date)
            'Leave From', // leave_from (required, start date YYYY-MM-DD)
            'Leave To', // leave_to (required, end date YYYY-MM-DD)
            'Days', // days (optional, calculated automatically)
            'Reason', // reason (optional, leave reason)
            'Response', // response (optional, admin response)
            'Substitute ID', // substitute_id (optional, substitute employee ID)
            'Status', // status (optional, defaults to 'pending')
            'Referred To', // referred_to (optional, user ID to refer to)
            'Force Paid Leave', // force_paid_leave (optional, boolean)
        ];
        fputcsv($handle, $headers, ',', '"', '\\');

        // Get actual data from database for sample values
        $users = \App\Models\User::where('status', 'active')->take(5)->get(['id', 'name', 'email', 'employee_id']);
        $leaveAssigns = \App\Models\Leave\LeaveAssign::where('status', 'active')->take(3)->get(['id', 'type_id']);
        $leaveTypes = \App\Models\Leave\LeaveType::whereIn('id', $leaveAssigns->pluck('type_id'))->get(['id', 'name']);

        // If no data found, create sample data
        if ($users->isEmpty()) {
            $this->generateComprehensiveLeaveSampleData($handle);
        } else {
            // Generate sample rows from actual users
            $this->generateUserBasedLeaveSampleData($handle, $users, $leaveAssigns, $leaveTypes);
        }

        fclose($handle);

        return Response::download($filepath)->deleteFileAfterSend(true);
    }

    /**
     * Generate comprehensive sample data for leave import
     */
    private function generateComprehensiveLeaveSampleData($handle)
    {
        $employeeIds = [1, 2, 3, 4, 5];
        $leaveAssignIds = [1, 2, 3];
        $statuses = ['pending', 'approved', 'rejected'];
        $reasons = ['Annual Leave', 'Sick Leave', 'Personal Leave', 'Maternity Leave', 'Paternity Leave'];

        // Generate 20 sample rows
        for ($i = 1; $i <= 20; $i++) {
            $employeeId = $employeeIds[array_rand($employeeIds)];
            $leaveFrom = date('Y-m-d', strtotime('+'.rand(1, 30).' days'));
            $leaveTo = date('Y-m-d', strtotime($leaveFrom.' +'.rand(1, 7).' days'));

            $row = [
                (string) $leaveAssignIds[array_rand($leaveAssignIds)], // Leave Assign ID
                (string) $employeeId, // Employee ID
                date('Y-m-d'), // Apply Date
                $leaveFrom, // Leave From
                $leaveTo, // Leave To
                (string) rand(1, 7), // Days
                $reasons[array_rand($reasons)], // Reason
                '', // Response (empty for new requests)
                (string) ($employeeIds[array_rand($employeeIds)]), // Substitute ID
                $statuses[array_rand($statuses)], // Status
                '', // Referred To (empty for most cases)
                rand(0, 1) ? 'true' : 'false', // Force Paid Leave
            ];

            fputcsv($handle, $row, ',', '"', '\\');
        }
    }

    /**
     * Generate user-based sample data for leave import
     */
    private function generateUserBasedLeaveSampleData($handle, $users, $leaveAssigns, $leaveTypes)
    {
        $statuses = ['pending', 'approved', 'rejected'];
        $reasons = ['Annual Leave', 'Sick Leave', 'Personal Leave', 'Maternity Leave', 'Paternity Leave'];

        // Generate sample rows from actual users
        foreach ($users as $index => $user) {
            // Generate 2-3 leave requests per user
            for ($j = 1; $j <= rand(2, 3); $j++) {
                $leaveFrom = date('Y-m-d', strtotime('+'.rand(1, 30).' days'));
                $leaveTo = date('Y-m-d', strtotime($leaveFrom.' +'.rand(1, 7).' days'));

                $row = [
                    (string) ($leaveAssigns->random()->id ?? 1), // Leave Assign ID
                    (string) $user->id, // Employee ID
                    date('Y-m-d'), // Apply Date
                    $leaveFrom, // Leave From
                    $leaveTo, // Leave To
                    (string) rand(1, 7), // Days
                    $reasons[array_rand($reasons)], // Reason
                    '', // Response (empty for new requests)
                    (string) ($users->random()->id ?? ''), // Substitute ID
                    $statuses[array_rand($statuses)], // Status
                    '', // Referred To (empty for most cases)
                    rand(0, 1) ? 'true' : 'false', // Force Paid Leave
                ];

                fputcsv($handle, $row, ',', '"', '\\');
            }
        }
    }
}