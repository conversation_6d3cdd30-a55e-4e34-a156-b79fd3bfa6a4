<?php

namespace Modules\AssetManagement\Database\Seeders;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Seeder;
use Modules\AssetManagement\Entities\Asset;
use Modules\AssetManagement\Entities\AssetLog;

class AssetHistoryTableSeeder extends Seeder
{
    public function run()
    {
        Model::unguard();

        $assets = Asset::all();

        AssetLog::truncate();

        $totalRecords = 0;
        $targetRecords = rand(60, 70); // Randomly pick between 60-70

        while ($totalRecords < $targetRecords) {
            foreach ($assets as $asset) {
                if ($totalRecords >= $targetRecords) {
                    break;
                }

                AssetLog::create([
                    'asset_id' => $asset->id,
                    'assigned_to' => $asset->assigned_to,
                    'remarks' => 'Generated log for testing',
                    'action_type' => ['assign', 'update', 'return'][array_rand(['assign', 'update', 'return'])],
                    'logged_at' => now()->subDays(rand(0, 30)),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                $totalRecords++;
            }
        }
    }
}
