<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Repositories\ReportRepository;
use App\Repositories\UserRepositoryV2;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ReportController extends Controller
{
    public function __construct(
        protected UserRepositoryV2 $userRepository,
        protected ReportRepository $repository,
    ) {}

    public function leaveReport(Request $request)
    {
        try {
            $data['title'] = 'Leave Reports';
            $data['collection'] = $this->repository->leaveReport($request);

            return view('backend.reports.leave')->with($data);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    public function attendanceReport(Request $request)
    {
        try {
            $data['title'] = 'Attendance Reports';
            $data['reportTitle'] = 'Attendance Report - '.date('F Y');
            $data['generate'] = 'Generated at: '.date('d F Y, h:i A').' by '.Auth::user()->name;
            $data['employees'] = $this->userRepository->employees();
            $data['collection'] = $this->repository->attendanceReport($request);

            return view('backend.reports.attendance_report')->with($data);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    public function attendanceReportDetail(Request $request)
    {
        try {
            $data['title'] = 'Attendance Report Details';
            $data['reportTitle'] = 'Attendance Report - '.date('F Y');
            $data['generate'] = 'Generated at: '.date('d F Y, h:i A').' by '.Auth::user()->name;
            $data['employees'] = $this->userRepository->employees();
            $data['collection'] = $this->repository->attendanceDetails($request);

            // Calculate total number of employees for rowspan
            $data['numberOfUsers'] = $data['collection']->count();
            $data['weekendRowspan'] = 2 + $data['numberOfUsers']; // 2 header rows + number of employees

            // Table headers
            $data['tableHeaders'] = '
                <th rowspan="2" valign="middle" class="text-center">Check In</th>
                <th rowspan="2" valign="middle" class="text-center">Check Out</th>
                <th rowspan="2" valign="middle" class="text-center">Total Hours</th>
                <th colspan="2" class="text-center">Entry</th>
                <th colspan="2" class="text-center">Exit</th>
                <th rowspan="2" valign="middle" class="text-center">Net Hours</th>
            ';

            $data['tableBodyTh'] = '
                <th class="text-center">Early</th>
                <th class="text-center">late</th>
                <th class="text-center">Early</th>
                <th class="text-center">late</th>';

            // Process chronological days
            $data['chronologicalDays'] = $this->repository->processChronologicalDays($data['collection']);

            // Process employee statistics
            $data['employeeStats'] = $this->repository->processEmployeeStats($data['collection']);

            return view('backend.reports.attendance_details')->with($data);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    public function monthlyTimesheet(Request $request)
    {
        try {
            $data['title'] = 'Monthly Time sheet';
            $data['employees'] = $this->userRepository->employees();
            $data['collection'] = $this->repository->monthlyTimesheet($request);

            // dd($data['collection']['data']);

            return view('backend.reports.monthly_timesheet')->with($data);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', $e->getMessage());
        }
    }
}
