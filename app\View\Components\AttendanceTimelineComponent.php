<?php

namespace App\View\Components;

use Carbon\Carbon;
use Carbon\CarbonInterval;
use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class AttendanceTimelineComponent extends Component
{
    public $row;

    public $timeline;

    public $times;

    public $duration;

    public $totalWorking;

    /**
     * Create a new component instance.
     */
    public function __construct($row)
    {
        $this->row = $row;

        if ($row == null) {
            $this->timeline = [];
            $this->times = [];
            $this->duration = 'N/A';

            return;
        }

        $schedule_start_time = $row->check_in_info['schedule_start_time'] ?? '00:00:00';
        $schedule_end_time = $row->check_in_info['schedule_end_time'] ?? '23:59:59';
        $checkin_consider_time = (int) ($row->check_in_info['checkin_consider_time'] ?? 0);
        $checkout_consider_time = (int) ($row->check_in_info['checkout_consider_time'] ?? 0);

        // Get the attendance date from check_in or use today
        $attendanceDate = @$row->check_in ? Carbon::parse($row->check_in)->startOfDay() : Carbon::now()->startOfDay();

        $scheduleStart = Carbon::createFromFormat('H:i:s', $schedule_start_time)->setDateFrom($attendanceDate);
        $scheduleEnd = Carbon::createFromFormat('H:i:s', $schedule_end_time)->setDateFrom($attendanceDate);

        $attendance = collect($row->attendance_log ?? []);
        $breaks = $attendance->filter(function ($log) {
            return $log['type'] === 'break';
        })->sortBy('start_time')->values();

        // Calculate total duration for percent
        $firstStart = @$row->check_in ? Carbon::parse($row->check_in) : $scheduleStart;
        $checkOut = @$row->check_out ? Carbon::parse($row->check_out) : null;

        if (! $checkOut && isset($row->check_in_info['auto_check_out_time'])) {
            $autoCheckoutTime = Carbon::parse($row->check_in_info['auto_check_out_time']);
            if (now()->greaterThan($autoCheckoutTime)) {
                $checkOut = Carbon::createFromFormat('H:i:s', $schedule_end_time)->setDateFrom($attendanceDate);
            }
        }

        $this->duration = $this->formatMinutesToHours($firstStart->diffInMinutes($checkOut ?: now()));

        // Determine if this is current day or previous day
        $isCurrentDay = $attendanceDate->isToday();
        $currentTime = $isCurrentDay ? Carbon::now() : ($checkOut ?: $firstStart->copy()->addHours(8)); // Default 8 hours for previous days

        $endBoundary = $checkOut ?: $currentTime;
        if ($endBoundary->lessThanOrEqualTo($firstStart)) {
            $endBoundary = $endBoundary->copy()->addDay();
        }
        $totalDuration = $firstStart->diffInMinutes($endBoundary);

        // Split breaks into before and after scheduleEnd
        $breaksBeforeScheduleEnd = $breaks->filter(function ($break) use ($scheduleEnd, $attendanceDate) {
            $breakStart = Carbon::createFromFormat('H:i:s', $break['start_time'])->setDateFrom($attendanceDate);

            return $breakStart->lessThan($scheduleEnd);
        })->values();
        $breaksInOvertime = $breaks->filter(function ($break) use ($scheduleEnd, $attendanceDate) {
            $breakStart = Carbon::createFromFormat('H:i:s', $break['start_time'])->setDateFrom($attendanceDate);

            return $breakStart->greaterThanOrEqualTo($scheduleEnd);
        })->values();
        $timeline = collect();

        if ($row->status == 'absent') {
            $timeline->push([
                'type' => 'Absent',
                'title' => 'Absent',
                'reason' => 'Not present',
                'start_time' => $scheduleStart->format('h:i A') ?? '00:00:00',
                'end_time' => $firstStart->format('h:i A') ?? '00:00:00',
                'difference' => $delayMinutes ?? 0,
                'class' => 'late',
                'percent' => $percent ?? 100,
            ]);
        } else {
            // Add late or consider_time block if first check-in is after schedule_start_time
            if ($firstStart->greaterThan($scheduleStart)) {
                $delayMinutes = $scheduleStart->diffInMinutes($firstStart);
                $percent = $totalDuration > 0 ? round(($delayMinutes / $totalDuration) * 100, 2) : 0;
                if ($delayMinutes > $checkin_consider_time) {
                    $timeline->push([
                        'type' => 'late',
                        'title' => 'Late',
                        'reason' => 'Late Arrival',
                        'start_time' => $scheduleStart->format('h:i A'),
                        'end_time' => $firstStart->format('h:i A'),
                        'difference' => $delayMinutes,
                        'class' => 'late',
                        'percent' => $percent,
                    ]);
                } else {
                    $timeline->push([
                        'type' => 'consider_time',
                        'title' => 'Consider',
                        'reason' => 'Consider Time',
                        'start_time' => $scheduleStart->format('h:i A'),
                        'end_time' => $firstStart->format('h:i A'),
                        'difference' => $delayMinutes,
                        'class' => 'consider',
                        'percent' => $percent,
                    ]);
                }
            }
            $lastEnd = $firstStart->copy();

            // Only process breaks before scheduleEnd in the main loop
            foreach ($breaksBeforeScheduleEnd as $break) {
                $breakStart = Carbon::createFromFormat('H:i:s', $break['start_time'])->setDateFrom($attendanceDate);
                $breakEnd = $break['end_time']
                    ? Carbon::createFromFormat('H:i:s', $break['end_time'])->setDateFrom($attendanceDate)
                    : $currentTime;
                // Add check_in (working) block before this break if needed
                if ($lastEnd->lt($breakStart)) {
                    $difference = $lastEnd->diffInMinutes($breakStart);
                    $percent = $totalDuration > 0 ? round(($difference / $totalDuration) * 100, 2) : 0;
                    $timeline->push([
                        'type' => 'check_in',
                        'title' => 'Working',
                        'reason' => 'Working',
                        'start_time' => $lastEnd->format('h:i A'),
                        'end_time' => $breakStart->format('h:i A'),
                        'difference' => $difference,
                        'class' => 'working',
                        'percent' => $percent,
                    ]);
                }
                // Add the break itself
                $difference = $breakStart->diffInMinutes($breakEnd);
                $percent = $totalDuration > 0 ? round(($difference / $totalDuration) * 100, 2) : 0;
                $timeline->push([
                    'type' => 'break',
                    'title' => 'Break',
                    'reason' => $break['reason'] ?? 'On Break',
                    'start_time' => $breakStart->format('h:i A'),
                    'end_time' => $breakEnd->format('h:i A'),
                    'difference' => $difference,
                    'class' => 'break',
                    'percent' => $percent,
                ]);
                $lastEnd = $breakEnd->copy();
            }

            // Add check_in (working) block after last break if needed (before overtime)
            if ($lastEnd->lt($endBoundary) && $lastEnd->lt($scheduleEnd)) {
                $workingEnd = $endBoundary->lessThanOrEqualTo($scheduleEnd) ? $endBoundary : $scheduleEnd;
                $difference = $lastEnd->diffInMinutes($workingEnd);
                $percent = $totalDuration > 0 ? round(($difference / $totalDuration) * 100, 2) : 0;
                $timeline->push([
                    'type' => 'check_in',
                    'title' => 'Working',
                    'reason' => 'Working',
                    'start_time' => $lastEnd->format('h:i A'),
                    'end_time' => $workingEnd->format('h:i A'),
                    'difference' => $difference,
                    'class' => 'working',
                    'percent' => $percent,
                ]);
                $lastEnd = $workingEnd->copy();
            }

            // If employee checked out before schedule end, mark as "Left Early"
            if ($checkOut && $checkOut->lt($scheduleEnd)) {
                $difference = $checkOut->diffInMinutes($scheduleEnd);
                $percent = $totalDuration > 0 ? round(($difference / $totalDuration) * 100, 2) : 0;

                $timeline->push([
                    'type' => 'left_early',
                    'title' => 'Left Early',
                    'reason' => 'Checked out before scheduled end',
                    'start_time' => $checkOut->format('h:i A'),
                    'end_time' => $scheduleEnd->format('h:i A'),
                    'difference' => $difference,
                    'class' => 'late',
                    'percent' => $percent,
                ]);
            }

            // Handle overtime with breaks in overtime
            if ($endBoundary->gt($scheduleEnd)) {
                $overtimeStart = $scheduleEnd->copy();
                foreach ($breaksInOvertime as $break) {
                    $breakStart = Carbon::createFromFormat('H:i:s', $break['start_time'])->setDateFrom($attendanceDate);
                    $breakEnd = $break['end_time']
                        ? Carbon::createFromFormat('H:i:s', $break['end_time'])->setDateFrom($attendanceDate)
                        : $currentTime;
                    // Add overtime block before this break if needed
                    if ($overtimeStart->lt($breakStart)) {
                        $difference = $overtimeStart->diffInMinutes($breakStart);
                        $percent = $totalDuration > 0 ? round(($difference / $totalDuration) * 100, 2) : 0;
                        $timeline->push([
                            'type' => 'overtime',
                            'title' => 'Overtime',
                            'reason' => '',
                            'start_time' => $overtimeStart->format('h:i A'),
                            'end_time' => $breakStart->format('h:i A'),
                            'difference' => $difference,
                            'class' => 'overtime',
                            'percent' => $percent,
                        ]);
                    }
                    // Add the break block
                    $difference = $breakStart->diffInMinutes($breakEnd);
                    $percent = $totalDuration > 0 ? round(($difference / $totalDuration) * 100, 2) : 0;
                    $timeline->push([
                        'type' => 'break',
                        'title' => 'Break',
                        'reason' => $break['reason'] ?? '',
                        'start_time' => $breakStart->format('h:i A'),
                        'end_time' => $breakEnd->format('h:i A'),
                        'difference' => $difference,
                        'class' => 'break',
                        'percent' => $percent,
                    ]);
                    $overtimeStart = $breakEnd->copy();
                }
                // Add final overtime block after last break if needed
                if ($overtimeStart->lt($endBoundary)) {
                    $difference = $overtimeStart->diffInMinutes($endBoundary);
                    $percent = $totalDuration > 0 ? round(($difference / $totalDuration) * 100, 2) : 0;
                    $timeline->push([
                        'type' => 'overtime',
                        'title' => 'Overtime',
                        'reason' => '',
                        'start_time' => $overtimeStart->format('h:i A'),
                        'end_time' => $endBoundary->format('h:i A'),
                        'difference' => $difference,
                        'class' => 'overtime',
                        'percent' => $percent,
                    ]);
                }
            }

            // Only show remaining time for current day without check-out
            if ($isCurrentDay && ! $row->check_out && $currentTime->lt($scheduleEnd)) {
                $difference = $currentTime->diffInMinutes($scheduleEnd);
                $percent = $totalDuration > 0 ? round(($difference / $totalDuration) * 100, 2) : 0;
                $timeline->push([
                    'type' => 'remaining',
                    'title' => 'Remaining',
                    'reason' => '',
                    'start_time' => $currentTime->format('h:i A'),
                    'end_time' => $scheduleEnd->format('h:i A'),
                    'difference' => $difference,
                    'class' => 'remaining',
                    'percent' => $percent,
                ]);
            }
        }

        $this->timeline = $timeline->values();
        // Times and duration logic can remain as before
        $timeData = $this->generateTimeSlotsFromScheduleAndLogs(
            $schedule_start_time,
            $schedule_end_time,
            $row->attendance_log ?? [],
            $row->check_out ?? null,
            60,
            $isCurrentDay,
            $attendanceDate
        );

        // list($hours, $minutes, $seconds) = explode(':', $this->row->worked_time ?? '00:00:00');
        // $totalMinutes = ($hours * 60) + $minutes;

        $this->totalWorking = $this->formatMinutesToHours(CarbonInterval::createFromFormat('H:i:s', @$this->row->worked_time?->format('H:i:s') ?? now()->format('H:i:s'))->totalMinutes);
        $this->times = $timeData['slots'];
        $timelineStart = $timeData['start'];
        $timelineEnd = $timeData['end'];
        $totalDuration = $timelineStart->diffInMinutes($timelineEnd);
    }

    public function formatMinutesToHours($minutes)
    {
        $hours = floor($minutes / 60);
        $mins = $minutes % 60;

        if ($hours > 0 && $mins > 0) {
            return "{$hours}h {$mins}min";
        } elseif ($hours > 0) {
            return "{$hours}h";
        } else {
            return "{$mins}min";
        }
    }

    private function generateTimeSlotsFromScheduleAndLogs($schedule_start_time, $schedule_end_time, $attendance, $check_out, $interval, $isCurrentDay, $attendanceDate)
    {
        $scheduleStart = Carbon::createFromFormat('H:i:s', $schedule_start_time)->setDateFrom($attendanceDate);
        $scheduleEnd = Carbon::createFromFormat('H:i:s', $schedule_end_time)->setDateFrom($attendanceDate);

        $currentTime = $isCurrentDay ? Carbon::now() : ($check_out ? Carbon::parse($check_out) : $scheduleStart->copy()->addHours(8));

        // Convert first and last log times if present
        $firstLogStart = data_get($attendance, '0.start_time');
        $lastLogEnd = data_get($attendance, count($attendance) - 1 .'.end_time');

        $firstCandidates = [$scheduleStart];

        if (@$this->row->check_in) {
            $firstCandidates[] = Carbon::parse($this->row->check_in)->setDateFrom($scheduleStart);
        }
        if ($firstLogStart) {
            $firstCandidates[] = Carbon::createFromFormat('H:i:s', $firstLogStart)->setDateFrom($attendanceDate);
        }
        $first = collect($firstCandidates)->min();

        $lastLogEndTime = $lastLogEnd
            ? Carbon::createFromFormat('H:i:s', $lastLogEnd)->setDateFrom($attendanceDate)
            : null;

        $checkOutTime = $check_out ? Carbon::parse($check_out) : null;

        $last = collect([
            $lastLogEndTime,
            $checkOutTime,
            $scheduleEnd,
            $currentTime,
        ])->filter()->max();

        // Handle overnight shifts
        if ($last->lessThanOrEqualTo($first)) {
            $last->addDay();
        }

        // Build time slots
        $slots = [];
        $current = $first->copy();
        while ($current < $last) {
            $slots[] = $current->format('h:i A');
            $current->addMinutes($interval);
        }
        // Only add the final end time if it's not already present and if it's at least one full interval after the previous slot
        if (! empty($slots)) {
            $lastSlotTime = Carbon::createFromFormat('h:i A', end($slots))->setDateFrom($last);
            if ($last->greaterThan($lastSlotTime)) {
                $slots[] = $last->format('h:i A');
            }
        } else {
            $slots[] = $last->format('h:i A');
        }

        // Limit to maximum 11 slots, spaced as evenly as possible
        $maxSlots = 11;
        $count = count($slots);
        $maxSlots = 11;
        $count = count($slots);
        if ($count > $maxSlots) {
            $result = [];
            for ($i = 0; $i < $maxSlots; $i++) {
                $index = (int) round($i * ($count - 1) / ($maxSlots - 1));
                $result[] = $slots[$index];
            }

            $slots = $result;
        }

        // Always return full structure
        return [
            'slots' => $slots,
            'start' => $first,
            'end' => $last,
        ];
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.attendance-timeline-component');
    }
}
