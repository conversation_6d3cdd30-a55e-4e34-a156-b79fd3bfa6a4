@extends('backend.layouts.app')
@section('title', @$data['title'])
@section('content')

    <x-container cardClass="mb-40" title="Bulk Import">
        <div class="row">
            <div class="col-12">
                <div class="bulk-card-widget-container">
                    @for ($i = 1; $i <= 9; $i++)
                        {{-- item 1 --}}
                        <div class="bulk-card-widget position-relative z-0">
                            <div class="bulk-card-body d-flex align-items-center gap-16">
                                <div class="icon">
                                    <svg width="66" height="68" viewBox="0 0 66 68" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                            d="M7.98163 40.1925C25.0435 43.1516 42.1057 43.0521 59.1676 40.1925V16.472C59.1676 16.2017 59.0801 15.939 58.918 15.7229L47.682 0.741663C47.4462 0.427356 47.0762 0.242188 46.6834 0.242188H11.727C10.7337 0.242188 9.78088 0.636794 9.07856 1.33912C8.37624 2.04174 7.98163 2.99425 7.98163 3.98751V32.7018C7.98163 34.5559 7.54688 36.1274 7.54688 37.3802C7.54688 39.112 7.98163 40.1925 7.98163 40.1925Z"
                                            fill="#D8DFE3" />
                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                            d="M0.490234 36.9531V63.9098C0.490234 64.9031 0.884841 65.8559 1.58716 66.5582C2.28949 67.2605 3.24229 67.6551 4.23555 67.6551H61.6639C62.6571 67.6551 63.6096 67.2605 64.3123 66.5582C65.0146 65.8559 65.4092 64.9031 65.4092 63.9098C65.4092 59.126 65.4092 48.7188 65.4092 43.935C65.4092 42.9417 65.0146 41.9889 64.3123 41.2866C63.6096 40.5843 62.6571 40.1897 61.6639 40.1897H5.48409C3.89128 40.1897 0.490234 36.9531 0.490234 36.9531Z"
                                            fill="#16B274" />
                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                            d="M4.23555 40.1938C3.09578 40.1938 2.26372 39.7314 1.65128 39.1621C1.0751 38.6264 0.490234 37.6823 0.490234 36.4484C0.490234 35.6565 0.884841 34.5024 1.58716 33.8001C2.28949 33.0977 3.24229 32.7031 4.23555 32.7031H7.98087V40.1938C7.98087 40.1938 8.30177 40.1938 4.23555 40.1938Z"
                                            fill="#16B274" />
                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                            d="M46.6816 0.242188V12.7267C46.6816 13.7199 47.0762 14.6727 47.7786 15.3751C48.4809 16.0774 49.4334 16.472 50.427 16.472H59.1658C59.1658 16.2017 59.0783 15.939 58.9162 15.7229L47.6803 0.741663C47.4445 0.427356 47.0744 0.242188 46.6816 0.242188Z"
                                            fill="#AFBDC7" />
                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                            d="M19.2173 17.7197H35.4471C36.1359 17.7197 36.6953 17.1603 36.6953 16.4712C36.6953 15.7821 36.1359 15.2227 35.4471 15.2227H19.2173C18.5282 15.2227 17.9688 15.7821 17.9688 16.4712C17.9688 17.1603 18.5282 17.7197 19.2173 17.7197Z"
                                            fill="#16B274" />
                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                            d="M19.2173 25.2119H47.9313C48.6204 25.2119 49.1798 24.6525 49.1798 23.9634C49.1798 23.2742 48.6204 22.7148 47.9313 22.7148H19.2173C18.5282 22.7148 17.9688 23.2742 17.9688 23.9634C17.9688 24.6525 18.5282 25.2119 19.2173 25.2119Z"
                                            fill="#16B274" />
                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                            d="M19.2173 32.7002H47.9313C48.6204 32.7002 49.1798 32.1408 49.1798 31.4517C49.1798 30.7625 48.6204 30.2031 47.9313 30.2031H19.2173C18.5282 30.2031 17.9688 30.7625 17.9688 31.4517C17.9688 32.1408 18.5282 32.7002 19.2173 32.7002Z"
                                            fill="#16B274" />
                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                            d="M39.9039 48.1459L44.8978 60.6303C45.0872 61.1044 45.5465 61.4151 46.0568 61.4151C46.5673 61.4151 47.0263 61.1044 47.216 60.6303L52.2099 48.1459C52.4657 47.5062 52.1541 46.779 51.5141 46.5231C50.8744 46.2672 50.1472 46.5788 49.8914 47.2185L46.0568 56.805L42.2221 47.2185C41.9663 46.5788 41.2391 46.2672 40.5994 46.5231C39.9597 46.779 39.6481 47.5062 39.9039 48.1459Z"
                                            fill="#D8DFE3" />
                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                            d="M20.5532 46.4336H19.8398C16.3923 46.4336 13.5977 49.2282 13.5977 52.6757V55.1728C13.5977 58.6203 16.3923 61.4149 19.8398 61.4149H20.5532C22.7803 61.4149 24.8028 60.1157 25.7286 58.0902C25.8577 57.808 25.8832 57.7546 25.9662 57.5707C26.2931 56.845 25.8985 56.1825 25.4299 55.947C24.6472 55.5536 23.9371 55.9895 23.6953 56.5325C23.6219 56.6973 23.4577 57.0523 23.4577 57.0523C22.9379 58.1888 21.8032 58.9181 20.5532 58.9181H19.8398C17.7711 58.9181 16.0944 57.2411 16.0944 55.1728V52.6757C16.0944 50.6074 17.7711 48.9304 19.8398 48.9304H20.5532C21.8032 48.9304 22.9379 49.6597 23.4577 50.7961C23.4577 50.7961 23.6099 51.1371 23.6953 51.316C24.0705 52.1022 24.9574 52.214 25.5183 51.8496C25.9123 51.5931 26.2844 50.9738 25.9662 50.2778C25.7286 49.7582 25.7286 49.7582 25.7286 49.7582C24.8028 47.7328 22.7803 46.4336 20.5532 46.4336Z"
                                            fill="#D8DFE3" />
                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                            d="M27.9551 57.0532V57.6719C27.9551 58.6646 28.3494 59.6168 29.0511 60.3185C29.7531 61.0206 30.705 61.4149 31.6977 61.4149H34.5118C35.5044 61.4149 36.4564 61.0206 37.1584 60.3185C37.8604 59.6168 38.2547 58.6646 38.2547 57.6719C38.2547 57.5042 38.2547 57.3376 38.2547 57.1752C38.2547 55.6438 37.3223 54.2667 35.9002 53.6977L31.3606 51.882C30.8117 51.6624 30.4519 51.1308 30.4519 50.5397V50.1789C30.4519 49.8478 30.5834 49.5302 30.8174 49.2962C31.0517 49.0619 31.369 48.9304 31.7001 48.9304H34.5094C34.8405 48.9304 35.1581 49.0619 35.3921 49.2962C35.6261 49.5302 35.7576 49.8478 35.7576 50.1789V50.7955C35.7576 51.1479 35.9134 51.4667 36.0701 51.6285C36.3137 51.9051 36.6637 52.0516 37.0062 52.0516C37.6138 52.0516 38.2547 51.5824 38.2547 50.7955V50.1789C38.2547 49.1857 37.8601 48.2328 37.1578 47.5305C36.4552 46.8282 35.5026 46.4336 34.5094 46.4336H31.7004C30.7068 46.4336 29.7543 46.8282 29.052 47.5305C28.3497 48.2328 27.9551 49.1857 27.9551 50.1789V50.5397C27.9551 52.1516 28.9367 53.6015 30.4333 54.2002L34.9729 56.0162C35.4469 56.2056 35.7576 56.6649 35.7576 57.1752V57.6719C35.7576 58.0024 35.6264 58.3194 35.3927 58.5531C35.1593 58.7869 34.8423 58.9181 34.5118 58.9181H31.6977C31.3672 58.9181 31.0505 58.7869 30.8168 58.5531C30.5831 58.3194 30.4519 58.0024 30.4519 57.6722C30.4519 57.6719 30.4519 57.6719 30.4519 57.0529C30.4519 56.2295 29.7807 55.7963 29.2252 55.7963C28.6984 55.7963 27.9551 56.2047 27.9551 57.0454V57.0532Z"
                                            fill="#D8DFE3" />
                                    </svg>
                                </div>
                                <div class="card-widget-content">
                                    <h4 class="text-16 fw-semibold mb-10 text-title">SCV formats, up to 50MB</h4>
                                    <x-common.tyne-badge :data="['new' => 'success']" :status="'Employee'" variant="success" />
                                </div>
                            </div>
                            <div class="bulk-card-footer">
                                <button type="submit" class="btn-primary-dashed radius-4 w-100 box-hover">Import <svg
                                        width="16" height="16" viewBox="0 0 16 16" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M13.3309 5.88054C12.5914 2.93468 9.60389 1.14606 6.65803 1.88554C4.3559 2.46345 2.6843 4.45314 2.51207 6.82041C0.87747 7.08997 -0.229127 8.63359 0.0404372 10.2682C0.280067 11.7214 1.53924 12.7856 3.01202 12.7798H5.51174V11.7799H3.01202C1.90758 11.7799 1.01224 10.8845 1.01224 9.78009C1.01224 8.67564 1.90758 7.7803 3.01202 7.7803C3.28814 7.7803 3.51196 7.55648 3.51196 7.28036C3.50946 4.79535 5.52196 2.77882 8.00697 2.77635C10.1581 2.7742 12.0099 4.29497 12.426 6.40545C12.4671 6.61618 12.6381 6.77716 12.8509 6.80541C14.2177 7.00005 15.1679 8.26581 14.9733 9.63257C14.7985 10.8599 13.7507 11.7737 12.511 11.7799H10.5112V12.7798H12.511C14.4438 12.7739 16.0058 11.2023 16 9.26955C15.9951 7.66066 14.8938 6.26234 13.3309 5.88054Z"
                                            fill="currentColor" />
                                        <path
                                            d="M7.65603 7.92664L5.65625 9.92642L6.36117 10.6313L7.51105 9.48647V14.2809H8.51094V9.48647L9.65581 10.6313L10.3607 9.92642L8.36095 7.92664C8.16594 7.73279 7.85104 7.73279 7.65603 7.92664Z"
                                            fill="currentColor" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    @endfor
                </div>
            </div>
        </div>
    </x-container>
    <x-container cardClass="mb-40" title="Recent Import">
        <div class="recent-import-list">
            <div class="recent-import-item d-flex align-items-center justify-content-between gap-16 flex-wrap">
                <div class="flex-fill">
                    <h4 class="text-14 font-semibold text-title text-capitalize">User Import</h4>
                    <div class="d-flex gap-8 mb-16">
                        <x-common.tyne-badge :data="['new' => 'success']" :status="'Employee'" variant="success" class="radius-20" />
                        <span class="text-14 fw-semibold text-subtitle">1 Minute Age</span>
                    </div>
                    <div class="d-flex gap-8 flex-wrap">
                        <div class="d-flex align-items-center gap-6">
                            <div class="icon lh-1">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                        d="M2.99617 7.49222H2.62164C2.00112 7.49222 1.49805 7.99529 1.49805 8.61581V13.9841C1.49805 14.3978 1.83339 14.7331 2.24711 14.7331H13.7328C13.9314 14.7331 14.1219 14.6542 14.2625 14.5138C14.4029 14.3733 14.4818 14.1827 14.4818 13.9841C14.4818 13.0273 14.4818 10.9459 14.4818 9.98912C14.4818 9.79047 14.4029 9.5999 14.2625 9.45944C14.1219 9.31897 13.9314 9.24005 13.7328 9.24005H13.2334V4.49596C13.2334 4.44191 13.2159 4.38936 13.1834 4.34615L10.9362 1.3499C10.8891 1.28703 10.8151 1.25 10.7365 1.25H3.74524C3.54659 1.25 3.35602 1.32892 3.21556 1.46939C3.0751 1.60991 2.99617 1.80041 2.99617 1.99906V7.49222ZM13.7328 9.73941H2.62116C2.39746 9.73941 2.18071 9.67271 1.9974 9.55028V13.9841C1.9974 14.122 2.10922 14.2338 2.24711 14.2338H13.7328C13.799 14.2338 13.8625 14.2075 13.9093 14.1607C13.9561 14.1138 13.9824 14.0503 13.9824 13.9841V9.98912C13.9824 9.9229 13.9561 9.85938 13.9093 9.81252C13.8625 9.76572 13.799 9.73941 13.7328 9.73941ZM5.51063 10.4885H5.36795C4.67845 10.4885 4.11953 11.0474 4.11953 11.7369V12.2363C4.11953 12.9258 4.67845 13.4847 5.36795 13.4847H5.51063C5.95605 13.4847 6.36049 13.2249 6.54572 12.8198C6.57149 12.7634 6.57664 12.7527 6.59318 12.7159C6.65862 12.5708 6.57963 12.4383 6.48597 12.3912C6.32945 12.3125 6.18742 12.3997 6.13907 12.5082C6.12432 12.5412 6.09154 12.6122 6.09154 12.6122C5.98757 12.8395 5.76058 12.9854 5.51063 12.9854H5.36795C4.95423 12.9854 4.61889 12.65 4.61889 12.2363V11.7369C4.61889 11.3232 4.95423 10.9878 5.36795 10.9878H5.51063C5.76058 10.9878 5.98757 11.1337 6.09154 11.361C6.09154 11.361 6.12199 11.4292 6.13907 11.465C6.21409 11.6222 6.39147 11.6445 6.50359 11.5717C6.58245 11.5204 6.65682 11.3965 6.59318 11.2573C6.54572 11.1534 6.54572 11.1534 6.54572 11.1534C6.36049 10.7483 5.95605 10.4885 5.51063 10.4885ZM9.38107 10.8309L10.3798 13.3278C10.4177 13.4226 10.5096 13.4847 10.6116 13.4847C10.7137 13.4847 10.8056 13.4226 10.8435 13.3278L11.8423 10.8309C11.8934 10.7029 11.8311 10.5575 11.7031 10.5063C11.5752 10.4552 11.4297 10.5175 11.3786 10.6454L10.6116 12.5627L9.84471 10.6454C9.79354 10.5175 9.6481 10.4552 9.52016 10.5063C9.39222 10.5575 9.3299 10.7029 9.38107 10.8309ZM6.9912 12.6124V12.7361C6.9912 12.9347 7.07006 13.1251 7.21041 13.2655C7.35081 13.4059 7.54119 13.4847 7.73972 13.4847H8.30254C8.50107 13.4847 8.69145 13.4059 8.83186 13.2655C8.97226 13.1251 9.05113 12.9347 9.05113 12.7361C9.05113 12.7026 9.05113 12.6693 9.05113 12.6368C9.05113 12.3305 8.86464 12.0551 8.58023 11.9413L7.67231 11.5782C7.56253 11.5342 7.49056 11.4279 7.49056 11.3097V11.2375C7.49056 11.1713 7.51686 11.1078 7.56366 11.061C7.61053 11.0141 7.67399 10.9878 7.7402 10.9878H8.30206C8.36828 10.9878 8.4318 11.0141 8.4786 11.061C8.5254 11.1078 8.55171 11.1713 8.55171 11.2375V11.3609C8.55171 11.4313 8.58287 11.4951 8.61421 11.5275C8.66293 11.5828 8.73292 11.6121 8.80142 11.6121C8.92295 11.6121 9.05113 11.5182 9.05113 11.3609V11.2375C9.05113 11.0389 8.9722 10.8483 8.83174 10.7079C8.69122 10.5674 8.50071 10.4885 8.30206 10.4885H7.74026C7.54155 10.4885 7.35105 10.5674 7.21059 10.7079C7.07012 10.8483 6.9912 11.0389 6.9912 11.2375V11.3097C6.9912 11.6321 7.18751 11.9221 7.48684 12.0418L8.39477 12.405C8.48957 12.4429 8.55171 12.5347 8.55171 12.6368V12.7361C8.55171 12.8022 8.52546 12.8656 8.47872 12.9124C8.43204 12.9591 8.36864 12.9854 8.30254 12.9854H7.73972C7.67363 12.9854 7.61029 12.9591 7.56354 12.9124C7.5168 12.8656 7.49056 12.8022 7.49056 12.7362C7.49056 12.7362 7.49056 12.7361 7.49056 12.6123C7.49056 12.4477 7.35632 12.361 7.24522 12.361C7.13987 12.361 6.9912 12.4427 6.9912 12.6108V12.6124ZM10.4868 1.74942H3.74524C3.67902 1.74942 3.6155 1.77572 3.5687 1.82252C3.52184 1.86933 3.49553 1.93285 3.49553 1.99906V9.24005H12.734V4.74567H11.2359C10.8222 4.74567 10.4868 4.41027 10.4868 3.99661V1.74942ZM2.99617 7.99157H2.62164C2.27689 7.99157 1.9974 8.27106 1.9974 8.61581V8.61629C1.9974 8.78175 2.06314 8.94037 2.18011 9.05734C2.29709 9.17432 2.45571 9.24005 2.62116 9.24005H2.99617V7.99157ZM5.24337 7.74193H10.9862C11.124 7.74193 11.2359 7.63005 11.2359 7.49222C11.2359 7.35439 11.124 7.24251 10.9862 7.24251H5.24337C5.10554 7.24251 4.99366 7.35439 4.99366 7.49222C4.99366 7.63005 5.10554 7.74193 5.24337 7.74193ZM5.24337 6.2438H10.9862C11.124 6.2438 11.2359 6.13192 11.2359 5.99409C11.2359 5.85626 11.124 5.74438 10.9862 5.74438H5.24337C5.10554 5.74438 4.99366 5.85626 4.99366 5.99409C4.99366 6.13192 5.10554 6.2438 5.24337 6.2438ZM5.24337 4.74567H8.48933C8.6271 4.74567 8.73898 4.63379 8.73898 4.49596C8.73898 4.35813 8.6271 4.24625 8.48933 4.24625H5.24337C5.10554 4.24625 4.99366 4.35813 4.99366 4.49596C4.99366 4.63379 5.10554 4.74567 5.24337 4.74567ZM10.9862 2.24877V3.99661C10.9862 4.13449 11.098 4.24625 11.2359 4.24625H12.4843L10.9862 2.24877Z"
                                        fill="black" />
                                </svg>
                            </div>
                            <p class="text-12 fw-normal mb-0 text-subtitle lh-1">
                                OxS566yTCqb5bKvAUE5V3C058S47JZGDTXFJXJQc.csv
                            </p>
                        </div>
                        <div class="d-flex align-items-center gap-6">
                            <div class="icon lh-1">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <g clip-path="url(#clip0_3239_58769)">
                                        <path
                                            d="M8.00065 14.6654C11.6827 14.6654 14.6673 11.6807 14.6673 7.9987C14.6673 4.3167 11.6827 1.33203 8.00065 1.33203C4.31865 1.33203 1.33398 4.3167 1.33398 7.9987C1.33398 11.6807 4.31865 14.6654 8.00065 14.6654Z"
                                            stroke="#545C66" stroke-miterlimit="10" />
                                        <path
                                            d="M5.99935 7.33333L7.66602 4H8.33268L9.99935 7.33333M5.33268 8.66667L4.66602 10L7.99935 12L11.3327 10L10.666 8.66667"
                                            stroke="#545C66" stroke-miterlimit="10" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_3239_58769">
                                            <rect width="16" height="16" fill="white" />
                                        </clipPath>
                                    </defs>
                                </svg>

                            </div>
                            <p class="text-12 fw-normal mb-0 text-subtitle lh-1">22.20KB</p>
                        </div>
                        <div class="d-flex align-items-center gap-6">
                            <div class="icon lh-1">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M8.66667 9.5286L10.0976 8.0976C10.2278 7.96747 10.4389 7.96747 10.5691 8.0976C10.6992 8.2278 10.6992 8.43887 10.5691 8.56907L8.56907 10.5691C8.43887 10.6992 8.2278 10.6992 8.0976 10.5691L6.09763 8.56907C5.96745 8.43887 5.96745 8.2278 6.09763 8.0976C6.22781 7.96747 6.43886 7.96747 6.56903 8.0976L8 9.5286V2.33333C8 2.14924 8.14927 2 8.33333 2C8.5174 2 8.66667 2.14924 8.66667 2.33333V9.5286ZM12.3333 4C12.1493 4 12 3.85076 12 3.66667C12 3.48257 12.1493 3.33333 12.3333 3.33333H13C13.9205 3.33333 14.6667 4.07953 14.6667 5V11.6667C14.6667 12.5871 13.9205 13.3333 13 13.3333H3.66667C2.74619 13.3333 2 12.5871 2 11.6667V5C2 4.07953 2.74619 3.33333 3.66667 3.33333H4.33333C4.51743 3.33333 4.66667 3.48257 4.66667 3.66667C4.66667 3.85076 4.51743 4 4.33333 4H3.66667C3.11438 4 2.66667 4.44771 2.66667 5V11.6667C2.66667 12.2189 3.11438 12.6667 3.66667 12.6667H13C13.5523 12.6667 14 12.2189 14 11.6667V5C14 4.44771 13.5523 4 13 4H12.3333Z"
                                        fill="black" />
                                </svg>

                            </div>
                            <p class="text-12 fw-normal mb-0 text-subtitle lh-1">2000 Imported</p>
                        </div>
                    </div>
                </div>
                <a href="{{ route('bulk-import.history') }}" class="btn-primary-outline d-flex align-items-center gap-6">
                    <x-common.icons name="eye" size="16" stroke-width="1.5" />
                    View Details
                </a>
            </div>
            <div class="recent-import-item d-flex align-items-center justify-content-between gap-16 flex-wrap">
                <div class="flex-fill">
                    <h4 class="text-14 font-semibold text-title text-capitalize">User Import</h4>
                    <div class="d-flex gap-8 mb-16">
                        <x-common.tyne-badge :data="['new' => 'success']" :status="'Employee'" variant="success" class="radius-20" />
                        <span class="text-14 fw-semibold text-subtitle">1 Minute Age</span>
                    </div>
                    <div class="d-flex gap-8 flex-wrap">
                        <div class="d-flex align-items-center gap-6">
                            <div class="icon lh-1">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                        d="M2.99617 7.49222H2.62164C2.00112 7.49222 1.49805 7.99529 1.49805 8.61581V13.9841C1.49805 14.3978 1.83339 14.7331 2.24711 14.7331H13.7328C13.9314 14.7331 14.1219 14.6542 14.2625 14.5138C14.4029 14.3733 14.4818 14.1827 14.4818 13.9841C14.4818 13.0273 14.4818 10.9459 14.4818 9.98912C14.4818 9.79047 14.4029 9.5999 14.2625 9.45944C14.1219 9.31897 13.9314 9.24005 13.7328 9.24005H13.2334V4.49596C13.2334 4.44191 13.2159 4.38936 13.1834 4.34615L10.9362 1.3499C10.8891 1.28703 10.8151 1.25 10.7365 1.25H3.74524C3.54659 1.25 3.35602 1.32892 3.21556 1.46939C3.0751 1.60991 2.99617 1.80041 2.99617 1.99906V7.49222ZM13.7328 9.73941H2.62116C2.39746 9.73941 2.18071 9.67271 1.9974 9.55028V13.9841C1.9974 14.122 2.10922 14.2338 2.24711 14.2338H13.7328C13.799 14.2338 13.8625 14.2075 13.9093 14.1607C13.9561 14.1138 13.9824 14.0503 13.9824 13.9841V9.98912C13.9824 9.9229 13.9561 9.85938 13.9093 9.81252C13.8625 9.76572 13.799 9.73941 13.7328 9.73941ZM5.51063 10.4885H5.36795C4.67845 10.4885 4.11953 11.0474 4.11953 11.7369V12.2363C4.11953 12.9258 4.67845 13.4847 5.36795 13.4847H5.51063C5.95605 13.4847 6.36049 13.2249 6.54572 12.8198C6.57149 12.7634 6.57664 12.7527 6.59318 12.7159C6.65862 12.5708 6.57963 12.4383 6.48597 12.3912C6.32945 12.3125 6.18742 12.3997 6.13907 12.5082C6.12432 12.5412 6.09154 12.6122 6.09154 12.6122C5.98757 12.8395 5.76058 12.9854 5.51063 12.9854H5.36795C4.95423 12.9854 4.61889 12.65 4.61889 12.2363V11.7369C4.61889 11.3232 4.95423 10.9878 5.36795 10.9878H5.51063C5.76058 10.9878 5.98757 11.1337 6.09154 11.361C6.09154 11.361 6.12199 11.4292 6.13907 11.465C6.21409 11.6222 6.39147 11.6445 6.50359 11.5717C6.58245 11.5204 6.65682 11.3965 6.59318 11.2573C6.54572 11.1534 6.54572 11.1534 6.54572 11.1534C6.36049 10.7483 5.95605 10.4885 5.51063 10.4885ZM9.38107 10.8309L10.3798 13.3278C10.4177 13.4226 10.5096 13.4847 10.6116 13.4847C10.7137 13.4847 10.8056 13.4226 10.8435 13.3278L11.8423 10.8309C11.8934 10.7029 11.8311 10.5575 11.7031 10.5063C11.5752 10.4552 11.4297 10.5175 11.3786 10.6454L10.6116 12.5627L9.84471 10.6454C9.79354 10.5175 9.6481 10.4552 9.52016 10.5063C9.39222 10.5575 9.3299 10.7029 9.38107 10.8309ZM6.9912 12.6124V12.7361C6.9912 12.9347 7.07006 13.1251 7.21041 13.2655C7.35081 13.4059 7.54119 13.4847 7.73972 13.4847H8.30254C8.50107 13.4847 8.69145 13.4059 8.83186 13.2655C8.97226 13.1251 9.05113 12.9347 9.05113 12.7361C9.05113 12.7026 9.05113 12.6693 9.05113 12.6368C9.05113 12.3305 8.86464 12.0551 8.58023 11.9413L7.67231 11.5782C7.56253 11.5342 7.49056 11.4279 7.49056 11.3097V11.2375C7.49056 11.1713 7.51686 11.1078 7.56366 11.061C7.61053 11.0141 7.67399 10.9878 7.7402 10.9878H8.30206C8.36828 10.9878 8.4318 11.0141 8.4786 11.061C8.5254 11.1078 8.55171 11.1713 8.55171 11.2375V11.3609C8.55171 11.4313 8.58287 11.4951 8.61421 11.5275C8.66293 11.5828 8.73292 11.6121 8.80142 11.6121C8.92295 11.6121 9.05113 11.5182 9.05113 11.3609V11.2375C9.05113 11.0389 8.9722 10.8483 8.83174 10.7079C8.69122 10.5674 8.50071 10.4885 8.30206 10.4885H7.74026C7.54155 10.4885 7.35105 10.5674 7.21059 10.7079C7.07012 10.8483 6.9912 11.0389 6.9912 11.2375V11.3097C6.9912 11.6321 7.18751 11.9221 7.48684 12.0418L8.39477 12.405C8.48957 12.4429 8.55171 12.5347 8.55171 12.6368V12.7361C8.55171 12.8022 8.52546 12.8656 8.47872 12.9124C8.43204 12.9591 8.36864 12.9854 8.30254 12.9854H7.73972C7.67363 12.9854 7.61029 12.9591 7.56354 12.9124C7.5168 12.8656 7.49056 12.8022 7.49056 12.7362C7.49056 12.7362 7.49056 12.7361 7.49056 12.6123C7.49056 12.4477 7.35632 12.361 7.24522 12.361C7.13987 12.361 6.9912 12.4427 6.9912 12.6108V12.6124ZM10.4868 1.74942H3.74524C3.67902 1.74942 3.6155 1.77572 3.5687 1.82252C3.52184 1.86933 3.49553 1.93285 3.49553 1.99906V9.24005H12.734V4.74567H11.2359C10.8222 4.74567 10.4868 4.41027 10.4868 3.99661V1.74942ZM2.99617 7.99157H2.62164C2.27689 7.99157 1.9974 8.27106 1.9974 8.61581V8.61629C1.9974 8.78175 2.06314 8.94037 2.18011 9.05734C2.29709 9.17432 2.45571 9.24005 2.62116 9.24005H2.99617V7.99157ZM5.24337 7.74193H10.9862C11.124 7.74193 11.2359 7.63005 11.2359 7.49222C11.2359 7.35439 11.124 7.24251 10.9862 7.24251H5.24337C5.10554 7.24251 4.99366 7.35439 4.99366 7.49222C4.99366 7.63005 5.10554 7.74193 5.24337 7.74193ZM5.24337 6.2438H10.9862C11.124 6.2438 11.2359 6.13192 11.2359 5.99409C11.2359 5.85626 11.124 5.74438 10.9862 5.74438H5.24337C5.10554 5.74438 4.99366 5.85626 4.99366 5.99409C4.99366 6.13192 5.10554 6.2438 5.24337 6.2438ZM5.24337 4.74567H8.48933C8.6271 4.74567 8.73898 4.63379 8.73898 4.49596C8.73898 4.35813 8.6271 4.24625 8.48933 4.24625H5.24337C5.10554 4.24625 4.99366 4.35813 4.99366 4.49596C4.99366 4.63379 5.10554 4.74567 5.24337 4.74567ZM10.9862 2.24877V3.99661C10.9862 4.13449 11.098 4.24625 11.2359 4.24625H12.4843L10.9862 2.24877Z"
                                        fill="black" />
                                </svg>
                            </div>
                            <p class="text-12 fw-normal mb-0 text-subtitle lh-1">
                                OxS566yTCqb5bKvAUE5V3C058S47JZGDTXFJXJQc.csv
                            </p>
                        </div>
                        <div class="d-flex align-items-center gap-6">
                            <div class="icon lh-1">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <g clip-path="url(#clip0_3239_58769)">
                                        <path
                                            d="M8.00065 14.6654C11.6827 14.6654 14.6673 11.6807 14.6673 7.9987C14.6673 4.3167 11.6827 1.33203 8.00065 1.33203C4.31865 1.33203 1.33398 4.3167 1.33398 7.9987C1.33398 11.6807 4.31865 14.6654 8.00065 14.6654Z"
                                            stroke="#545C66" stroke-miterlimit="10" />
                                        <path
                                            d="M5.99935 7.33333L7.66602 4H8.33268L9.99935 7.33333M5.33268 8.66667L4.66602 10L7.99935 12L11.3327 10L10.666 8.66667"
                                            stroke="#545C66" stroke-miterlimit="10" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_3239_58769">
                                            <rect width="16" height="16" fill="white" />
                                        </clipPath>
                                    </defs>
                                </svg>

                            </div>
                            <p class="text-12 fw-normal mb-0 text-subtitle lh-1">22.20KB</p>
                        </div>
                        <div class="d-flex align-items-center gap-6">
                            <div class="icon lh-1">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M8.66667 9.5286L10.0976 8.0976C10.2278 7.96747 10.4389 7.96747 10.5691 8.0976C10.6992 8.2278 10.6992 8.43887 10.5691 8.56907L8.56907 10.5691C8.43887 10.6992 8.2278 10.6992 8.0976 10.5691L6.09763 8.56907C5.96745 8.43887 5.96745 8.2278 6.09763 8.0976C6.22781 7.96747 6.43886 7.96747 6.56903 8.0976L8 9.5286V2.33333C8 2.14924 8.14927 2 8.33333 2C8.5174 2 8.66667 2.14924 8.66667 2.33333V9.5286ZM12.3333 4C12.1493 4 12 3.85076 12 3.66667C12 3.48257 12.1493 3.33333 12.3333 3.33333H13C13.9205 3.33333 14.6667 4.07953 14.6667 5V11.6667C14.6667 12.5871 13.9205 13.3333 13 13.3333H3.66667C2.74619 13.3333 2 12.5871 2 11.6667V5C2 4.07953 2.74619 3.33333 3.66667 3.33333H4.33333C4.51743 3.33333 4.66667 3.48257 4.66667 3.66667C4.66667 3.85076 4.51743 4 4.33333 4H3.66667C3.11438 4 2.66667 4.44771 2.66667 5V11.6667C2.66667 12.2189 3.11438 12.6667 3.66667 12.6667H13C13.5523 12.6667 14 12.2189 14 11.6667V5C14 4.44771 13.5523 4 13 4H12.3333Z"
                                        fill="black" />
                                </svg>

                            </div>
                            <p class="text-12 fw-normal mb-0 text-subtitle lh-1">2000 Imported</p>
                        </div>
                    </div>
                </div>
                <a href="{{ route('bulk-import.history') }}" class="btn-primary-outline d-flex align-items-center gap-6">
                    <x-common.icons name="eye" size="16" stroke-width="1.5" />
                    View Details
                </a>
            </div>
            <div class="recent-import-item d-flex align-items-center justify-content-between gap-16 flex-wrap">
                <div class="flex-fill">
                    <h4 class="text-14 font-semibold text-title text-capitalize">User Import</h4>
                    <div class="d-flex gap-8 mb-16">
                        <x-common.tyne-badge :data="['new' => 'success']" :status="'Employee'" variant="success" class="radius-20" />
                        <span class="text-14 fw-semibold text-subtitle">1 Minute Age</span>
                    </div>
                    <div class="d-flex gap-8 flex-wrap">
                        <div class="d-flex align-items-center gap-6">
                            <div class="icon lh-1">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                        d="M2.99617 7.49222H2.62164C2.00112 7.49222 1.49805 7.99529 1.49805 8.61581V13.9841C1.49805 14.3978 1.83339 14.7331 2.24711 14.7331H13.7328C13.9314 14.7331 14.1219 14.6542 14.2625 14.5138C14.4029 14.3733 14.4818 14.1827 14.4818 13.9841C14.4818 13.0273 14.4818 10.9459 14.4818 9.98912C14.4818 9.79047 14.4029 9.5999 14.2625 9.45944C14.1219 9.31897 13.9314 9.24005 13.7328 9.24005H13.2334V4.49596C13.2334 4.44191 13.2159 4.38936 13.1834 4.34615L10.9362 1.3499C10.8891 1.28703 10.8151 1.25 10.7365 1.25H3.74524C3.54659 1.25 3.35602 1.32892 3.21556 1.46939C3.0751 1.60991 2.99617 1.80041 2.99617 1.99906V7.49222ZM13.7328 9.73941H2.62116C2.39746 9.73941 2.18071 9.67271 1.9974 9.55028V13.9841C1.9974 14.122 2.10922 14.2338 2.24711 14.2338H13.7328C13.799 14.2338 13.8625 14.2075 13.9093 14.1607C13.9561 14.1138 13.9824 14.0503 13.9824 13.9841V9.98912C13.9824 9.9229 13.9561 9.85938 13.9093 9.81252C13.8625 9.76572 13.799 9.73941 13.7328 9.73941ZM5.51063 10.4885H5.36795C4.67845 10.4885 4.11953 11.0474 4.11953 11.7369V12.2363C4.11953 12.9258 4.67845 13.4847 5.36795 13.4847H5.51063C5.95605 13.4847 6.36049 13.2249 6.54572 12.8198C6.57149 12.7634 6.57664 12.7527 6.59318 12.7159C6.65862 12.5708 6.57963 12.4383 6.48597 12.3912C6.32945 12.3125 6.18742 12.3997 6.13907 12.5082C6.12432 12.5412 6.09154 12.6122 6.09154 12.6122C5.98757 12.8395 5.76058 12.9854 5.51063 12.9854H5.36795C4.95423 12.9854 4.61889 12.65 4.61889 12.2363V11.7369C4.61889 11.3232 4.95423 10.9878 5.36795 10.9878H5.51063C5.76058 10.9878 5.98757 11.1337 6.09154 11.361C6.09154 11.361 6.12199 11.4292 6.13907 11.465C6.21409 11.6222 6.39147 11.6445 6.50359 11.5717C6.58245 11.5204 6.65682 11.3965 6.59318 11.2573C6.54572 11.1534 6.54572 11.1534 6.54572 11.1534C6.36049 10.7483 5.95605 10.4885 5.51063 10.4885ZM9.38107 10.8309L10.3798 13.3278C10.4177 13.4226 10.5096 13.4847 10.6116 13.4847C10.7137 13.4847 10.8056 13.4226 10.8435 13.3278L11.8423 10.8309C11.8934 10.7029 11.8311 10.5575 11.7031 10.5063C11.5752 10.4552 11.4297 10.5175 11.3786 10.6454L10.6116 12.5627L9.84471 10.6454C9.79354 10.5175 9.6481 10.4552 9.52016 10.5063C9.39222 10.5575 9.3299 10.7029 9.38107 10.8309ZM6.9912 12.6124V12.7361C6.9912 12.9347 7.07006 13.1251 7.21041 13.2655C7.35081 13.4059 7.54119 13.4847 7.73972 13.4847H8.30254C8.50107 13.4847 8.69145 13.4059 8.83186 13.2655C8.97226 13.1251 9.05113 12.9347 9.05113 12.7361C9.05113 12.7026 9.05113 12.6693 9.05113 12.6368C9.05113 12.3305 8.86464 12.0551 8.58023 11.9413L7.67231 11.5782C7.56253 11.5342 7.49056 11.4279 7.49056 11.3097V11.2375C7.49056 11.1713 7.51686 11.1078 7.56366 11.061C7.61053 11.0141 7.67399 10.9878 7.7402 10.9878H8.30206C8.36828 10.9878 8.4318 11.0141 8.4786 11.061C8.5254 11.1078 8.55171 11.1713 8.55171 11.2375V11.3609C8.55171 11.4313 8.58287 11.4951 8.61421 11.5275C8.66293 11.5828 8.73292 11.6121 8.80142 11.6121C8.92295 11.6121 9.05113 11.5182 9.05113 11.3609V11.2375C9.05113 11.0389 8.9722 10.8483 8.83174 10.7079C8.69122 10.5674 8.50071 10.4885 8.30206 10.4885H7.74026C7.54155 10.4885 7.35105 10.5674 7.21059 10.7079C7.07012 10.8483 6.9912 11.0389 6.9912 11.2375V11.3097C6.9912 11.6321 7.18751 11.9221 7.48684 12.0418L8.39477 12.405C8.48957 12.4429 8.55171 12.5347 8.55171 12.6368V12.7361C8.55171 12.8022 8.52546 12.8656 8.47872 12.9124C8.43204 12.9591 8.36864 12.9854 8.30254 12.9854H7.73972C7.67363 12.9854 7.61029 12.9591 7.56354 12.9124C7.5168 12.8656 7.49056 12.8022 7.49056 12.7362C7.49056 12.7362 7.49056 12.7361 7.49056 12.6123C7.49056 12.4477 7.35632 12.361 7.24522 12.361C7.13987 12.361 6.9912 12.4427 6.9912 12.6108V12.6124ZM10.4868 1.74942H3.74524C3.67902 1.74942 3.6155 1.77572 3.5687 1.82252C3.52184 1.86933 3.49553 1.93285 3.49553 1.99906V9.24005H12.734V4.74567H11.2359C10.8222 4.74567 10.4868 4.41027 10.4868 3.99661V1.74942ZM2.99617 7.99157H2.62164C2.27689 7.99157 1.9974 8.27106 1.9974 8.61581V8.61629C1.9974 8.78175 2.06314 8.94037 2.18011 9.05734C2.29709 9.17432 2.45571 9.24005 2.62116 9.24005H2.99617V7.99157ZM5.24337 7.74193H10.9862C11.124 7.74193 11.2359 7.63005 11.2359 7.49222C11.2359 7.35439 11.124 7.24251 10.9862 7.24251H5.24337C5.10554 7.24251 4.99366 7.35439 4.99366 7.49222C4.99366 7.63005 5.10554 7.74193 5.24337 7.74193ZM5.24337 6.2438H10.9862C11.124 6.2438 11.2359 6.13192 11.2359 5.99409C11.2359 5.85626 11.124 5.74438 10.9862 5.74438H5.24337C5.10554 5.74438 4.99366 5.85626 4.99366 5.99409C4.99366 6.13192 5.10554 6.2438 5.24337 6.2438ZM5.24337 4.74567H8.48933C8.6271 4.74567 8.73898 4.63379 8.73898 4.49596C8.73898 4.35813 8.6271 4.24625 8.48933 4.24625H5.24337C5.10554 4.24625 4.99366 4.35813 4.99366 4.49596C4.99366 4.63379 5.10554 4.74567 5.24337 4.74567ZM10.9862 2.24877V3.99661C10.9862 4.13449 11.098 4.24625 11.2359 4.24625H12.4843L10.9862 2.24877Z"
                                        fill="black" />
                                </svg>
                            </div>
                            <p class="text-12 fw-normal mb-0 text-subtitle lh-1">
                                OxS566yTCqb5bKvAUE5V3C058S47JZGDTXFJXJQc.csv
                            </p>
                        </div>
                        <div class="d-flex align-items-center gap-6">
                            <div class="icon lh-1">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <g clip-path="url(#clip0_3239_58769)">
                                        <path
                                            d="M8.00065 14.6654C11.6827 14.6654 14.6673 11.6807 14.6673 7.9987C14.6673 4.3167 11.6827 1.33203 8.00065 1.33203C4.31865 1.33203 1.33398 4.3167 1.33398 7.9987C1.33398 11.6807 4.31865 14.6654 8.00065 14.6654Z"
                                            stroke="#545C66" stroke-miterlimit="10" />
                                        <path
                                            d="M5.99935 7.33333L7.66602 4H8.33268L9.99935 7.33333M5.33268 8.66667L4.66602 10L7.99935 12L11.3327 10L10.666 8.66667"
                                            stroke="#545C66" stroke-miterlimit="10" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_3239_58769">
                                            <rect width="16" height="16" fill="white" />
                                        </clipPath>
                                    </defs>
                                </svg>

                            </div>
                            <p class="text-12 fw-normal mb-0 text-subtitle lh-1">22.20KB</p>
                        </div>
                        <div class="d-flex align-items-center gap-6">
                            <div class="icon lh-1">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M8.66667 9.5286L10.0976 8.0976C10.2278 7.96747 10.4389 7.96747 10.5691 8.0976C10.6992 8.2278 10.6992 8.43887 10.5691 8.56907L8.56907 10.5691C8.43887 10.6992 8.2278 10.6992 8.0976 10.5691L6.09763 8.56907C5.96745 8.43887 5.96745 8.2278 6.09763 8.0976C6.22781 7.96747 6.43886 7.96747 6.56903 8.0976L8 9.5286V2.33333C8 2.14924 8.14927 2 8.33333 2C8.5174 2 8.66667 2.14924 8.66667 2.33333V9.5286ZM12.3333 4C12.1493 4 12 3.85076 12 3.66667C12 3.48257 12.1493 3.33333 12.3333 3.33333H13C13.9205 3.33333 14.6667 4.07953 14.6667 5V11.6667C14.6667 12.5871 13.9205 13.3333 13 13.3333H3.66667C2.74619 13.3333 2 12.5871 2 11.6667V5C2 4.07953 2.74619 3.33333 3.66667 3.33333H4.33333C4.51743 3.33333 4.66667 3.48257 4.66667 3.66667C4.66667 3.85076 4.51743 4 4.33333 4H3.66667C3.11438 4 2.66667 4.44771 2.66667 5V11.6667C2.66667 12.2189 3.11438 12.6667 3.66667 12.6667H13C13.5523 12.6667 14 12.2189 14 11.6667V5C14 4.44771 13.5523 4 13 4H12.3333Z"
                                        fill="black" />
                                </svg>

                            </div>
                            <p class="text-12 fw-normal mb-0 text-subtitle lh-1">2000 Imported</p>
                        </div>
                    </div>
                </div>
                <a href="{{ route('bulk-import.history') }}" class="btn-primary-outline d-flex align-items-center gap-6">
                    <x-common.icons name="eye" size="16" stroke-width="1.5" />
                    View Details
                </a>
            </div>
            <div class="recent-import-item d-flex align-items-center justify-content-between gap-16 flex-wrap">
                <div class="flex-fill">
                    <h4 class="text-14 font-semibold text-title text-capitalize">User Import</h4>
                    <div class="d-flex gap-8 mb-16">
                        <x-common.tyne-badge :data="['new' => 'success']" :status="'Employee'" variant="success" class="radius-20" />
                        <span class="text-14 fw-semibold text-subtitle">1 Minute Age</span>
                    </div>
                    <div class="d-flex gap-8 flex-wrap">
                        <div class="d-flex align-items-center gap-6">
                            <div class="icon lh-1">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                        d="M2.99617 7.49222H2.62164C2.00112 7.49222 1.49805 7.99529 1.49805 8.61581V13.9841C1.49805 14.3978 1.83339 14.7331 2.24711 14.7331H13.7328C13.9314 14.7331 14.1219 14.6542 14.2625 14.5138C14.4029 14.3733 14.4818 14.1827 14.4818 13.9841C14.4818 13.0273 14.4818 10.9459 14.4818 9.98912C14.4818 9.79047 14.4029 9.5999 14.2625 9.45944C14.1219 9.31897 13.9314 9.24005 13.7328 9.24005H13.2334V4.49596C13.2334 4.44191 13.2159 4.38936 13.1834 4.34615L10.9362 1.3499C10.8891 1.28703 10.8151 1.25 10.7365 1.25H3.74524C3.54659 1.25 3.35602 1.32892 3.21556 1.46939C3.0751 1.60991 2.99617 1.80041 2.99617 1.99906V7.49222ZM13.7328 9.73941H2.62116C2.39746 9.73941 2.18071 9.67271 1.9974 9.55028V13.9841C1.9974 14.122 2.10922 14.2338 2.24711 14.2338H13.7328C13.799 14.2338 13.8625 14.2075 13.9093 14.1607C13.9561 14.1138 13.9824 14.0503 13.9824 13.9841V9.98912C13.9824 9.9229 13.9561 9.85938 13.9093 9.81252C13.8625 9.76572 13.799 9.73941 13.7328 9.73941ZM5.51063 10.4885H5.36795C4.67845 10.4885 4.11953 11.0474 4.11953 11.7369V12.2363C4.11953 12.9258 4.67845 13.4847 5.36795 13.4847H5.51063C5.95605 13.4847 6.36049 13.2249 6.54572 12.8198C6.57149 12.7634 6.57664 12.7527 6.59318 12.7159C6.65862 12.5708 6.57963 12.4383 6.48597 12.3912C6.32945 12.3125 6.18742 12.3997 6.13907 12.5082C6.12432 12.5412 6.09154 12.6122 6.09154 12.6122C5.98757 12.8395 5.76058 12.9854 5.51063 12.9854H5.36795C4.95423 12.9854 4.61889 12.65 4.61889 12.2363V11.7369C4.61889 11.3232 4.95423 10.9878 5.36795 10.9878H5.51063C5.76058 10.9878 5.98757 11.1337 6.09154 11.361C6.09154 11.361 6.12199 11.4292 6.13907 11.465C6.21409 11.6222 6.39147 11.6445 6.50359 11.5717C6.58245 11.5204 6.65682 11.3965 6.59318 11.2573C6.54572 11.1534 6.54572 11.1534 6.54572 11.1534C6.36049 10.7483 5.95605 10.4885 5.51063 10.4885ZM9.38107 10.8309L10.3798 13.3278C10.4177 13.4226 10.5096 13.4847 10.6116 13.4847C10.7137 13.4847 10.8056 13.4226 10.8435 13.3278L11.8423 10.8309C11.8934 10.7029 11.8311 10.5575 11.7031 10.5063C11.5752 10.4552 11.4297 10.5175 11.3786 10.6454L10.6116 12.5627L9.84471 10.6454C9.79354 10.5175 9.6481 10.4552 9.52016 10.5063C9.39222 10.5575 9.3299 10.7029 9.38107 10.8309ZM6.9912 12.6124V12.7361C6.9912 12.9347 7.07006 13.1251 7.21041 13.2655C7.35081 13.4059 7.54119 13.4847 7.73972 13.4847H8.30254C8.50107 13.4847 8.69145 13.4059 8.83186 13.2655C8.97226 13.1251 9.05113 12.9347 9.05113 12.7361C9.05113 12.7026 9.05113 12.6693 9.05113 12.6368C9.05113 12.3305 8.86464 12.0551 8.58023 11.9413L7.67231 11.5782C7.56253 11.5342 7.49056 11.4279 7.49056 11.3097V11.2375C7.49056 11.1713 7.51686 11.1078 7.56366 11.061C7.61053 11.0141 7.67399 10.9878 7.7402 10.9878H8.30206C8.36828 10.9878 8.4318 11.0141 8.4786 11.061C8.5254 11.1078 8.55171 11.1713 8.55171 11.2375V11.3609C8.55171 11.4313 8.58287 11.4951 8.61421 11.5275C8.66293 11.5828 8.73292 11.6121 8.80142 11.6121C8.92295 11.6121 9.05113 11.5182 9.05113 11.3609V11.2375C9.05113 11.0389 8.9722 10.8483 8.83174 10.7079C8.69122 10.5674 8.50071 10.4885 8.30206 10.4885H7.74026C7.54155 10.4885 7.35105 10.5674 7.21059 10.7079C7.07012 10.8483 6.9912 11.0389 6.9912 11.2375V11.3097C6.9912 11.6321 7.18751 11.9221 7.48684 12.0418L8.39477 12.405C8.48957 12.4429 8.55171 12.5347 8.55171 12.6368V12.7361C8.55171 12.8022 8.52546 12.8656 8.47872 12.9124C8.43204 12.9591 8.36864 12.9854 8.30254 12.9854H7.73972C7.67363 12.9854 7.61029 12.9591 7.56354 12.9124C7.5168 12.8656 7.49056 12.8022 7.49056 12.7362C7.49056 12.7362 7.49056 12.7361 7.49056 12.6123C7.49056 12.4477 7.35632 12.361 7.24522 12.361C7.13987 12.361 6.9912 12.4427 6.9912 12.6108V12.6124ZM10.4868 1.74942H3.74524C3.67902 1.74942 3.6155 1.77572 3.5687 1.82252C3.52184 1.86933 3.49553 1.93285 3.49553 1.99906V9.24005H12.734V4.74567H11.2359C10.8222 4.74567 10.4868 4.41027 10.4868 3.99661V1.74942ZM2.99617 7.99157H2.62164C2.27689 7.99157 1.9974 8.27106 1.9974 8.61581V8.61629C1.9974 8.78175 2.06314 8.94037 2.18011 9.05734C2.29709 9.17432 2.45571 9.24005 2.62116 9.24005H2.99617V7.99157ZM5.24337 7.74193H10.9862C11.124 7.74193 11.2359 7.63005 11.2359 7.49222C11.2359 7.35439 11.124 7.24251 10.9862 7.24251H5.24337C5.10554 7.24251 4.99366 7.35439 4.99366 7.49222C4.99366 7.63005 5.10554 7.74193 5.24337 7.74193ZM5.24337 6.2438H10.9862C11.124 6.2438 11.2359 6.13192 11.2359 5.99409C11.2359 5.85626 11.124 5.74438 10.9862 5.74438H5.24337C5.10554 5.74438 4.99366 5.85626 4.99366 5.99409C4.99366 6.13192 5.10554 6.2438 5.24337 6.2438ZM5.24337 4.74567H8.48933C8.6271 4.74567 8.73898 4.63379 8.73898 4.49596C8.73898 4.35813 8.6271 4.24625 8.48933 4.24625H5.24337C5.10554 4.24625 4.99366 4.35813 4.99366 4.49596C4.99366 4.63379 5.10554 4.74567 5.24337 4.74567ZM10.9862 2.24877V3.99661C10.9862 4.13449 11.098 4.24625 11.2359 4.24625H12.4843L10.9862 2.24877Z"
                                        fill="black" />
                                </svg>
                            </div>
                            <p class="text-12 fw-normal mb-0 text-subtitle lh-1">
                                OxS566yTCqb5bKvAUE5V3C058S47JZGDTXFJXJQc.csv
                            </p>
                        </div>
                        <div class="d-flex align-items-center gap-6">
                            <div class="icon lh-1">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <g clip-path="url(#clip0_3239_58769)">
                                        <path
                                            d="M8.00065 14.6654C11.6827 14.6654 14.6673 11.6807 14.6673 7.9987C14.6673 4.3167 11.6827 1.33203 8.00065 1.33203C4.31865 1.33203 1.33398 4.3167 1.33398 7.9987C1.33398 11.6807 4.31865 14.6654 8.00065 14.6654Z"
                                            stroke="#545C66" stroke-miterlimit="10" />
                                        <path
                                            d="M5.99935 7.33333L7.66602 4H8.33268L9.99935 7.33333M5.33268 8.66667L4.66602 10L7.99935 12L11.3327 10L10.666 8.66667"
                                            stroke="#545C66" stroke-miterlimit="10" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_3239_58769">
                                            <rect width="16" height="16" fill="white" />
                                        </clipPath>
                                    </defs>
                                </svg>

                            </div>
                            <p class="text-12 fw-normal mb-0 text-subtitle lh-1">22.20KB</p>
                        </div>
                        <div class="d-flex align-items-center gap-6">
                            <div class="icon lh-1">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M8.66667 9.5286L10.0976 8.0976C10.2278 7.96747 10.4389 7.96747 10.5691 8.0976C10.6992 8.2278 10.6992 8.43887 10.5691 8.56907L8.56907 10.5691C8.43887 10.6992 8.2278 10.6992 8.0976 10.5691L6.09763 8.56907C5.96745 8.43887 5.96745 8.2278 6.09763 8.0976C6.22781 7.96747 6.43886 7.96747 6.56903 8.0976L8 9.5286V2.33333C8 2.14924 8.14927 2 8.33333 2C8.5174 2 8.66667 2.14924 8.66667 2.33333V9.5286ZM12.3333 4C12.1493 4 12 3.85076 12 3.66667C12 3.48257 12.1493 3.33333 12.3333 3.33333H13C13.9205 3.33333 14.6667 4.07953 14.6667 5V11.6667C14.6667 12.5871 13.9205 13.3333 13 13.3333H3.66667C2.74619 13.3333 2 12.5871 2 11.6667V5C2 4.07953 2.74619 3.33333 3.66667 3.33333H4.33333C4.51743 3.33333 4.66667 3.48257 4.66667 3.66667C4.66667 3.85076 4.51743 4 4.33333 4H3.66667C3.11438 4 2.66667 4.44771 2.66667 5V11.6667C2.66667 12.2189 3.11438 12.6667 3.66667 12.6667H13C13.5523 12.6667 14 12.2189 14 11.6667V5C14 4.44771 13.5523 4 13 4H12.3333Z"
                                        fill="black" />
                                </svg>

                            </div>
                            <p class="text-12 fw-normal mb-0 text-subtitle lh-1">2000 Imported</p>
                        </div>
                    </div>
                </div>
                <a href="{{ route('bulk-import.history') }}" class="btn-primary-outline d-flex align-items-center gap-6">
                    <x-common.icons name="eye" size="16" stroke-width="1.5" />
                    View Details
                </a>
            </div>
        </div>
    </x-container>

    <div class="ot-card">
        <h3 class="card-title d-flex align-items-center gap-10 mb-25">
            <x-common.icons name="upload" class="text-primary" size="24" stroke-width="1.5" />
            <span>Bulk Import</span>
        </h3>
        <p class="text-subtitle mb-30">Select the type of data you want to import by clicking on one of the cards below.
            Then
            upload your CSV file for processing.</p>

        {{-- Import Options Cards --}}
        <div class="row g-y-24 mb-4">
            @foreach ($models as $model)
                <div class="col-xl-3 col-lg-4 col-md-6">
                    <div class="ot-card border radius-8 p-24 d-flex flex-column import-card h-100 cursor-pointer"
                        data-model="{{ $model }}">
                        <div class="d-flex align-items-center gap-15 mb-16">
                            <div
                                class="icon border rounded-circle wh-48px d-flex align-items-center justify-content-center bg-primary-soft">
                                @php
                                    $iconMap = [
                                        'employee' => 'profile-2user',
                                        'users' => 'users',
                                        'user' => 'user-profile',
                                        'department' => 'shapes',
                                        'designation' => 'personalcard',
                                        'attendance' => 'time',
                                        'leave' => 'export',
                                        'holiday' => 'calendar',
                                        'expense' => 'dollar-circle',
                                        'salary' => 'wallet_check',
                                        'payroll' => 'money-square',
                                        'branch' => 'bank',
                                        'shift' => 'clock',
                                        'role' => 'shield',
                                        'permission' => 'lock',
                                    ];
                                    $modelLower = strtolower($model);
                                    $iconName = $iconMap[$model] ?? 'document-copy'; // default icon
                                @endphp
                                <x-common.icons name="{{ $iconName }}" class="text-primary" size="20"
                                    stroke-width="1.5" />
                            </div>
                            <div class="flex-fill">
                                @php
                                    $displayNames = [
                                        'employee' => 'Employee',
                                        'user' => 'User',
                                        'department' => 'Department',
                                        'designation' => 'Designation',
                                        'attendance' => 'Attendance',
                                        'leave' => 'Leave',
                                        'holiday' => 'Holiday',
                                        'expense' => 'Expense',
                                        'salary' => 'Salary',
                                        'payroll' => 'Payroll',
                                        'branch' => 'Branch',
                                        'shift' => 'Shift',
                                        'role' => 'Role',
                                        'permission' => 'Permission',
                                    ];
                                    $displayName = $displayNames[$model] ?? ucwords(str_replace('_', ' ', $model));
                                @endphp
                                <h3 class="text-16 fw-semibold mb-2">{{ $displayName }}</h3>
                                <p class="text-14 text-subtitle mb-0">Import {{ strtolower($displayName) }} data</p>
                            </div>
                        </div>

                        <div class="border-top mt-auto pt-16">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-flex align-items-center gap-6">
                                    <x-common.icons name="upload" class="text-subtitle" size="16"
                                        stroke-width="1.5" />
                                    <span class="text-12 text-subtitle">CSV Import</span>
                                </div>
                                <div class="d-flex align-items-center gap-6">
                                    <x-common.icons name="arrow-right" class="text-primary" size="16"
                                        stroke-width="1.5" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        {{-- Recent Imports Section --}}
        <div class="row mt-40">
            <div class="col-12">
                <div class="d-flex align-items-center justify-content-between mb-24">
                    <h4 class="card-title mb-0">Recent Imports</h4>
                    <a href="{{ route('bulk-import.history') }}"
                        class="btn-primary-outline d-flex align-items-center gap-6">
                        <x-common.icons name="export" class="" size="16" stroke-width="1.5" />
                        View All History
                    </a>
                </div>

                @if ($recentImports->count() > 0)
                    <div class="row g-y-16">
                        @foreach ($recentImports as $import)
                            <div class="col-12">
                                <div class="ot-card border radius-8 p-20">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div class="d-flex align-items-center gap-15">
                                            <div
                                                class="icon border rounded-circle wh-48px d-flex align-items-center justify-content-center bg-{{ $import->status === 'completed' ? 'success' : ($import->status === 'failed' ? 'danger' : 'primary') }}-soft">
                                                @php
                                                    $statusIcon = match ($import->status) {
                                                        'completed' => 'check-circle',
                                                        'failed' => 'close-circle',
                                                        'processing' => 'loading',
                                                        'pending' => 'clock',
                                                        'cancelled' => 'cancel',
                                                        default => 'help',
                                                    };
                                                @endphp
                                                <x-common.icons name="{{ $statusIcon }}"
                                                    class="text-{{ $import->status === 'completed' ? 'success' : ($import->status === 'failed' ? 'danger' : 'primary') }}"
                                                    size="20" stroke-width="1.5" />
                                            </div>
                                            <div>
                                                <h6 class="mb-4">
                                                    {{ ucwords(str_replace('_', ' ', class_basename($import->model_class))) }}
                                                    Import</h6>
                                                <div class="d-flex align-items-center gap-12 mb-2">
                                                    @php
                                                        $badgeClass = match ($import->status) {
                                                            'completed' => 'badge-success',
                                                            'failed' => 'badge-danger',
                                                            'processing' => 'badge-info',
                                                            'pending' => 'badge-warning',
                                                            'cancelled' => 'badge-secondary',
                                                            default => 'badge-secondary',
                                                        };
                                                    @endphp
                                                    <span class="badge {{ $badgeClass }} text-12">
                                                        {{ ucfirst($import->status) }}
                                                    </span>
                                                    <span
                                                        class="text-12 text-subtitle">{{ $import->created_at->diffForHumans() }}</span>
                                                </div>
                                                <div class="d-flex align-items-center gap-12 text-12 text-subtitle">
                                                    <span>{{ $import->file_name }}</span>
                                                    <span>•</span>
                                                    <span>{{ round($import->file_size / 1024, 2) }} KB</span>
                                                    @if ($import->imported_rows)
                                                        <span>•</span>
                                                        <span>{{ $import->imported_rows }} imported</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                        <div class="d-flex align-items-center gap-8">
                                            <a href="{{ route('bulk-import.history.show', $import->id) }}"
                                                class="btn-secondary-outline btn-sm d-flex align-items-center gap-4">
                                                <x-common.icons name="eye" class="" size="14"
                                                    stroke-width="1.5" />
                                                View
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="ot-card bg-primary-soft border-left-primary p-20">
                        <div class="d-flex align-items-center gap-15">
                            <x-common.icons name="chart-analytics" class="text-primary" size="24"
                                stroke-width="1.5" />
                            <div>
                                <h5 class="mb-6">No recent imports found</h5>
                                <p class="text-subtitle mb-0">Start importing data by selecting one of the options above.
                                </p>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>

    {{-- Upload Modal --}}
    <div class="modal fade" id="uploadModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-header-style mb-3">
                    <h5 class="modal-title text-white" id="uploadModalLabel">
                        <x-common.icons name="upload" class="text-white me-2" size="20" stroke-width="1.5" />
                        Import <span id="selectedModelName"></span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                        <i class="las la-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form method="POST" action="{{ route('bulk-import.upload') }}" enctype="multipart/form-data"
                        id="importForm">
                        @csrf
                        <input type="hidden" name="model" id="selectedModel">

                        <div class="row g-y-16 bulk-import-modal-body">
                            {{-- Import Info Card --}}
                            <div class="col-12">
                                <div class="bulk-import-guideline">
                                    <h6 class="text-16 text-capitalize fw-semibold mb-8">Import Guidelines</h6>
                                    <ul class="text-14 fw-normal lh-base text-subtitle mb-0 ps-3">
                                        <li>Ensure your CSV file follows the required format</li>
                                        <li>Maximum file size: 10MB</li>
                                        <li>First row should contain column headers</li>
                                        <li>Download sample file if you need a template</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="bulk-import-file-upload">
                                    <div class="icon d-flex align-items-center justify-content-center">
                                        <svg width="70" height="70" viewBox="0 0 70 70" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M13.1089 40.4174C28.0381 43.0066 42.9675 42.9196 57.8966 40.4174V19.662C57.8966 19.4255 57.8201 19.1956 57.6782 19.0066L47.8468 5.89798C47.6404 5.62296 47.3166 5.46094 46.9729 5.46094H16.3861C15.517 5.46094 14.6833 5.80622 14.0687 6.42075C13.4542 7.03554 13.1089 7.86899 13.1089 8.73809V33.8631C13.1089 35.4854 12.7285 36.8605 12.7285 37.9567C12.7285 39.472 13.1089 40.4174 13.1089 40.4174Z"
                                                fill="#D8DFE3" />
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M6.55469 37.5859V61.173C6.55469 62.0422 6.89997 62.8759 7.5145 63.4904C8.12903 64.1049 8.96274 64.4502 9.83184 64.4502H60.0816C60.9507 64.4502 61.7842 64.1049 62.399 63.4904C63.0135 62.8759 63.3588 62.0422 63.3588 61.173C63.3588 56.9872 63.3588 47.8809 63.3588 43.6951C63.3588 42.826 63.0135 41.9923 62.399 41.3777C61.7842 40.7632 60.9507 40.4179 60.0816 40.4179H10.9243C9.53061 40.4179 6.55469 37.5859 6.55469 37.5859Z"
                                                fill="#1FB35B" />
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M9.83184 40.4176C8.83454 40.4176 8.10649 40.0131 7.57061 39.5149C7.06645 39.0462 6.55469 38.2201 6.55469 37.1404C6.55469 36.4475 6.89997 35.4376 7.5145 34.8231C8.12903 34.2086 8.96274 33.8633 9.83184 33.8633H13.109V40.4176C13.109 40.4176 13.3898 40.4176 9.83184 40.4176Z"
                                                fill="#198043" />
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M46.9727 5.46094V16.3849C46.9727 17.254 47.3179 18.0877 47.9325 18.7022C48.547 19.3167 49.3804 19.662 50.2498 19.662H57.8963C57.8963 19.4255 57.8198 19.1956 57.6779 19.0066L47.8465 5.89798C47.6401 5.62296 47.3164 5.46094 46.9727 5.46094Z"
                                                fill="#AFBDC7" />
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M22.9401 20.7553H37.1412C37.744 20.7553 38.2334 20.2658 38.2334 19.6628C38.2334 19.0598 37.744 18.5703 37.1412 18.5703H22.9401C22.3371 18.5703 21.8477 19.0598 21.8477 19.6628C21.8477 20.2658 22.3371 20.7553 22.9401 20.7553Z"
                                                fill="#1FB35B" />
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M22.9401 27.3099H48.0649C48.6679 27.3099 49.1574 26.8205 49.1574 26.2175C49.1574 25.6145 48.6679 25.125 48.0649 25.125H22.9401C22.3371 25.125 21.8477 25.6145 21.8477 26.2175C21.8477 26.8205 22.3371 27.3099 22.9401 27.3099Z"
                                                fill="#1FB35B" />
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M22.9401 33.8646H48.0649C48.6679 33.8646 49.1574 33.3752 49.1574 32.7722C49.1574 32.1692 48.6679 31.6797 48.0649 31.6797H22.9401C22.3371 31.6797 21.8477 32.1692 21.8477 32.7722C21.8477 33.3752 22.3371 33.8646 22.9401 33.8646Z"
                                                fill="#1FB35B" />
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M41.0412 47.3771L45.4108 58.3011C45.5765 58.7158 45.9784 58.9877 46.4249 58.9877C46.8716 58.9877 47.2733 58.7158 47.4392 58.3011L51.8089 47.3771C52.0328 46.8174 51.7601 46.1811 51.2001 45.9572C50.6404 45.7333 50.0041 46.006 49.7802 46.5657L46.4249 54.9539L43.0696 46.5657C42.8457 46.006 42.2094 45.7333 41.6497 45.9572C41.09 46.1811 40.8173 46.8174 41.0412 47.3771Z"
                                                fill="#D8DFE3" />
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M24.1095 45.8789H23.4853C20.4687 45.8789 18.0234 48.3242 18.0234 51.3407V53.5257C18.0234 56.5422 20.4687 58.9875 23.4853 58.9875H24.1095C26.0582 58.9875 27.8279 57.8507 28.638 56.0785C28.751 55.8315 28.7733 55.7848 28.8459 55.6238C29.1319 54.9889 28.7867 54.4092 28.3766 54.2031C27.6918 53.8589 27.0705 54.2404 26.8589 54.7154C26.7947 54.8596 26.651 55.1703 26.651 55.1703C26.1961 56.1647 25.2033 56.8028 24.1095 56.8028H23.4853C21.6752 56.8028 20.2081 55.3355 20.2081 53.5257V51.3407C20.2081 49.531 21.6752 48.0636 23.4853 48.0636H24.1095C25.2033 48.0636 26.1961 48.7017 26.651 49.6961C26.651 49.6961 26.7842 49.9945 26.8589 50.151C27.1871 50.8389 27.9632 50.9367 28.454 50.6179C28.7987 50.3935 29.1243 49.8516 28.8459 49.2426C28.638 48.788 28.638 48.788 28.638 48.788C27.8279 47.0157 26.0582 45.8789 24.1095 45.8789Z"
                                                fill="#D8DFE3" />
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M30.5859 55.1711V55.7125C30.5859 56.581 30.931 57.4142 31.545 58.0282C32.1592 58.6425 32.9922 58.9875 33.8607 58.9875H36.3231C37.1916 58.9875 38.0246 58.6425 38.6388 58.0282C39.2531 57.4142 39.5981 56.581 39.5981 55.7125C39.5981 55.5656 39.5981 55.4199 39.5981 55.2778C39.5981 53.9378 38.7822 52.7329 37.538 52.235L33.5658 50.6462C33.0855 50.4541 32.7706 49.989 32.7706 49.4717V49.1561C32.7706 48.8664 32.8857 48.5885 33.0905 48.3837C33.2955 48.1787 33.5731 48.0636 33.8628 48.0636H36.321C36.6107 48.0636 36.8886 48.1787 37.0933 48.3837C37.2981 48.5885 37.4132 48.8664 37.4132 49.1561V49.6956C37.4132 50.0039 37.5495 50.2829 37.6866 50.4244C37.8998 50.6664 38.206 50.7946 38.5056 50.7946C39.0373 50.7946 39.5981 50.3841 39.5981 49.6956V49.1561C39.5981 48.287 39.2528 47.4532 38.6383 46.8387C38.0235 46.2242 37.1901 45.8789 36.321 45.8789H33.8631C32.9937 45.8789 32.1603 46.2242 31.5458 46.8387C30.9312 47.4532 30.5859 48.287 30.5859 49.1561V49.4717C30.5859 50.8822 31.4448 52.1509 32.7544 52.6747L36.7265 54.2637C37.1413 54.4294 37.4132 54.8313 37.4132 55.2778V55.7125C37.4132 56.0016 37.2983 56.279 37.0938 56.4835C36.8896 56.688 36.6122 56.8028 36.3231 56.8028H33.8607C33.5716 56.8028 33.2944 56.688 33.0899 56.4835C32.8855 56.279 32.7706 56.0016 32.7706 55.7127C32.7706 55.7125 32.7706 55.7125 32.7706 55.1708C32.7706 54.4504 32.1834 54.0713 31.6973 54.0713C31.2364 54.0713 30.5859 54.4286 30.5859 55.1643V55.1711Z"
                                                fill="#D8DFE3" />
                                        </svg>
                                    </div>
                                    <label
                                        class="form-label text-title d-flex fw-semibold align-items-center gap-8 justify-content-center mb-20">
                                        Upload CSV File
                                    </label>
                                    <div class="ot-fileUploader">
                                        <input class="form-control" type="text" placeholder="Choose your CSV file..."
                                            readonly id="placeholderModal">
                                        <div class="primary-btn-small-input">
                                            <label class="btn-primary-fill file-up-btn d-flex align-items-center gap-6"
                                                for="fileBrowseModal">
                                                <x-common.icons name="upload" class="text-white" size="16"
                                                    stroke-width="1.5" />
                                                Browse
                                            </label>
                                            <input type="file" class="d-none form-control" name="csv_file"
                                                id="fileBrowseModal" accept=".csv" required>
                                        </div>
                                    </div>
                                    @if ($errors->has('csv_file'))
                                        <span class="text-danger">{{ $errors->first('csv_file') }}</span>
                                    @endif
                                </div>
                            </div>


                            {{-- Download Template --}}
                            <div class="col-12">
                                <div class="bulk-import-demo-example radius-8 p-16">
                                    <div class="d-flex align-items-center gap-12">
                                        <svg width="50" height="50" viewBox="0 0 50 50" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M31.2507 4.16797H12.5007C11.3956 4.16797 10.3358 4.60696 9.55437 5.38836C8.77297 6.16976 8.33398 7.22957 8.33398 8.33464V41.668C8.33398 42.773 8.77297 43.8328 9.55437 44.6142C10.3358 45.3956 11.3956 45.8346 12.5007 45.8346H37.5006C38.6057 45.8346 39.6655 45.3956 40.4469 44.6142C41.2283 43.8328 41.6673 42.773 41.6673 41.668V14.5846L31.2507 4.16797Z"
                                                stroke="#161F2B" stroke-width="1.5" stroke-linecap="round"
                                                stroke-linejoin="round" />
                                            <path
                                                d="M29.166 4.16797V12.5013C29.166 13.6064 29.605 14.6662 30.3864 15.4476C31.1678 16.229 32.2276 16.668 33.3327 16.668H41.666"
                                                stroke="#161F2B" stroke-width="1.5" stroke-linecap="round"
                                                stroke-linejoin="round" />
                                            <path d="M20.8327 18.75H16.666" stroke="#161F2B" stroke-width="1.5"
                                                stroke-linecap="round" stroke-linejoin="round" />
                                            <path d="M33.3327 27.082H16.666" stroke="#161F2B" stroke-width="1.5"
                                                stroke-linecap="round" stroke-linejoin="round" />
                                            <path d="M33.3327 35.418H16.666" stroke="#161F2B" stroke-width="1.5"
                                                stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>
                                        <div class="d-flex flex-column gap-8 align-items-start">
                                            <h6 class="text-16 text-capitalize text-title fw-semibold mb-0">Demo
                                                Example !</h6>
                                            <p class="text-14 fw-normal text-subtitle mb-0">Download sample CSV file
                                                for user</p>
                                            <button type="button" class="green-btn d-flex align-items-center gap-6"
                                                id="downloadTemplate">
                                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M8 10V2" stroke="white" stroke-linecap="round"
                                                        stroke-linejoin="round" />
                                                    <path
                                                        d="M14 10V12.6667C14 13.0203 13.8595 13.3594 13.6095 13.6095C13.3594 13.8595 13.0203 14 12.6667 14H3.33333C2.97971 14 2.64057 13.8595 2.39052 13.6095C2.14048 13.3594 2 13.0203 2 12.6667V10"
                                                        stroke="white" stroke-linecap="round" stroke-linejoin="round" />
                                                    <path d="M4.66602 6.66797L7.99935 10.0013L11.3327 6.66797"
                                                        stroke="white" stroke-linecap="round" stroke-linejoin="round" />
                                                </svg>
                                                Download
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="modal-footer border-0 m-0 pb-0 pt-24">
                            <button type="button" class="btn-cancel-soft d-flex align-items-center gap-6"
                                data-bs-dismiss="modal">
                                <x-common.icons name="closed-round" class="" size="16" stroke-width="1.5" />
                                Cancel
                            </button>
                            <button type="submit" class="btn-primary-fill d-flex align-items-center gap-6"
                                id="importBtn">
                                <x-common.icons name="upload" class="text-white" size="16" stroke-width="1.5" />
                                Start Import
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection



@pushonce('script')
    <script>
        $(document).ready(function() {
            // Handle card clicks using event delegation for better reliability
            $(document).on('click', '.import-card', function() {
                const model = $(this).data('model');
                const modelDisplayName = $(this).find('h3').text();

                $('#selectedModel').val(model);
                $('#selectedModelName').text(modelDisplayName);
                $('#templateModelName').text(modelDisplayName.toLowerCase());

                // Use Bootstrap 5 syntax
                const modalElement = document.getElementById('uploadModal');
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
            });

            // Handle file input in modal
            $('#fileBrowseModal').on('change', function() {
                const fileName = this.files[0]?.name || '';
                $('#placeholderModal').val(fileName);
                console.log('File selected:', fileName); // Debug log

                // Also update the button text to show file is selected
                if (fileName) {
                    $('.file-up-btn').html(`
                        <x-common.icons name="check-circle" class="text-white" size="16" stroke-width="1.5" />
                        File Selected
                    `).addClass('btn-success-fill').removeClass('btn-primary-fill');
                }
            });

            // Handle download template
            $('#downloadTemplate').on('click', function() {
                const model = $('#selectedModel').val();

                if (model === 'employee') {
                    // Create download link for employee template
                    const link = $('<a>', {
                        href: '{{ route('bulk-import.generate-employees') }}',
                        download: `employee_import_template.csv`
                    });
                    $('body').append(link);
                    link[0].click();
                    link.remove();
                } else if (model === 'attendance') {
                    // Create download link for attendance template
                    const link = $('<a>', {
                        href: '{{ route('bulk-import.generate-attendance') }}',
                        download: `attendance_import_template.csv`
                    });
                    $('body').append(link);
                    link[0].click();
                    link.remove();
                } else if (model === 'leave') {
                    // Create download link for leave template
                    const link = $('<a>', {
                        href: '{{ route('bulk-import.generate-leave') }}',
                        download: `leave_import_template.csv`
                    });
                    $('body').append(link);
                    link[0].click();
                    link.remove();
                } else {
                    // For other models, you can add specific template routes here
                    alert('Template not available for this model yet.');
                }
            });

            // Form validation
            $('#importForm').on('submit', function(e) {
                const fileInput = $('#fileBrowseModal')[0];
                if (!fileInput.files.length) {
                    e.preventDefault();
                    alert('Please select a CSV file to upload.');
                    return false;
                }

                // Change button text to show loading
                const importBtn = $('#importBtn');
                importBtn.html(`
                    <div class="spinner-border spinner-border-sm text-white me-2" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    Processing...
                `);
                importBtn.prop('disabled', true);
            });

            // Reset modal when closed (Bootstrap 5 compatible)
            const modalElement = document.getElementById('uploadModal');
            modalElement.addEventListener('hidden.bs.modal', function() {
                const importBtn = $('#importBtn');
                importBtn.html(`
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4m11-4l-5-5-5 5m5-5v12"/>
                    </svg>
                    Start Import
                `);
                importBtn.prop('disabled', false);

                // Reset form
                $('#importForm')[0].reset();
                $('#placeholderModal').val('');

                // Reset browse button
                $('.file-up-btn').html(`
                    <x-common.icons name="upload" class="text-white" size="16" stroke-width="1.5" />
                    Browse
                `).removeClass('btn-success-fill').addClass('btn-primary-fill');
            });
        });
    </script>
@endpushonce
