<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Artisan;
use Modules\AssetManagement\Database\Seeders\AssetManagementDatabaseSeeder;
use Modules\Credential\Database\Seeders\CredentialDatabaseSeeder;
use Modules\EmployeeDocuments\Database\Seeders\EmployeeDocumentsDatabaseSeeder;
use Modules\EmployeePerformance\Database\Seeders\EmployeePerformanceDatabaseSeeder;
use Modules\Payroll\Database\Seeders\PayrollDatabaseSeeder;
use Modules\Tardy\Database\Seeders\TardyDatabaseSeeder;

use function isModuleActive;

class ModuleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        if (isModuleActive('Credential')) {
            Artisan::call('vendor:publish --tag=credential-module-storage');
            $this->call(CredentialDatabaseSeeder::class);
        }

        if (isModuleActive('Tardy')) {
            $this->call(TardyDatabaseSeeder::class);
        }

        if (isModuleActive('EmployeePerformance')) {
            Artisan::call('vendor:publish --tag=employeeperformance-module-storage');
            $this->call(EmployeePerformanceDatabaseSeeder::class);
        }

        if (isModuleActive('Payroll')) {
            $this->call(PayrollDatabaseSeeder::class);
        }
        if (isModuleActive('AssetManagement')) {
            $this->call(AssetManagementDatabaseSeeder::class);
        }
        if (isModuleActive('EmployeeDocuments')) {
            $this->call(EmployeeDocumentsDatabaseSeeder::class);
        }

        // if (\isModuleActive('HRPolicies')) {
        //     $this->call(HRPoliciesSeeder::class);
        // }

        // if (\isModuleActive('SystemGuidelines')) {
        //     $this->call(SystemGuidelinesDatabaseSeeder::class);
        // }
    }
}
