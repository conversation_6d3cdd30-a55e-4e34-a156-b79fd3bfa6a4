<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON>les\Credential\Http\Controllers\CredentialAccessController;
use Modules\Credential\Http\Controllers\CredentialController;
use Modules\Credential\Http\Controllers\CredentialGroupController;

Route::middleware(['xss', 'admin'])->group(function () {
    Route::prefix('credential')->group(function () {
        // Credential Group
        Route::controller(CredentialGroupController::class)->prefix('groups')->group(function () {
            Route::get('/', 'index')->name('credential.group.index');
        });

        Route::controller(CredentialController::class)->prefix('group')->name('credential.')->group(function () {
            Route::get('/{groupSlug?}', 'index')->name('index');
            Route::post('/store/{groupSlug?}', 'store')->name('store');
            Route::put('/update/{credential}', 'update')->name('update');
            Route::get('/delete/{credential}', 'destroy')->name('destroy');
        });

        Route::controller(CredentialAccessController::class)->prefix('access')->name('credential.access.')->group(function () {
            Route::get('/{credential}', 'index')->name('index');
            Route::post('/{credential}/store', 'store')->name('store');
            Route::put('/update/{credentialAccess}', 'update')->name('update');
            Route::get('/delete/{credentialAccess}', 'destroy')->name('destroy');
        });
    });
});
