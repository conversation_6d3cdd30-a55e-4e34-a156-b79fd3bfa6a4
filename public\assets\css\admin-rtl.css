/*-----------------------------------------------------------------------------------

  @Version            : 1.0.0
  @Application Name   : Onest HRM
  @Application Author : www.onesttech.com

-----------------------------------------------------------------------------------*/

/*------------------------------------------------------------------
  RTL CSS
--------------------------------------------------------------------*/
.rtl {
  .header {
    right: 286px;
    left: 0;
    transition: right 0.4s, -webkit-right 0.4s;

    .header-search {
      .search-icon {
        left: auto;
        right: 0;
      }

      .search-field {
        padding: 12px 36px 12px 12px;
      }
    }
  }

  .main-content {
    margin-right: 286px;
    margin-left: 0;
    transition: margin-right 0.3s, -webkit-margin-right 0.3s;
  }

  .profile-expand-list {
    float: right;
    align-items: baseline;
    width: 100%;

    .profile-expand-item {
      width: 100%;
      display: flex;
      align-items: center;
      gap: 10px;

      i {
        margin-right: 0;
        margin-left: 10px;
      }
    }
  }

  .topbar-dropdown-header,
  .topbar-dropdown-content {
    text-align: right;
  }

  .sidebar {
    left: auto !important;
    right: 0 !important;
    border-right: 0;
    border-left: 1px solid #b0ceff54;

    .sidebar-menu {

      ol,
      ul {
        padding-right: 0;
        margin-right: 0;

        a {
          text-decoration: none;
        }
      }

      .sidebar-menu-section {
        .sidebar-menu-section-heading {
          margin-right: 30px;
        }

        .sidebar-menu-item {
          >ul>li>ul>li>a {
            padding-right: 64px;
          }
        }
      }

      ul>li>a {
        padding-right: 28px;
        padding-left: 0;
      }
    }
  }

  .select2-container--default {
    .select2-selection--single {
      .select2-selection-arrow b {
        margin-left: 7px;
      }
    }
  }

  .select2-results-options {
    text-align: right;
  }

  .select2-results-option {
    padding: 10px 15px;
  }

  .table-toolbar {
    .dropdown-export {
      .dropdown-menu li {
        text-align: right;
      }

      .dropdown-item .icon {
        margin-left: 8px;
      }
    }

    .dropdown-action .dropdown-menu {
      text-align: right;
    }
  }

  .form-check {
    .form-check-input {
      float: right;
    }
  }

  .accordion-button::after {
    margin-right: auto;
    margin-left: 0;
  }

  .table {
    .thead tr th {
      padding: 16px 16px 16px 32px;
    }

    .sorting-asc::before,
    .sorting-desc::before,
    .sorting-asc::after,
    .sorting-desc::after {
      right: auto;
      left: 16px;
    }
  }

  .input-check-radio {
    .form-check {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 0;

      .form-check-input {
        margin-left: 4px;
      }

      .form-check-label {
        margin-right: 8px;
      }
    }
  }
}

.rtl {
  .half-expand {
    .header {
      z-index: 22;
      right: 100px;
      transition: right 0.4s, -webkit-right 0.4s;
    }

    .main-content {
      margin-right: 100px;
      transition: margin-right 0.3s, -webkit-margin-right 0.3s;
    }

    .sidebar {
      .sidebar-header {
        .half-expand-toggle {
          position: absolute;
          right: 100%;
          margin-right: 10px;

          img {
            transform: rotate(180deg);
          }
        }
      }

      .sidebar-menu {
        overflow: visible;

        .sidebar-menu-section-heading {
          margin-right: 0;
        }

        .parent-menu-list {
          .sidebar-menu-item {
            .parent-item-content span {
              display: none;
            }

            a {
              border: none;
            }
          }
        }

        .sidebar-menu-item {
          >ul>li>a {
            padding-left: 10px !important;
          }

          >ul>li>ul>li>a {
            padding-left: 30px !important;
          }
        }
      }

      ul.parent-menu-list>li>ul {
        position: absolute;
        top: 0;
        left: 100px;
      }
    }

    .sidebar-menu {
      position: absolute;
      left: 50%;
      top: 10%;
      z-index: 2000;
      overflow: hidden;
      height: calc(100vh - 100px);
      transform: translateX(-50%);
    }
  }

  .sidebar-expand {
    .sidebar {
      right: 0;
      transition: right 0.3s;
    }
  }
}

.rtl {
  .half-expand-toggle img {
    transform: rotate(180deg);
  }

  /* half-expand  */
  .half-expand {
    .half-expand-toggle img {
      transform: rotate(0deg) !important;
    }

    .sidebar {
      & .sidebar-menu {
        & .sidebar-menu-section {
          & .sidebar-dropdown-menu {
            & li.sidebar-menu-item {
              &>ul.child-menu-list {
                left: auto;
                right: 68px;

                &::before {
                  display: none;
                }

                & a {
                  &::before {
                    display: none;
                  }

                  &::after {
                    display: none;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

*[dir="rtl"] {
  .sidebar {
    .sidebar-menu {
      .sidebar-menu-section {
        .sidebar-menu-item {
          a.has-arrow::after {
            transform: rotate(-45deg) translate(0, -50%);
          }

          >ul>li>a {
            padding: 5px 47px 5px 0;
          }
        }

        .mm-active>a.has-arrow::after {
          transform: rotate(-135deg) translate(0, -50%);
        }
      }
    }
  }
}

.single-select {
  >.select2-container[dir="rtl"] {
    .select2-selection--single {
      .select2-selection-rendered {
        padding-right: 0;
        padding-left: 30px;
      }
    }
  }
}

.rtl {
  input[type="range"] {
    transform: scaleX(-1);

    &::-webkit-slider-runnable-track {
      transform: scaleX(-1);
    }

    &::-webkit-slider-thumb {
      transform: scaleX(-1);
    }
  }

  .breadcrumb-item+.breadcrumb-item::before {
    float: unset;
    padding-left: 7px;
  }

  .modal-header .btn-close {
    margin: unset;
  }

  /* New */
  .sidebar-menu-item {
    &.mm-active {
      &>.parent-item-content::before {
        left: auto;
        right: -15px;
        border-radius: 4px 0px 0px 4px;
      }
    }
  }

  .sidebar {
    & .sidebar-menu {
      & .sidebar-menu-section {
        & .sidebar-dropdown-menu {
          & li.sidebar-menu-item {
            &>ul.child-menu-list {
              &::before {
                left: auto;
                right: 28px;
              }

              & a {
                &::before {
                  left: auto;
                  right: 26px;
                }

                &::after {
                  left: auto;
                  right: 27px;
                  -webkit-transform: scaleX(-1);
                  transform: scaleX(-1);
                }
              }
            }
          }
        }
      }
    }
  }
}

/*====================================================================
                       LAYOUTS RTL STYLES
====================================================================*/
*[dir="rtl"] {

  aside.sidebar {
    left: auto !important;
    right: 0 !important;
  }

  .timeline {
    min-width: 400px;
    margin: 20px auto;
    position: relative;

    @media (max-width: 767.99px) {
      min-width: auto;
    }

    .timeline-item {
      display: flex;
      align-items: flex-start;
      position: relative;

      .time {
        min-width: 81px;
        text-align: left;
        padding-left: 16px;
        padding-right: 0;
        border-left: 1px dashed #ddd;
        border-right: none;
        padding-bottom: 21px;

        @media (max-width: 767.99px) {
          min-width: 60px;
          padding-left: 8px;
        }
      }

      .circle {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        position: absolute;
        right: 76px;
        left: auto;
        top: 8px;

        @media (max-width: 767.99px) {
          right: 67px;
        }

        &::after {
          position: absolute;
          content: "";
          width: 14px;
          height: 14px;
          border-radius: 50%;
          right: -3px;
          left: auto;
          top: -3px;
        }
      }

      .content {
        padding: 0 15px;
        padding-left: 0;
        border-radius: 8px;
        margin-right: 20px;
        margin-left: 0;
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;

        @media (max-width: 767.99px) {
          margin-right: 10px;
          padding: 0 8px;
          padding-left: 0;
        }

        .user {
          display: flex;
          align-items: center;
          gap: 10px;

          @media (max-width: 767.99px) {
            gap: 6px;
            flex-direction: column;
            align-items: flex-start;
          }

          img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;

            @media (max-width: 767.99px) {
              width: 30px;
              height: 30px;
            }
          }

          div {
            @media (max-width: 767.99px) {
              width: 100%;
            }
          }
        }
      }
    }
  }

  .ot-pagination {
    & .pagination {
      & .page-item {
        margin-right: 0px;
        margin-left: 10px;
      }
    }
  }


  /* ===================================
             COMPONENT WIZARD
  =================================== */
  .avatar {
    margin-left: 0;
    margin-right: -10px;
  }

  .avatar:first-child {
    margin-left: 0;
    margin-right: -10px;
  }

  .more-count {
    margin-right: -10px;
    margin-left: 0;
  }

  .search-box .icon {
    left: 12px;
    right: auto;
  }

  .leave-status-employee {
    & .profile-image {
      &::after {
        right: auto;
        left: 0;
      }
    }
  }

  .profile-complete-status {
    left: 10px;
    right: auto;
  }

  .mr-30 {
    margin-right: auto !important;
    margin-left: 30px;
  }

  .radio-button-group .btn-group .btn {
    border-radius: 0.3rem 0 0rem 0.3rem !important;

    &:first-child {
      border-radius: 0 0.3rem 0.3rem 0 !important;
    }
  }

  .form-control[type="email"] {
    direction: rtl !important;
  }

  /* ===================================
   RESPONSIVE
  =================================== */
  @media only screen and (min-width: 992px) {
    aside.sidebar {
      left: auto !important;
      right: 0 !important;
    }

    .main-content {
      width: calc(100% - 286px);
      left: 0 !important;
      margin-left: 0 !important;
      margin-right: auto !important;
    }

    #layout-wrapper main {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      flex: 1 1 0;
    }

    .header {
      right: 286px;
      left: 0 !important;
      width: calc(100% - 286px);
    }

    /* half-expand */
    .half-expand {
      .header {
        right: 100px;
        left: 0 !important;
        width: calc(100% - 100px);
      }

      .main-content {
        width: calc(100% - 100px);
        left: 0 !important;
        margin-left: 0 !important;
        margin-right: auto !important;
      }
    }
  }

  @media only screen and (max-width: 991px) {
    aside.sidebar {
      left: auto !important;
      right: -100% !important;
    }

    .sidebar-expand aside.sidebar {
      left: auto !important;
      right: 0px !important;
    }

    .main-content {
      width: 100%;
      left: 0 !important;
      margin-left: 0 !important;
      margin-right: auto !important;
    }

    .sidebar-expand {
      .aside.sidebar {
        right: 0 !important;
        transition: right 0.3s;
      }
    }
  }
}