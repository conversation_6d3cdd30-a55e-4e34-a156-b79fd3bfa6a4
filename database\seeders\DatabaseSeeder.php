<?php

namespace Database\Seeders;

use Database\Seeders\Admin\AttendanceConfigurationSeeder;
use Database\Seeders\Admin\CompanySeeder;
use Database\Seeders\Admin\RoleSeeder;
use Database\Seeders\Admin\SalaryConfigurationSeeder;
use Database\Seeders\Admin\UserDutyScheduleSeeder;
use Database\Seeders\Admin\UserInfoSeeder;
use Database\Seeders\Admin\UserSeeder;
use Database\Seeders\Admin\UserWisePermissionSeeder;
use Database\Seeders\Leave\LeaveAssignSeeder;
use Database\Seeders\Leave\LeaveRequestSeeder;
use Database\Seeders\Leave\LeaveTypeSeeder;
use Database\Seeders\Setting\BrandSettingSeeder;
use Database\Seeders\Setting\IntegrationSettingSeeder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;

class DatabaseSeeder extends Seeder
{
    public function run()
    {
        // Disable foreign key constraints
        Schema::disableForeignKeyConstraints();

        // Disable mass assignment protection
        Model::unguard();

        $this->syncAssetsToStorage();

        // First run critical seeders that create reference tables
        $this->runCriticalSeeders();

        // Then run the rest of the seeders
        $this->runMainSeeders();

        // Enable foreign key constraints
        Schema::enableForeignKeyConstraints();

        // Re-enable mass assignment protection
        Model::reguard();
    }

    protected function syncAssetsToStorage()
    {
        $fileStorage = config('filesystems.default');
        $publicSeederPath = public_path('seeder');
        $storageSeederPath = Storage::disk($fileStorage)->path('seeder');

        Artisan::call('storage:unlink');
        File::deleteDirectory(public_path('storage'));
        Storage::disk($fileStorage)->deleteDirectory('uploads');
        Storage::disk($fileStorage)->deleteDirectory('seeder');

        File::copyDirectory($publicSeederPath, $storageSeederPath);

        Artisan::call('storage:link');
        $this->command->info('Created new storage symlink.');
    }

    /**
     * Run critical seeders that establish reference tables
     * These must run first because other tables depend on them
     */
    protected function runCriticalSeeders()
    {
        // Next populate currencies
        if (Schema::hasTable('currencies')) {
            $this->call(CurrencySeeder::class);
        }

        // Next populate countries
        if (Schema::hasTable('countries')) {
            $this->call(CountrySeeder::class);
        }

        // Next populate company and branch tables
        $this->call(CompanySeeder::class);
        $this->call(BranchSeeder::class);

        // Now that we have companies and branches, populate languages
        if (Schema::hasTable('languages')) {
            $this->call(LanguageSeeder::class);
        }
    }

    /**
     * Run the main seeders that depend on reference tables
     */
    protected function runMainSeeders()
    {
        // Company and branch are now in critical seeders
        $this->call(RoleSeeder::class);
        $this->call(DesignationSeeder::class);
        $this->call(LeaveTypeSeeder::class);
        $this->call(DepartmentSeeder::class);
        $this->call(UserSeeder::class);
        $this->call(LeaveAssignSeeder::class);
        $this->call(LeaveRequestSeeder::class);
        $this->call(SettingsSeeder::class);
        $this->call(DutyScheduleSeeder::class);
        $this->call(WeekendSeeder::class);
        $this->call(BrandSettingSeeder::class);
        $this->call(UserInfoSeeder::class);
        $this->call(UserWisePermissionSeeder::class);
        $this->call(AttendanceConfigurationSeeder::class);
        $this->call(UserDutyScheduleSeeder::class);
        $this->call(AttendanceSeeder::class);
        $this->call(SalaryConfigurationSeeder::class);
        $this->call(StickyNoteSeeder::class);
        $this->call(ModuleSeeder::class);
        $this->call(IntegrationSettingSeeder::class);
        $this->call(AppointmentSeeder::class);
        $this->call(NoticeSeeder::class);
    }
}
