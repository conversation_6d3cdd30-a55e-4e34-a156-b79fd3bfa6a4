/**
 * CustomCalendar - A reusable jQuery-based calendar component
 * Usage: new CustomCalendar('elementId', { options })
 */
class CustomCalendar {
    constructor(elementId, options = {}) {
        this.elementId = elementId;
        this.options = {
            title: 'Calendar',
            format: 'yyyy-mm-dd',
            inline: true,
            type: 'inline',
            daysOfWeekDisabled: [0, 6],
            daysOfWeekHighlighted: [1, 2, 3, 4, 5],
            showOnFocus: true,
            todayHighlight: true,
            weekStart: 1,
            autohide: false,
            onDateSelect: null, // Callback function for date selection
            onMonthChange: null, // Callback function for month change
            ...options
        };
        this.element = null;
        this.datepicker = null;
        this.init();
    }

    init() {
        $(document).ready(() => {
            this.element = document.getElementById(this.elementId);
            if (!this.element) {
                console.error(`Element with ID '${this.elementId}' not found`);
                return;
            }

            this.createDatepicker();
            this.applyCustomizations();
        });
    }

    createDatepicker() {
        const prevArrow = this.createArrowIcon('arrow_left');
        const nextArrow = this.createArrowIcon('arrow_right_alt');

        this.datepicker = new Datepicker(this.element, {
            ...this.options,
            nextArrow: prevArrow,
            prevArrow: nextArrow,
        });

        // Listen for view changes
        this.element.addEventListener('changeView', () => {
            setTimeout(() => this.applyCustomizations(), 10);
            if (this.options.onMonthChange) {
                this.options.onMonthChange(this.datepicker.getDate());
            }
        });

        // Listen for date selection
        this.element.addEventListener('changeDate', (e) => {
            if (this.options.onDateSelect) {
                this.options.onDateSelect(e.detail.date);
            }
        });
    }

    createArrowIcon(iconName) {
        return $(`<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="none" viewBox="0 0 24 24">
            <path d="M15 18l-6-6 6-6" stroke="#545C66" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>`)[0];
    }

    restructureHeader() {
        const header = this.element.querySelector('.datepicker-header');
        const prev = header?.querySelector('.prev-button');
        const next = header?.querySelector('.next-button');
        const viewSwitch = header?.querySelector('.view-switch');

        if (!header || !prev || !next || !viewSwitch) return;

        const leftGroup = document.createElement('div');
        const rightGroup = document.createElement('div');
        const title = document.createElement('div');

        leftGroup.classList.add('left-group');
        rightGroup.classList.add('right-group');
        title.classList.add('datepicker-title');
        title.textContent = this.options.title;

        leftGroup.appendChild(title);
        rightGroup.appendChild(prev);
        rightGroup.appendChild(viewSwitch);
        rightGroup.appendChild(next);

        header.innerHTML = '';
        header.appendChild(leftGroup);
        header.appendChild(rightGroup);
    }

    injectSVG() {
        const viewSwitch = this.element.querySelector('.view-switch');
        if (viewSwitch && !viewSwitch.querySelector('svg')) {
            viewSwitch.style.display = 'flex';
            viewSwitch.style.alignItems = 'center';
            viewSwitch.style.gap = '6px';

            viewSwitch.insertAdjacentHTML('beforeend', `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16">
                    <path d="M5.33333 1.33301V3.33301M10.6667 1.33301V3.33301M2.33333 6.05967H13.6667M14 5.66634V11.333C14 13.333 13 14.6663 10.6667 14.6663H5.33333C3 14.6663 2 13.333 2 11.333V5.66634C2 3.66634 3 2.33301 5.33333 2.33301H10.6667C13 2.33301 14 3.66634 14 5.66634Z" stroke="#545C66" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M10.4652 9.13379H10.4713M10.4652 11.1338H10.4713M7.99858 9.13379H8.00525M7.99858 11.1338H8.00525M5.53125 9.13379H5.53792M5.53125 11.1338H5.53792" stroke="#545C66" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            `);
        }
    }

    attachReapplyListeners() {
        const header = this.element.querySelector('.datepicker-header');
        if (!header) return;

        const prev = header.querySelector('.prev-button');
        const next = header.querySelector('.next-button');
        const viewSwitch = header.querySelector('.view-switch');

        [prev, next, viewSwitch].forEach(btn => {
            if (btn) {
                btn.addEventListener('click', () => {
                    setTimeout(() => this.applyCustomizations(), 10);
                }, { once: true });
            }
        });
    }

    applyCustomizations() {
        this.restructureHeader();
        this.injectSVG();
        this.attachReapplyListeners();
    }

    // Public methods
    getDatepicker() {
        return this.datepicker;
    }

    getElement() {
        return this.element;
    }

    getSelectedDate() {
        return this.datepicker ? this.datepicker.getDate() : null;
    }

    setDate(date) {
        if (this.datepicker) {
            this.datepicker.setDate(date);
        }
    }

    updateTitle(newTitle) {
        this.options.title = newTitle;
        const titleElement = this.element.querySelector('.datepicker-title');
        if (titleElement) {
            titleElement.textContent = newTitle;
        }
    }

    destroy() {
        if (this.datepicker) {
            this.datepicker.destroy();
        }
    }
}

// Global function for easy initialization
window.createCustomCalendar = function (elementId, options = {}) {
    return new CustomCalendar(elementId, options);
};
