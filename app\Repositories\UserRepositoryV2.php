<?php

namespace App\Repositories;

use App\Models\Attendance\Attendance;
use App\Models\Attendance\DutySchedule;
use App\Models\Attendance\Weekend;
use App\Models\Country;
use App\Models\Department;
use App\Models\Designation;
use App\Models\Language;
use App\Models\Role;
use App\Models\User;
use App\Models\UserPersonalDocument;
use App\Services\FileUploadService;
use App\Traits\LogEmployeeLifecycle;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Modules\AreaBasedAttendance\Entities\LocationBind;
use Modules\IpBasedAttendance\Entities\UserIpBind;
use Modules\Notify\Services\NotificationService;
use Throwable;

class UserRepositoryV2
{
    use LogEmployeeLifecycle;

    public function __construct(
        protected User $model,
        protected Role $roleModel,
        protected Language $languageModel,
        protected Department $departmentModel,
        protected Designation $designationModel,
        protected UserPersonalDocument $personalDocumentModel,
        protected Country $countryModel,
        protected Weekend $weekendModel,
        protected DutySchedule $dutyScheduleModel
    ) {}

    public function getPaginateData($request)
    {
        $query = $this->model->query()
            ->with([
                'manager:id,name',
                'personalInfo:id,user_id,address,birth_date,blood_group,marital_status,profile_percentage',
                'dutySchedules:id,shift,start_time,end_time',
                'salaryConfig:id,user_id,joining_date,payslip_type',
            ])
            ->where('branch_id', getBranchId());

        $where = [];
        if ($request->search) {
            $where[] = ['name', 'like', '%'.$request->search.'%'];
        }
        if (@$request->designation_id) {
            $where[] = ['designation_id', $request->designation_id];
        }
        if (@$request->department_id) {
            $where[] = ['department_id', $request->department_id];
        }
        if (@$request->status) { // active status filter
            $where[] = ['status', $request->status];
        }

        return $query->where($where)
            ->latest()
            ->paginate($request->limit ?? 10);
    }

    public function storeEmployeeSetup($payload)
    {
        return DB::transaction(function () use ($payload) {
            // Create user with mass assignment
            $user = new $this->model;

            $user->fill([
                'name' => $payload['name'],
                'email' => $payload['email'],
                'phone' => $payload['phone'],
                'employee_id' => $payload['employee_id'],
                'job_type' => $payload['job_type'],
                'department_id' => $payload['department_id'],
                'designation_id' => $payload['designation_id'],
                'role_id' => $payload['role_id'],
                'status' => $payload['status'],
                'company_id' => Auth::user()->company_id,
                'password' => Hash::make(12345678),
                'lang' => $this->languageModel->where('is_default', 1)->value('code'),
            ])->save();

            // Log lifecycle
            $this->logLifecycle(
                'Created',
                [
                    'Job Type' => formatTitleCase($user->job_type),
                    'Department' => $user->department->title,
                    'Designation' => $user->designation->title,
                    'Role' => $user->role->name,
                ],
                $user->id
            );

            // Retrieve permissions once
            $rolePermissions = $this->roleModel->where('id', $payload['role_id'])->first()->permissions->permissions;

            // Create related configurations
            $user->salaryConfig()->create([
                'joining_date' => $payload['joining_date'],
            ]);

            $user->attendanceConfig()->create([
                'attendance_method' => $payload['attendance_method'],
            ]);

            // Update or create duty schedule
            $user->dutySchedules()->sync([$payload['duty_schedule_id']]);

            $user->permissions()->create([
                'permissions' => $rolePermissions,
            ]);

            // Notification & email (only if mail config is set)
            try {
                notification()->send([
                    'receiver' => $user,
                    'title' => 'Password Change Required',
                    'message' => '<b>'.$user->name.'</b> please change your temporary password!',
                    'web_redirect_url' => route('user.profile', ['process_id' => $user->id]),
                ]);
            } catch (Throwable $th) {
                Log::error('Password email failed: '.$th->getMessage());
            }

            return $user;
        });
    }

    public function dutySchedules()
    {
        return $this->dutyScheduleModel->select('id', 'shift', 'start_time', 'end_time')->get()->map(function ($item) {
            $item->shift = @$item->shift.' ['.Carbon::parse($item->start_time)->format('h:i: A').' - '.Carbon::parse($item->end_time)->format('h:i: A').']';

            return $item;
        });
    }

    public function storePersonalInfo($id, $payload)
    {
        $user = $this->model->findOrfail($id);

        return DB::transaction(function () use ($payload, $user) {
            $user->gender = $payload['gender'];
            // Avatar handling
            $image = [];
            if (isset($payload['avatar']) && $payload['avatar']->isValid()) {
                $image = [
                    'disk' => config('filesystems.default'),
                    'file' => FileUploadService::image($payload['avatar'], 'user', 400, 400, $user->avatar),
                ];
            } elseif (! $user->image) {
                $image = [
                    'disk' => config('filesystems.default'),
                    'file' => FileUploadService::generateAvatar($user->name, $user->gender),
                ];
            }

            $user->image = empty($image) ? $user->image : $image;
            $user->save();

            // Update or create personal info (via relationship method)
            $user->personalInfo()->updateOrCreate(
                ['user_id' => $user->id],
                [
                    'country_id' => $payload['country_id'],
                    'address' => $payload['address'],
                    'religion' => $payload['religion'],
                    'marital_status' => $payload['marital_status'],
                    'blood_group' => $payload['blood_group'],
                    'birth_date' => $payload['birth_date'],
                    'speak_language' => $payload['speak_language'],
                    'profile_percentage' => $payload['profile_percentage'],
                ]
            );

            return $user;
        });
    }

    public function updateOfficialSetup($id, $payload)
    {
        return DB::transaction(function () use ($id, $payload) {
            $user = $this->model->findOrFail($id);
            $user->manager_id = ! blank($payload['manager_id']) ? $payload['manager_id'] : null;
            $user->save();

            $user->attendanceConfig->updateOrCreate(
                [
                    'user_id' => $user->id,
                ],
                [
                    'time_zone' => @$payload['time_zone'],
                    'is_free_ip' => @$payload['is_free_ip'],
                    'is_free_location' => @$payload['is_free_location'],
                ]
            );

            // User IP bind
            if (! empty($payload['ip_addresses'])) {
                $ipAddresses = array_map('trim', explode(',', $payload['ip_addresses']));

                UserIpBind::updateOrCreate(
                    [
                        'user_id' => $user->id,
                        'company_id' => getCompanyId(),
                        'branch_id' => getBranchId(),
                    ],
                    [
                        'ip_addresses' => $ipAddresses,
                    ]
                );
            }

            // User location bind
            if (! empty($payload['locations']) && is_array($payload['locations'])) {
                // Optionally clear previous binds if needed
                LocationBind::where('user_id', $user->id)->delete();

                foreach ($payload['locations'] as $location) {
                    LocationBind::create([
                        'user_id' => $user->id,
                        'latitude' => $location['latitude'],
                        'longitude' => $location['longitude'],
                        'address' => $location['address'],
                        'distance' => $location['distance'],
                    ]);
                }
            }

            $user->personalInfo()->update(['profile_percentage' => $payload['profile_percentage']]);

            return $user;
        });
    }

    public function delete($user)
    {
        $user->update(['status_id' => 4]);
        $user->save();
        $user->delete();

        return true;
    }

    public function updatePersonalDocument($id, $payload)
    {
        $user = $this->show($id);
        $document = $user->personalDocument ?? new $this->personalDocumentModel;
        $document->user_id = $id;

        // Passport
        $document->passport_number = $payload['passport_number'] ?? null;
        if (isset($payload['passport_file'])) {
            $document->passport_file = $this->uploadPersonalFile($payload['passport_file'], $document->passport_file);
        }
        $document->passport_expire_date = $payload['passport_expire_date'] ?? null;

        // Labour Identification
        $document->labour_identification_number = $payload['labour_identification_number'] ?? null;
        if (isset($payload['labour_identification_file'])) {
            $document->labour_identification_file = $this->uploadPersonalFile($payload['labour_identification_file'], $document->labour_identification_file);
        }
        $document->labour_identification_expire_date = $payload['labour_identification_expire_date'] ?? null;

        // Social Security
        $document->social_security_number = $payload['social_security_number'] ?? null;
        if (isset($payload['social_security_file'])) {
            $document->social_security_file = $this->uploadPersonalFile($payload['social_security_file'], $document->social_security_file);
        }

        // NID Card
        $document->nid_card_number = $payload['nid_card_number'] ?? null;
        if (isset($payload['nid_card_file'])) {
            $document->nid_card_file = $this->uploadPersonalFile($payload['nid_card_file'], $document->nid_card_file);
        }

        // TIN
        $document->tin_number = $payload['tin_number'] ?? null;
        if (isset($payload['tin_file'])) {
            $document->tin_file = $this->uploadPersonalFile($payload['tin_file'], $document->tin_file);
        }

        $document->save();

        $user->personalInfo()->update(['profile_percentage' => $payload['profile_percentage']]);

        return $document;
    }

    public function show($id, $with = [])
    {
        return $this->model->with($with)->findOrFail($id);
    }

    private function uploadPersonalFile($file, $oldFile = null, $path = 'user/personal-document')
    {
        if (isset($file) && $file->isValid()) {
            $file = [
                'disk' => config('filesystems.default'),
                'file' => FileUploadService::file($file, $path, $oldFile),
            ];

            return empty($file) ? $oldFile : $file;
        }
    }

    public function updateSalaryConfiguration($id, $payload)
    {
        $user = $this->model->findOrFail($id);

        // Convert allowances and deductions to JSON if they exist
        if (isset($payload['allowances'])) {
            $payload['allowances'] = json_encode($payload['allowances']);
        }

        if (isset($payload['deductions'])) {
            $payload['deductions'] = json_encode($payload['deductions']);
        }

        // Use the relationship method to create or update
        $user->salaryConfig()->updateOrCreate(
            ['user_id' => $user->id],
            $payload
        );

        $user->personalInfo()->update(['profile_percentage' => $payload['profile_percentage']]);

        return $user;
    }

    public function updateEmployeeSetup($id, $payload)
    {
        // Create a new instance of the User model
        $user = $this->model->findOrfail($id);

        return DB::transaction(function () use ($user, $payload) {
            $backupUser = clone $user;

            // Update user fields
            $user->update([
                'name' => $payload['name'],
                'email' => $payload['email'],
                'phone' => $payload['phone'],
                'employee_id' => $payload['employee_id'],
                'department_id' => $payload['department_id'],
                'designation_id' => $payload['designation_id'],
                'role_id' => $payload['role_id'],
                'status' => $payload['status'],
            ]);

            // Employee Lifecycle Log Start
            if ($backupUser->department_id != $user->department_id) {
                $this->logLifecycle('Department Changed', ['Previous Department' => $backupUser->department->title, 'Changed To' => $user->department->title], $user->id);
            }

            if ($backupUser->designation_id != $user->designation_id) {
                $this->logLifecycle('Designation Changed', ['Previous Designation' => $backupUser->designation->title, 'Changed To' => $user->designation->title], $user->id);
            }
            // Employee Lifecycle Log End

            // Get updated permissions from role
            $rolePermissions = $this->roleModel->where('id', $payload['role_id'])->first()->permissions()->value('permissions');

            // Update or create salary config
            $user->salaryConfig()->updateOrCreate(
                ['user_id' => $user->id],
                ['joining_date' => $payload['joining_date']]
            );

            // Update or create attendance config
            $user->attendanceConfig()->updateOrCreate(
                ['user_id' => $user->id],
                [
                    'duty_schedule_id' => $payload['duty_schedule_id'],
                    'attendance_method' => $payload['attendance_method'],
                ]
            );

            // Update or create duty schedule
            $user->dutySchedules()->sync([$payload['duty_schedule_id']]);

            // Update or create user permissions
            $user->permissions()->updateOrCreate(
                ['user_id' => $user->id],
                ['permissions' => $rolePermissions]
            );

            $user->personalInfo()->update(['profile_percentage' => $payload['profile_percentage']]);

            return $user;
        });
    }

    public function employees()
    {
        return $this->model->isActive()->get();
    }

    public function departments()
    {
        return $this->departmentModel->select('id', 'title')->where('status', 'active')->get() ?? [];
    }

    public function designations()
    {
        return $this->designationModel->select('id', 'title')->where('status', 'active')->get();
    }

    public function roles()
    {
        return $this->roleModel->select('id', 'name', 'slug')->where('status', 'active')->get();
    }

    public function countries()
    {
        return $this->countryModel->pluck('name', 'id');
    }

    public function managers()
    {
        return $this->model->query()
            ->isActive()
            ->with('designation:id,title')
            ->isActive()
            ->get(['id', 'name', 'designation_id']);
    }

    public function weekends()
    {
        return $this->weekendModel->pluck('name');
    }

    public function defaultWeekends()
    {
        return $this->weekendModel->where('is_weekend', true)->pluck('name')->toArray();
    }

    public function getAll()
    {
        return $this->model->query()->get();
    }

    public function getActiveAll()
    {
        return $this->model
            ->query()
            ->isActive()
            ->get();
    }

    public function getById($id)
    {
        return $this->model->find($id);
    }

    public function trashedData($request)
    {
        $query = $this->model->query()->onlyTrashed()
            ->with(['manager:id,name', 'personalInfo:id,user_id,birth_date,blood_group,marital_status', 'attendanceConfig.dutySchedule:id,shift,start_time,end_time', 'salaryConfig:id,user_id,joining_date,payslip_type']);

        $where = [];
        if ($request->search) {
            $where[] = ['name', 'like', '%'.$request->search.'%'];
        }
        if (@$request->designation_id) {
            $where[] = ['designation_id', $request->designation_id];
        }
        if (@$request->status) { // active status filter
            $where[] = ['status', $request->status];
        }

        return $query->where($where)
            ->orderBy('created_at', 'desc')
            ->paginate($request->limit ?? 10);
    }

    public function permissionUpdate($request, $id)
    {
        return DB::transaction(function () use ($request, $id) {
            $user = $this->model->findOrFail($id);

            $user->permissions()->updateOrCreate(
                ['user_id' => $user->id],
                ['permissions' => $request->permissions]
            );

            return $user;
        });
    }

    public function deletePermanently($id)
    {
        $user = $this->model->onlyTrashed()->findOrFail($id);

        // Now permanently delete the user
        return $user->forceDelete();
    }

    public function changeStatus($user, $status)
    {
        dd($status);
        $user->update([
            'status_id' => $status,
        ]);
        (new NotificationService)->storeEmployeeStatusActiveInactiveNotification($user);

        return true;
    }

    public function makeHR($user_id)
    {
        $user = $this->model->query()->find($user_id);
        if ($user->is_hr == 1) {
            $user->is_hr = 0;
            $user->update();
        } else {
            $user->is_hr = 1;
            $user->update();
        }

        return $user;
    }

    public function employeeLocationHistory($request, $id = null)
    {
        $logs = DB::table('location_logs')
            ->select('latitude', 'longitude', 'address as start_location', 'id', 'created_at')
            ->where('user_id', $request->user)
            ->where('date', 'LIKE', $request->date.'%')
            ->get();

        $data = [];
        $total = $logs->count();
        foreach ($logs as $key => $value) {
            if ($total > 25 ? ($key % ceil($total / 25)) == 0 || $key == 0 || $key == $total - 1 : true) {
                // Format the created_at timestamp
                $formattedCreatedAt = date('j F Y, h:i a', strtotime($value->created_at));
                $value->created_at = $formattedCreatedAt;

                array_push($data, $value);
            }
        }

        return $data;
    }

    public function sendResetMail($id)
    {
        $user = $this->model->find($id);

        if (! $user) {
            throw new Exception(_trans('message.User not found.'));
        }

        $password = Str::random(8);
        $user->password = Hash::make($password);
        $user->save();

        if (! $this->sendEmail($user, $password)) {
            throw new Exception(_trans('message.Mail not sent.'));
        }
    }

    public function resetDevice($user_id, $device)
    {
        try {
            $user = $this->model->query()->find($user_id);
            if ($device == 'mobile') {
                $user->app_token = null;
                $user->device_id = null;
                $user->device_info = null;
            } else {
                $user->device_uuid = null;
            }
            $user->last_login_device = null;

            $user->save();

            return true;
        } catch (Throwable $th) {
            return $th->getMessage();

            return false;
        }
    }

    public function restore($id)
    {
        $user = $this->model->withTrashed()->find($id);

        return $user->restore();
    }

    public function employeeSummary()
    {
        return [
            [
                'title' => _trans('common.Total Employee'),
                'value' => User::count(),
                'class' => 'text-primary',
            ],
            [
                'title' => _trans('common.Active Employee'),
                'value' => User::isActive()->count(),
                'class' => 'text-success',
            ],
            [
                'title' => _trans('common.Today Absent'),
                'value' => $this->todayAbsent()->count(),
                'class' => 'text-danger',
            ],
            [
                'title' => _trans('common.Today Present'),
                'value' => $this->todayPresent()->count(),
                'class' => 'text-primary',
            ],
            [
                'title' => _trans('common.Total Department'),
                'value' => Department::count(),
                'class' => 'text-warning',
            ],
            [
                'title' => _trans('common.Total Role'),
                'value' => Role::count(),
                'class' => 'text-primary',
            ],
        ];
    }

    protected function todayAbsent()
    {
        $today = Carbon::now()->toDateString();

        return User::with([
            'approvedLeaves',
            'attendances' => function ($query) use ($today) {
                $query->whereDate('date', $today);
            },
        ])
            ->select('id', 'name', 'image')
            ->whereDoesntHave('attendances', function ($query) use ($today) {
                $query->whereDate('date', $today);
            })
            ->whereDoesntHave('approvedLeaves', function ($query) use ($today) {
                $query->whereDate('leave_from', '<=', $today)
                    ->whereDate('leave_to', '>=', $today);
            })
            ->get();
    }

    protected function todayPresent()
    {
        $today = Carbon::now()->toDateString();

        return Attendance::with([
            'user:id,name',
            'user.approvedLeaves',
        ])
            ->whereDate('date', $today)
            ->whereDoesntHave('user.approvedLeaves', function ($query) use ($today) {
                $query->whereDate('leave_from', '<=', $today)
                    ->whereDate('leave_to', '>=', $today);
            })
            ->get();
    }
}
