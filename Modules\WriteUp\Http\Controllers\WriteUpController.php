<?php

namespace Modules\WriteUp\Http\Controllers;

use App\Repositories\DepartmentRepository;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use Modules\WriteUp\Entities\Complain;
use Modules\WriteUp\Entities\ComplainDetail;
use Modules\WriteUp\Http\Repositories\ComplainRepository;
use Modules\WriteUp\Http\Requests\CreateComplainRequest;
use Throwable;

class WriteUpController extends Controller
{
    public function __construct(
        protected ComplainRepository $repository,
        protected DepartmentRepository $departmentRepository
    ) {}

    public function index(Request $request)
    {
        $data['title'] = _trans('writeup.Complain List');
        $data['departments'] = $this->departmentRepository->getAll();
        $data['collection'] = $this->repository->getPaginateData($request);

        return view('writeup::complain.index')->with($data);
    }

    public function store(CreateComplainRequest $request)
    {
        try {
            $this->repository->store($request);

            return redirect()->route('complain.index')->with('success', _trans('writeup.Created successfully'));
        } catch (Throwable $th) {
            return redirect()->back()->with('error', _trans('writeup.Something went wrong'));
        }
    }

    public function edit($id)
    {
        $data['title'] = _trans('meeting.Update Complaint Application');
        $data['today'] = Carbon::now()->format('Y-m-d');
        $data['complain_to'] = $this->repository->complaintTo();
        $data['complain_for'] = $this->repository->users();
        $data['complain'] = $this->repository->show($id);

        return view('writeup::complain.edit')->with($data);
    }

    public function show($id)
    {
        $findId = decrypt($id);
        $data['complain'] = Complain::with('complainDetails.user.role')->findOrFail($findId);
        $data['title'] = _trans('common.Complaint Application Preview');

        return view('writeup::complain.show')->with($data);
    }

    public function delete($id)
    {
        try {
            $this->repository->delete($id);

            return redirect()->route('complain.index')->with('success', _trans('writeup.Deleted successfully'));
        } catch (Throwable $th) {
            return redirect()->back()->with('error', _trans('writeup.Something went wrong'));
        }
    }

    public function updateComplainStatus(Request $request, $id)
    {
        try {
            $complain = Complain::find($id);

            $complainStatus = $request->is_appeal ? 'appeal' : 'agree';
            $complain->update([
                'complain_status' => $complainStatus,
            ]);

            if ($request->is_appeal) {
                ComplainDetail::create([
                    'complain_id' => $complain->id,
                    'user_id' => Auth::id(),
                    'description' => $request->explaination,
                    'is_hr_contact' => ! $request->is_explaination ? 1 : 0,
                ]);
            }

            $complainStatusColor = $complainStatus == 'appeal' ? 'text-warning' : 'text-success';

            if ($request->is_reply) {
                $complainStatus = _trans('common.reply');
            }

            $params['message'] = '<b class="text-danger">'.$complain->title.':</b> <span class="'.$complainStatusColor.'">'.$complainStatus.'</span>';

            if (Auth::id() != $complain->user_id) {
                $params['receivers'][] = $complain->user_id;
            }

            if (! empty($complain->user->manager_id)) {
                $params['receivers'][] = $complain->user->manager_id;
            }

            $params['redirectUrlForWeb'] = route('complain.show', encrypt($complain->id));
            $params['forSelf'] = false;
            storeNotification($params);

            $message = _trans('alert.Complain has been ').$complainStatus.' '._trans('alert.successfully');

            return redirect()->back()->with('success', $message);
        } catch (Throwable $th) {
            return redirect()->back()->with('error', _trans('writeup.Something went wrong'));
        }
    }

    public function update(CreateComplainRequest $request, $id)
    {
        try {
            $this->repository->update($request, $id);

            return redirect()->route('complain.index')->with('success', _trans('writeup.Updated successfully'));
        } catch (Throwable $th) {
            return redirect()->back()->with('error', _trans('writeup.Something went wrong'));
        }
    }

    public function create()
    {
        $data['title'] = _trans('writeup.Complaint Application');
        $data['today'] = Carbon::now()->format('Y-m-d');
        $data['complain_to'] = $this->repository->complaintTo();
        $data['complain_for'] = $this->repository->users();

        return view('writeup::complain.create')->with($data);
    }

    public function addHrToThisConversation($id)
    {
        try {
            $complain = Complain::where('id', $id)->first();

            $complain->update([
                'is_show_complain_about_me' => 1,
                'is_feedback_enable' => 1,
            ]);

            $params['message'] = 'You had been added a <b class="text-warning">complaint</b> agains you! Added By: <b class="text-dark">'.Auth::user()->name.' ['.Auth::user()->employee_id.']</b>';
            $params['receivers'][] = $complain->user_id;
            $params['redirectUrlForWeb'] = route('complain.show', $complain->id);
            $params['forSelf'] = false;
            storeNotification($params);

            return redirect()->back()->with('success', _trans('response.Complain has been updated successfully'));
        } catch (Throwable $th) {
            return redirect()->back()->with('error', _trans('response.Something went wrong'));
        }
    }
}
