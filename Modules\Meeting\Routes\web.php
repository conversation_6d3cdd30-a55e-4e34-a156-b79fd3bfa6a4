<?php

use Illuminate\Support\Facades\Route;
use Modules\Meeting\Http\Controllers\MeetingController;

Route::controller(MeetingController::class)->prefix('meetings')->middleware(['web', 'demo.mode', 'xss', 'TimeZone', 'admin'])->group(function () {
    Route::get('/', 'index')->name('meetings.index')->middleware('PermissionCheck:meeting_read');
    Route::get('/create', 'create')->name('meetings.create')->middleware('PermissionCheck:meeting_create');
    Route::post('/', 'store')->name('meetings.store')->middleware('PermissionCheck:meeting_create');
    Route::get('/{id}/edit', 'edit')->name('meetings.edit')->middleware('PermissionCheck:meeting_update');
    Route::put('/{id}', 'update')->name('meetings.update')->middleware('PermissionCheck:meeting_update');
    Route::get('/delete/{id}', 'destroy')->name('meetings.destroy')->middleware('PermissionCheck:meeting_delete');
});
