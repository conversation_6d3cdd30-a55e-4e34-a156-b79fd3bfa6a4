@extends('backend.layouts.app')
@section('title', @$data['title'])

@section('content')
    <div class="row">
        <div class="col-lg-8">
            <x-container :title="@$title" buttonTitle="Back" buttonRoute="{{ route('complain.index') }}">
                @if ($complain->is_complain_about_hr)
                    <div class="alert alert-danger">
                        <b>{{ _trans('common.A report has been made against HR') }}: {{ $complain->user->name }}
                            [{{ $complain->user->employee_id }}]</b>
                    </div>
                @endif

                <div class="d-flex align-items-center justify-content-between gap-2">
                    <div class="d-flex flex-column gap-1">
                        <span class="text-primary">{{ _trans('common.Date') }}: {{ $complain->date }}</span>
                        <span class="text-danger">{{ _trans('common.Response Deadline') }}:
                            {{ $complain->response_deadline }}</span>
                    </div>
                    <div class="d-flex flex-column gap-1">
                        <span class="text-muted">{{ _trans('common.Complain By') }}:
                            {{ $complain->created_by == auth()->id() ? $complain->createdBy->name : _trans('common.Anonymous') }}</span>
                        <span class="text-muted">{{ _trans('common.Complain At') }}:
                            {{ date('Y-m-d h:i A', strtotime($complain->created_at)) }}</span>
                    </div>
                </div>

                <div class="d-flex justify-content-between align-items-center">
                    <div class="">
                        Application Status <span
                            class="badge text-capitalize badge-{{ $complain->complain_status == 'pending' ? 'danger' : ($complain->complain_status == 'appeal' ? 'warning' : 'success') }}">
                            {{ $complain->complain_status }}
                        </span>
                    </div>

                    @if ($complain->is_complain_about_hr && !$complain->is_feedback_enable)
                        <form id="addHrForm" action="{{ route('complain.add-hr-to-this-conversation', $complain->id) }}"
                            method="POST">
                            @csrf
                            <button type="button" class="btn btn-sm btn-info" id="addHrBtn">
                                <i class="las la-user-plus"></i>
                                {{ _trans('common.Add HR to this conversation') }}
                            </button>
                        </form>
                    @endif
                </div>

                <div class="border border-1 p-2 my-2 rounded-2">
                    <p><strong>Date:</strong> {{ formatDateTime($complain->date) }}</p>

                    <p><strong>Subject:</strong> Complaint Regarding {{ $complain->title }}</p>

                    <p>
                        <strong>Dear,</strong>
                        @foreach ($complain->complainTos as $to)
                            <span class="badge bg-light text-dark font-size-13">{{ $to->user->name }}</span>
                            @if (!$loop->last)
                                ,
                            @endif
                        @endforeach
                    </p>

                    <p>I am writing to formally lodge a complaint regarding
                        <span class="badge bg-light text-dark font-size-13">{{ $complain->user->name ?? 'N/A' }}</span>
                    </p>

                    <p>{{ $complain->description }}</p>

                    @if ($complain->attachment)
                        <p>
                            <strong>Attachment:</strong>
                            <a href="{{ $complain->attachment }}" target="_blank">
                                View File <i class="las la-paperclip"></i>
                            </a>
                        </p>
                    @endif

                    <p>Looking forward to a prompt resolution.</p>

                    <p>Thank you for your attention.</p>

                    <p>Yours sincerely,</p>

                    <p>
                        {{ $complain->createdBy->name }}<br />
                        {{ $complain->createdBy->designation->title }}, {{ $complain->createdBy->department->title }}
                    </p>
                </div>
            </x-container>
        </div>
        <div class="col-lg-4">
            <div class="ot-card">
                @if ($complain->complain_status == 'appeal')
                    <div class="my-5">
                        <h3>{{ _trans('common.Replies') }} ({{ count($complain->complainDetails ?? []) }})</h3>
                        @foreach ($complain->complainDetails ?? [] as $complainDetail)
                            <div class="d-flex flex-column gap-2 ms-5 border p-3 mb-3">
                                <div class="d-flex align-items-center gap-2">
                                    @if ($complainDetail->user_id == auth()->id())
                                        <img src="{{ $complainDetail->user->avatar }}" class="staff-profile-image-small">
                                        <div class="d-flex flex-column gap-0">
                                            <span>{{ $complainDetail->user->name }}</span>
                                            <small
                                                class="text-muted">{{ date('Y-m-d h:i A', strtotime($complainDetail->created_at)) }}</small>
                                        </div>
                                    @else
                                        <img src="{{ asset('assets/images/avater.png') }}"
                                            class="staff-profile-image-small">
                                        <div class="d-flex flex-column gap-0">
                                            <span>{{ _trans('common.Anonymous') }}</span>
                                            <small
                                                class="text-muted">{{ date('Y-m-d h:i A', strtotime($complainDetail->created_at)) }}</small>
                                        </div>
                                    @endif
                                </div>
                                <p class="ps-5 ms-1">
                                    {{ $complainDetail->is_hr_contact ? _trans('common.Contact with HR') : $complainDetail->description }}
                                </p>
                            </div>
                        @endforeach
                    </div>
                @endif

                @if ($complain->response_deadline >= date('Y-m-d') && $complain->complain_status != 'agree')
                    @if (
                        ($complain->is_complain_about_hr && @auth()->user()->role->slug == 'hr' && $complain->is_feedback_enable) ||
                            (@auth()->user()->role->slug != 'hr' && $complain->is_complain_about_hr) ||
                            !$complain->is_complain_about_hr)
                        <div class="border bg-light p-3 mt-3">
                            <form action="{{ route('complain.update-complain-status', $complain->id) }}" method="POST"
                                class="d-flex flex-column gap-3">
                                @csrf
                                <input type="hidden" name="is_reply"
                                    value="{{ $complain->complain_status == 'appeal' ? 1 : 0 }}">
                                <div class="d-flex align-items-center gap-3 mb-3">
                                    <h3 class="m-0">
                                        {{ $complain->complain_status == 'pending' ? _trans('common.Feedback') : _trans('common.Reply') }}
                                        :
                                    </h3>
                                    @if ($complain->complain_status == 'pending')
                                        <div class="">
                                            <input type="radio" class="btn-check" name="is_appeal" id="appeal"
                                                value="1" autocomplete="off" checked onchange="toggleFeedback(this)">
                                            <label class="btn" for="appeal">{{ _trans('common.Appeal') }}</label>
                                            <input type="radio" class="btn-check" name="is_appeal" id="agree"
                                                value="0" autocomplete="off" onchange="toggleFeedback(this)">
                                            <label class="btn" for="agree">{{ _trans('common.Agree') }}</label>
                                        </div>
                                    @else
                                        <input type="hidden" name="is_appeal" value="1">
                                    @endif
                                </div>
                                <div id="explain">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="is_explaination"
                                            id="writeExplaination" onchange="requiredExplaination(this)" value="1"
                                            checked>
                                        <label class="form-check-label" for="writeExplaination">
                                            {{ _trans('common.Write Explaination') }}
                                        </label>
                                    </div>
                                    <textarea name="explaination" rows="5" class="ms-3 form-control explaination"
                                        placeholder="{{ _trans('common.Write here') }}" required></textarea>
                                </div>
                                @if (@auth()->user()->role->slug != 'hr')
                                    <div id="talkWithHR" class="form-check">
                                        <input class="form-check-input" type="radio" name="is_explaination"
                                            id="directTalkWithHR" onchange="requiredExplaination(this)" value="0">
                                        <label class="form-check-label" for="directTalkWithHR">
                                            {{ _trans('common.Direct talk with HR') }}
                                        </label>
                                    </div>
                                @endif

                                <div id="noExplain" class="d-none text-warning bg-white p-3 fw-bold shadow-sm">
                                    {{ _trans('common.I acknowledge this complaint and accept it without further explanation') }}
                                </div>
                                <div class="mt-3">
                                    <button type="submit"
                                        class="btn-primary-fill px-3 float-end d-flex align-items-center justify-content-center gap-2">
                                        <i class="lab la-telegram"></i>
                                        {{ _trans('common.Send') }}
                                    </button>
                                </div>
                            </form>
                        </div>
                    @endif
                @endif
            </div>
        </div>
    </div>

@endsection

@push('script')
    <script>
        const toggleFeedback = (obj) => {
            const value = $(obj).val();

            if (value == 1) {
                $('#explain').removeClass('d-none');
                $('#talkWithHR').removeClass('d-none');
                $('#noExplain').addClass('d-none');
            } else {
                $('#explain').addClass('d-none');
                $('#talkWithHR').addClass('d-none');
                $('#noExplain').removeClass('d-none');
                $('.explaination').prop('required', false);
            }
        };

        const requiredExplaination = (obj) => {
            const value = $(obj).val();

            if (value == 1) {
                $('.explaination').prop('required', true);
            } else {
                $('.explaination').prop('required', false);
            }
        };

        $('#addHrBtn').on('click', function() {
            Swal.fire({
                title: `{{ _trans('common.Are you sure?') }}`,
                text: `{{ _trans('common. Do you wanna add ') }}` +
                    `{{ $complain->user->name }}` + ' [' +
                    `{{ $complain->user->employee_id }}` + '] in this conversation!',
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#3085d6",
                cancelButtonColor: "#d33",
                confirmButtonText: `{{ _trans('common.Yes') }}`
            }).then((result) => {
                if (result.isConfirmed) {
                    $('#addHrForm').submit();
                } else if (
                    result.dismiss === Swal.DismissReason.cancel
                ) {
                    swalWithBootstrapButtons.fire({
                        title: "Cancelled",
                        text: "Your imaginary file is safe :)",
                        icon: "error"
                    });
                }
            });
        })
    </script>
@endpush
