<?php

namespace Modules\AssetManagement\Http\Controllers;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Modules\AssetManagement\Entities\AssetCategory;
use Modules\AssetManagement\Http\Requests\AssetCategoryRequest;
use Modules\AssetManagement\Repositories\AssetCategoryRepository;

use function _trans;
use function redirect;

class AssetCategoryController extends Controller
{
    public function __construct(
        protected AssetCategoryRepository $repository
    ) {}

    public function index(Request $request)
    {
        $data['title'] = _trans('common.Asset Categories');
        $data['formTitle'] = _trans('common.Add Asset Categories');
        $data['collection'] = $this->repository->getPaginateData($request);

        if ($request->filled('id')) {
            $data['formTitle'] = _trans('common.Edit Asset Categories');
            $data['model'] = $this->repository->show($request->id);
        }

        return view('assetmanagement::asset_categories.index', $data);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  string|null  $groupSlug
     * @return Response
     */
    public function store(AssetCategoryRequest $request)
    {
        try {
            $this->repository->store($request->validated());

            return redirect()->back()->with('success', _trans('alert.Created Successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @return Response
     */
    public function update(AssetCategoryRequest $request, $id)
    {
        if (config('app.style') === 'demo' || env('APP_STYLE') === 'demo') {
            return redirect()->back()->with('error', _trans('alert.You are not allowed to perform the delete action in demo mode'));
        }

        try {
            $category = AssetCategory::findOrFail($id);
            $this->repository->update($request->validated(), $category);

            return redirect()->route('assets.category.index')->with('success', _trans('alert.Updated Successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    /**
     * Remove the specified credential from storage.
     *
     * If the application is in demo mode, the delete action is not allowed.
     * Otherwise, attempts to delete the credential using the repository.
     * Redirects to the credential index route with a success message on
     * successful deletion, or handles exceptions if any occur.
     *
     * @return Response
     */
    public function destroy(AssetCategory $credential)
    {
        try {
            $this->repository->delete($credential);

            return redirect()->route('assets.category.index')->with('success', _trans('alert.Deleted Successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }
}
