<?php

use Illuminate\Support\Facades\Route;
use Modules\AssetManagement\Http\Controllers\AssetCategoryController;
use Modules\AssetManagement\Http\Controllers\AssetController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::group(['middleware' => ['xss', 'admin']], function () {
    Route::prefix('assets')->group(function () {
        // Assets
        Route::controller(AssetController::class)->group(function () {
            Route::get('/index', 'index')->name('assets.asset.index')->middleware('PermissionCheck:asset_read');
            Route::get('/create', 'create')->name('assets.asset.create')->middleware('PermissionCheck:asset_create');
            Route::post('/store', 'store')->name('assets.asset.store')->middleware('PermissionCheck:asset_create');

            Route::get('/view/{id}', 'show')->name('assets.asset.view')->middleware('PermissionCheck:asset_read');

            Route::get('/edit/{id}', 'edit')->name('assets.asset.edit')->middleware('PermissionCheck:asset_update');
            Route::put('/update/{credential}', 'update')->name('assets.asset.update')->middleware('PermissionCheck:asset_update');
            Route::get('/delete/{credential}', 'destroy')->name('assets.asset.destroy')->middleware('PermissionCheck:asset_delete');
            Route::get('/asset-details/{id}', 'assetDetails');

            Route::post('/assign-store', 'assignStore')->name('assets.asset.assignStore')->middleware('PermissionCheck:asset_assign');
            Route::get('asset-history', 'assetHistory')->name('assets.asset.history')->middleware('PermissionCheck:asset_history_read');
        });

        // Asset Category
        Route::controller(AssetCategoryController::class)->prefix('category')->group(function () {
            Route::get('/', 'index')->name('assets.category.index')->middleware('PermissionCheck:asset_category_read');
            Route::post('/store/{id?}', 'store')->name('assets.category.store')->middleware('PermissionCheck:asset_category_create');
            Route::put('/update/{credential}', 'update')->name('assets.category.update')->middleware('PermissionCheck:asset_category_update');
            Route::get('/delete/{credential}', 'destroy')->name('assets.category.destroy')->middleware('PermissionCheck:asset_category_delete');
        });

    });
});
