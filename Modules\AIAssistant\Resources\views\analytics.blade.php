@extends('backend.layouts.app')

@section('title', 'AI Assistant Analytics')

@section('content')
    {{-- <PERSON> Header --}}
    <div class="ot-card mb-3">
        <div class="d-flex align-items-center justify-content-between flex-wrap gap-20">
            <div class="dashboard-heading-wrapper d-flex gap-10">
                <div class="icon icon-size-32 text-primary line-height-1">
                    <x-common.icons name="chart-analytics" class="text-primary" size="32" stroke-width="1.5" />
                </div>
                <div class="dashboard-heading">
                    <h3 class="card-title d-flex align-items-center gap-10">AI Assistant Analytics</h3>
                    <p class="paragraph text-subtitle mb-0">Monitor AI performance, user engagement, and system insights</p>
                </div>
            </div>
            <div class="d-flex align-items-center gap-10">
                <div class="d-flex align-items-center gap-10">
                    <input type="date" class="form-control form-control-sm" id="start-date"
                        value="{{ date('Y-m-d', strtotime('-30 days')) }}">
                    <span class="text-secondary">to</span>
                    <input type="date" class="form-control form-control-sm" id="end-date" value="{{ date('Y-m-d') }}">
                </div>
                <button class="btn-primary-fill" id="refresh-btn">
                    <x-common.icons name="refresh" class="text-white" size="16" stroke-width="1.5" />
                    Refresh
                </button>
            </div>
        </div>
    </div>

    {{-- Stats Cards --}}
    <div class="row g-y-16 mb-3">
        <div class="col-xl-3 col-lg-4 col-md-6">
            <div class="ot-card border radius-8 p-24 d-flex flex-column">
                <div class="d-flex align-items-center gap-10 mb-16">
                    <div class="icon icon-size-32 text-primary line-height-1">
                        <x-common.icons name="chat" class="text-primary" size="32" stroke-width="1.5" />
                    </div>
                    <div class="flex-fill">
                        <h4 class="text-24 fw-bold mb-2" id="total-queries">0</h4>
                        <p class="text-14 text-subtitle mb-0">Total Queries</p>
                    </div>
                </div>
                <div class="d-flex align-items-center gap-5">
                    <x-common.icons name="trending-up" class="text-success" size="20" stroke-width="1.5" />
                    <span class="badge bg-success-subtle text-success">+12%</span>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-4 col-md-6">
            <div class="ot-card border radius-8 p-24 d-flex flex-column">
                <div class="d-flex align-items-center gap-10 mb-16">
                    <div class="icon icon-size-32 text-info line-height-1">
                        <x-common.icons name="users" class="text-info" size="32" stroke-width="1.5" />
                    </div>
                    <div class="flex-fill">
                        <h4 class="text-24 fw-bold mb-2" id="active-users">0</h4>
                        <p class="text-14 text-subtitle mb-0">Active Users</p>
                    </div>
                </div>
                <div class="d-flex align-items-center gap-5">
                    <x-common.icons name="trending-up" class="text-success" size="20" stroke-width="1.5" />
                    <span class="badge bg-success-subtle text-success">+8%</span>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-4 col-md-6">
            <div class="ot-card border radius-8 p-24 d-flex flex-column">
                <div class="d-flex align-items-center gap-10 mb-16">
                    <div class="icon icon-size-32 text-warning line-height-1">
                        <x-common.icons name="time" class="text-warning" size="32" stroke-width="1.5" />
                    </div>
                    <div class="flex-fill">
                        <h4 class="text-24 fw-bold mb-2" id="avg-response-time">0s</h4>
                        <p class="text-14 text-subtitle mb-0">Avg Response Time</p>
                    </div>
                </div>
                <div class="d-flex align-items-center gap-5">
                    <x-common.icons name="trending-down" class="text-success" size="20" stroke-width="1.5" />
                    <span class="badge bg-success-subtle text-success">-15%</span>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-4 col-md-6">
            <div class="ot-card border radius-8 p-24 d-flex flex-column">
                <div class="d-flex align-items-center gap-10 mb-16">
                    <div class="icon icon-size-32 text-success line-height-1">
                        <x-common.icons name="check-circle" class="text-success" size="32" stroke-width="1.5" />
                    </div>
                    <div class="flex-fill">
                        <h4 class="text-24 fw-bold mb-2" id="success-rate">0%</h4>
                        <p class="text-14 text-subtitle mb-0">Success Rate</p>
                    </div>
                </div>
                <div class="d-flex align-items-center gap-5">
                    <x-common.icons name="trending-up" class="text-success" size="20" stroke-width="1.5" />
                    <span class="badge bg-success-subtle text-success">+5%</span>
                </div>
            </div>
        </div>
    </div>

    {{-- Charts Row --}}
    <div class="row g-y-16 mb-3">
        <div class="col-lg-8">
            <div class="ot-card">
                <div class="d-flex align-items-center justify-content-between mb-3">
                    <h3 class="card-title d-flex align-items-center gap-10">
                        <x-common.icons name="chart-analytics" class="text-primary me-2" size="20"
                            stroke-width="1.5" />
                        Queries by Day
                    </h3>
                </div>
                <div class="chart-container" style="height: 300px;">
                    <canvas id="queriesChart"></canvas>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="ot-card">
                <div class="d-flex align-items-center justify-content-between mb-3">
                    <h3 class="card-title d-flex align-items-center gap-10">
                        <x-common.icons name="database" class="text-info me-2" size="20" stroke-width="1.5" />
                        Query Types
                    </h3>
                </div>
                <div class="chart-container" style="height: 300px;">
                    <canvas id="queryTypesChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    {{-- Tables Row --}}
    <div class="row g-y-16 mb-3">
        <div class="col-lg-6">
            <div class="ot-card">
                <div class="d-flex align-items-center justify-content-between mb-3">
                    <h3 class="card-title d-flex align-items-center gap-10">
                        <x-common.icons name="help-circle" class="text-warning me-2" size="20"
                            stroke-width="1.5" />
                        Top Questions
                    </h3>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover" id="top-questions-table">
                        <thead>
                            <tr>
                                <th>Question</th>
                                <th>Count</th>
                                <th>Success Rate</th>
                                <th>Avg Time</th>
                                <th>Trend</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="5" class="text-center text-muted py-4">
                                    <x-common.icons name="search" class="text-muted" size="24"
                                        stroke-width="1.5" />
                                    <p class="mt-2 mb-0">No data available</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="ot-card">
                <div class="d-flex align-items-center justify-content-between mb-3">
                    <h3 class="card-title d-flex align-items-center gap-10">
                        <x-common.icons name="clock" class="text-success me-2" size="20" stroke-width="1.5" />
                        Recent Queries
                    </h3>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover" id="recent-queries-table">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Question</th>
                                <th>Response Time</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="6" class="text-center text-muted py-4">
                                    <x-common.icons name="search" class="text-muted" size="24"
                                        stroke-width="1.5" />
                                    <p class="mt-2 mb-0">No data available</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    {{-- Performance Metrics --}}
    <div class="row g-y-16 mb-3">
        <div class="col-lg-6">
            <div class="ot-card">
                <div class="d-flex align-items-center justify-content-between mb-3">
                    <h3 class="card-title d-flex align-items-center gap-10">
                        <x-common.icons name="chart-analytics" class="text-primary me-2" size="20"
                            stroke-width="1.5" />
                        Performance Metrics
                    </h3>
                </div>
                <div class="user-related-info d-flex align-items-center justify-content-between flex-wrap gap-20">
                    <div class="user-related-info-item d-flex align-items-center gap-10">
                        <div class="contents">
                            <h6 class="title text-16 fw-bold mb-6" id="avg-query-length">0</h6>
                            <p class="paragraph mb-0 text-title">Avg Query Length</p>
                        </div>
                    </div>
                    <span class="line-style"></span>
                    <div class="user-related-info-item d-flex align-items-center gap-10">
                        <div class="contents">
                            <h6 class="title text-16 fw-bold mb-6" id="peak-hours">0</h6>
                            <p class="paragraph mb-0 text-title">Peak Hours</p>
                        </div>
                    </div>
                    <span class="line-style"></span>
                    <div class="user-related-info-item d-flex align-items-center gap-10">
                        <div class="contents">
                            <h6 class="title text-16 fw-bold mb-6" id="session-duration">0m</h6>
                            <p class="paragraph mb-0 text-title">Avg Session</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="ot-card">
                <div class="d-flex align-items-center justify-content-between mb-3">
                    <h3 class="card-title d-flex align-items-center gap-10">
                        <x-common.icons name="users" class="text-warning me-2" size="20" stroke-width="1.5" />
                        User Engagement
                    </h3>
                </div>
                <div class="user-related-info d-flex align-items-center justify-content-between flex-wrap gap-20">
                    <div class="user-related-info-item d-flex align-items-center gap-10">
                        <div class="contents">
                            <h6 class="title text-16 fw-bold mb-6" id="new-users">0</h6>
                            <p class="paragraph mb-0 text-title">New Users</p>
                        </div>
                    </div>
                    <span class="line-style"></span>
                    <div class="user-related-info-item d-flex align-items-center gap-10">
                        <div class="contents">
                            <h6 class="title text-16 fw-bold mb-6" id="returning-users">0</h6>
                            <p class="paragraph mb-0 text-title">Returning</p>
                        </div>
                    </div>
                    <span class="line-style"></span>
                    <div class="user-related-info-item d-flex align-items-center gap-10">
                        <div class="contents">
                            <h6 class="title text-16 fw-bold mb-6" id="engagement-score">0%</h6>
                            <p class="paragraph mb-0 text-title">Engagement</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Export Section --}}
    <div class="ot-card">
        <div class="d-flex align-items-center justify-content-between mb-3">
            <h3 class="card-title d-flex align-items-center gap-10">
                <x-common.icons name="download" class="text-primary me-2" size="20" stroke-width="1.5" />
                Export Analytics
            </h3>
        </div>
        <div class="d-flex gap-10">
            <button class="btn-primary-outline" onclick="exportData('pdf')">
                <x-common.icons name="file-text" class="me-2" size="16" stroke-width="1.5" />
                Export PDF
            </button>
            <button class="btn-primary-outline" onclick="exportData('excel')">
                <x-common.icons name="grid" class="me-2" size="16" stroke-width="1.5" />
                Export Excel
            </button>
            <button class="btn-primary-outline" onclick="exportData('csv')">
                <x-common.icons name="file" class="me-2" size="16" stroke-width="1.5" />
                Export CSV
            </button>
        </div>
    </div>
@endsection

@push('script')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize charts
            initializeCharts();

            // Load initial data
            loadDashboardData();
            loadRecentQueries();

            // Event handlers
            $('#refresh-btn').on('click', function() {
                $(this).prop('disabled', true);
                $(this).html(
                    '<x-common.icons name="refresh" class="text-white" size="16" stroke-width="1.5" /> Loading...'
                );

                Promise.all([
                    loadDashboardData(),
                    loadRecentQueries()
                ]).finally(() => {
                    $(this).prop('disabled', false);
                    $(this).html(
                        '<x-common.icons name="refresh" class="text-white" size="16" stroke-width="1.5" /> Refresh'
                    );
                });
            });
        });

        // Initialize charts
        function initializeCharts() {
            // Queries by Day Chart
            const queriesCtx = document.getElementById('queriesChart').getContext('2d');
            window.queriesChart = new Chart(queriesCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Queries',
                        data: [],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Query Types Chart
            const typesCtx = document.getElementById('queryTypesChart').getContext('2d');
            window.queryTypesChart = new Chart(typesCtx, {
                type: 'doughnut',
                data: {
                    labels: ['HR', 'Payroll', 'Leave', 'Performance', 'Other'],
                    datasets: [{
                        data: [30, 25, 20, 15, 10],
                        backgroundColor: [
                            '#3b82f6',
                            '#10b981',
                            '#f59e0b',
                            '#ef4444',
                            '#8b5cf6'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // Load dashboard data
        function loadDashboardData() {
            // Simulate API call - replace with actual endpoint
            const mockData = {
                totalQueries: 1247,
                activeUsers: 89,
                avgResponseTime: 2.3,
                successRate: 94.2,
                avgQueryLength: 23,
                peakHours: '2-4 PM',
                sessionDuration: 8.5,
                newUsers: 12,
                returningUsers: 77,
                engagementScore: 87.3
            };

            // Update stats
            $('#total-queries').text(mockData.totalQueries.toLocaleString());
            $('#active-users').text(mockData.activeUsers);
            $('#avg-response-time').text(mockData.avgResponseTime + 's');
            $('#success-rate').text(mockData.successRate + '%');
            $('#avg-query-length').text(mockData.avgQueryLength);
            $('#peak-hours').text(mockData.peakHours);
            $('#session-duration').text(mockData.sessionDuration + 'm');
            $('#new-users').text(mockData.newUsers);
            $('#returning-users').text(mockData.returningUsers);
            $('#engagement-score').text(mockData.engagementScore + '%');

            // Update charts
            updateCharts(mockData);
        }

        // Update charts with new data
        function updateCharts(data) {
            // Update queries chart
            const last7Days = Array.from({
                length: 7
            }, (_, i) => {
                const d = new Date();
                d.setDate(d.getDate() - i);
                return d.toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric'
                });
            }).reverse();

            const queriesData = [45, 52, 38, 67, 89, 76, 94];

            window.queriesChart.data.labels = last7Days;
            window.queriesChart.data.datasets[0].data = queriesData;
            window.queriesChart.update();
        }

        // Load recent queries
        function loadRecentQueries() {
            // Simulate API call - replace with actual endpoint
            const mockQueries = [{
                    id: 1,
                    user: 'John Doe',
                    question: 'How do I submit a leave request?',
                    responseTime: '1.2s',
                    status: 'success',
                    date: '2025-01-21 14:30'
                },
                {
                    id: 2,
                    user: 'Jane Smith',
                    question: 'What is my current leave balance?',
                    responseTime: '0.8s',
                    status: 'success',
                    date: '2025-01-21 14:25'
                },
                {
                    id: 3,
                    user: 'Mike Johnson',
                    question: 'How to update my profile information?',
                    responseTime: '2.1s',
                    status: 'success',
                    date: '2025-01-21 14:20'
                }
            ];

            updateRecentQueries(mockQueries);
            updateTopQuestions(mockQueries);
        }

        // Update recent queries table
        function updateRecentQueries(data) {
            const tableBody = $('#recent-queries-table tbody');
            tableBody.empty();

            if (!data || data.length === 0) {
                tableBody.append(`
                <tr>
                    <td colspan="6" class="text-center text-muted py-4">
                        <x-common.icons name="search" class="text-muted" size="24" stroke-width="1.5" />
                        <p class="mt-2 mb-0">No data available</p>
                    </td>
                </tr>
            `);
                return;
            }

            data.forEach(query => {
                const statusBadge = query.status === 'success' ?
                    '<span class="badge bg-success-subtle text-success">Success</span>' :
                    '<span class="badge bg-danger-subtle text-danger">Failed</span>';

                tableBody.append(`
                <tr>
                    <td>
                        <div class="d-flex align-items-center gap-10">
                            <div class="avatar-group">
                                <img src="https://i.pravatar.cc/32?img=${query.id}" alt="User" class="avatar rounded-circle" width="32" height="32">
                            </div>
                            <span class="fw-medium">${query.user}</span>
                        </div>
                    </td>
                    <td>
                        <div class="text-truncate" style="max-width: 200px;" title="${query.question}">
                            ${query.question}
                        </div>
                    </td>
                    <td>${query.responseTime}</td>
                    <td>${statusBadge}</td>
                    <td>${query.date}</td>
                    <td>
                        <div class="d-flex gap-5">
                            <button class="btn btn-sm btn-outline-primary" onclick="viewQuery(${query.id})">
                                <x-common.icons name="eye" size="14" stroke-width="1.5" />
                            </button>
                            <button class="btn btn-sm btn-outline-info" onclick="exportQuery(${query.id})">
                                <x-common.icons name="download" size="14" stroke-width="1.5" />
                            </button>
                        </div>
                    </td>
                </tr>
            `);
            });
        }

        // Update top questions table
        function updateTopQuestions(data) {
            const tableBody = $('#top-questions-table tbody');
            tableBody.empty();

            if (!data || data.length === 0) {
                tableBody.append(`
                <tr>
                    <td colspan="5" class="text-center text-muted py-4">
                        <x-common.icons name="search" class="text-muted" size="24" stroke-width="1.5" />
                        <p class="mt-2 mb-0">No data available</p>
                    </td>
                </tr>
            `);
                return;
            }

            data.forEach(query => {
                tableBody.append(`
                <tr>
                    <td>
                        <div class="text-truncate" style="max-width: 200px;" title="${query.question}">
                            ${query.question}
                        </div>
                    </td>
                    <td>
                        <span class="badge bg-primary-subtle text-primary">${Math.floor(Math.random() * 50) + 10}</span>
                    </td>
                    <td>
                        <span class="badge bg-success-subtle text-success">${Math.floor(Math.random() * 20) + 80}%</span>
                    </td>
                    <td>${(Math.random() * 3 + 0.5).toFixed(1)}s</td>
                    <td>
                        <x-common.icons name="trending-up" class="text-success" size="16" stroke-width="1.5" />
                    </td>
                </tr>
            `);
            });
        }

        // Export functions
        function exportData(format) {
            // Implement export functionality
            console.log(`Exporting data in ${format} format`);
            // You can implement actual export logic here
        }

        function viewQuery(id) {
            // Implement view query functionality
            console.log(`Viewing query ${id}`);
        }

        function exportQuery(id) {
            // Implement export query functionality
            console.log(`Exporting query ${id}`);
        }
    </script>
@endpush
