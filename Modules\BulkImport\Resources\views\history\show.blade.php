@extends('backend.layouts.app')
@section('title', @$data['title'])



@section('content')
    <x-container :title="@$data['title']">

        <div class="ot-card">
            <div class="d-flex align-items-center justify-content-between mb-25">
                <h3 class="card-title d-flex align-items-center gap-10 mb-0">
                    <x-common.icons name="document-copy" class="text-primary" size="24" stroke-width="1.5" />
                    {{ _trans('common.Import Details') }}
                </h3>
                <div class="d-flex align-items-center gap-8">
                    @if ($import->isInProgress())
                        <form action="{{ route('bulk-import.history.cancel', $import->id) }}" method="POST"
                            class="d-inline-block">
                            @csrf
                            <button type="submit" class="btn-cancel-soft d-flex align-items-center gap-6">
                                <x-common.icons name="close-circle" class="" size="16" stroke-width="1.5" />
                                {{ _trans('common.Cancel Import') }}
                            </button>
                        </form>
                    @endif

                    @if ($import->isFailed() || $import->status === 'cancelled')
                        <form action="{{ route('bulk-import.history.retry', $import->id) }}" method="POST"
                            class="d-inline-block">
                            @csrf
                            <button type="submit" class="btn-primary-fill d-flex align-items-center gap-6">
                                <x-common.icons name="refresh" class="text-white" size="16" stroke-width="1.5" />
                                {{ _trans('common.Retry Import') }}
                            </button>
                        </form>
                    @endif

                    <a href="{{ route('bulk-import.history') }}"
                        class="btn-primary-outline d-flex align-items-center gap-6">
                        <x-common.icons name="arrow-left" class="" size="16" stroke-width="1.5" />
                        {{ _trans('common.Back to List') }}
                    </a>
                </div>
            </div>
            @if ($errors->has('errors'))
                <div class="ot-card bg-danger-soft border-left-danger p-16 mb-20">
                    <div class="d-flex align-items-start gap-12">
                        <x-common.icons name="close-circle" class="text-danger flex-shrink-0 mt-1" size="18"
                            stroke-width="1.5" />
                        <div>
                            <h6 class="text-danger fw-semibold mb-8">Error</h6>
                            <p class="text-danger mb-0">{{ $errors->first('errors') }}</p>
                        </div>
                    </div>
                </div>
            @endif

            @if (session('success'))
                <div class="ot-card bg-success-soft border-left-success p-16 mb-20">
                    <div class="d-flex align-items-start gap-12">
                        <x-common.icons name="check-circle" class="text-success flex-shrink-0 mt-1" size="18"
                            stroke-width="1.5" />
                        <div>
                            <h6 class="text-success fw-semibold mb-8">Success</h6>
                            <p class="text-success mb-0">{{ session('success') }}</p>
                        </div>
                    </div>
                </div>
            @endif

            @if (session('error'))
                <div class="ot-card bg-danger-soft border-left-danger p-16 mb-20">
                    <div class="d-flex align-items-start gap-12">
                        <x-common.icons name="close-circle" class="text-danger flex-shrink-0 mt-1" size="18"
                            stroke-width="1.5" />
                        <div>
                            <h6 class="text-danger fw-semibold mb-8">Error</h6>
                            <p class="text-danger mb-0">{{ session('error') }}</p>
                        </div>
                    </div>
                </div>
            @endif

            <div class="row g-y-20 mb-4">
                <div class="col-md-6">
                    <div class="ot-card h-100">
                        <div class="d-flex align-items-center gap-10 mb-20">
                            <x-common.icons name="info-circle" class="text-primary" size="20" stroke-width="1.5" />
                            <h6 class="mb-0">{{ _trans('common.Import Information') }}</h6>
                        </div>
                        <div class="list-widget">
                            <div class="d-flex justify-content-between align-items-center py-12 border-bottom">
                                <span class="text-subtitle">{{ _trans('common.ID') }}:</span>
                                <span class="fw-semibold">{{ $import->id }}</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center py-12 border-bottom">
                                <span class="text-subtitle">{{ _trans('common.Status') }}:</span>
                                <x-common.tyne-badge :status="$import->status === 'completed' ? 'success' : ($import->status === 'failed' ? 'danger' : ($import->status === 'processing' ? 'info' : 'warning'))" :text="ucfirst($import->status)" id="status-badge" />
                            </div>
                            <div class="d-flex justify-content-between align-items-center py-12 border-bottom">
                                <span class="text-subtitle">{{ _trans('common.Model') }}:</span>
                                <span class="fw-semibold">{{ $import->model_display_name }}</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center py-12 border-bottom">
                                <span class="text-subtitle">{{ _trans('common.File Name') }}:</span>
                                <span class="fw-semibold">{{ $import->file_name }}</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center py-12 border-bottom">
                                <span class="text-subtitle">{{ _trans('common.File Size') }}:</span>
                                <span class="fw-semibold">{{ round($import->file_size / 1024, 2) }} KB</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center py-12 border-bottom">
                                <span class="text-subtitle">{{ _trans('common.Created At') }}:</span>
                                <span class="fw-semibold">{{ $import->created_at->format('Y-m-d H:i:s') }}</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center py-12 border-bottom">
                                <span class="text-subtitle">{{ _trans('common.Started At') }}:</span>
                                <span class="fw-semibold" id="started-at">
                                    {{ $import->started_at ? $import->started_at->format('Y-m-d H:i:s') : 'N/A' }}
                                </span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center py-12 border-bottom">
                                <span class="text-subtitle">{{ _trans('common.Completed At') }}:</span>
                                <span class="fw-semibold" id="completed-at">
                                    {{ $import->completed_at ? $import->completed_at->format('Y-m-d H:i:s') : 'N/A' }}
                                </span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center py-12">
                                <span class="text-subtitle">{{ _trans('common.Duration') }}:</span>
                                <span class="fw-semibold" id="duration">{{ $import->duration ?? 'N/A' }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="ot-card h-100">
                        <div class="d-flex align-items-center gap-10 mb-20">
                            <x-common.icons name="chart-analytics" class="text-primary" size="20"
                                stroke-width="1.5" />
                            <h6 class="mb-0">{{ _trans('common.Import Results') }}</h6>
                        </div>
                        <div id="import-results">
                            @if ($import->total_rows > 0)
                                <div class="mb-20">
                                    <div class="d-flex align-items-center justify-content-between mb-8">
                                        <h6 class="mb-0">{{ _trans('common.Progress') }}</h6>
                                        <span class="text-12 text-subtitle">
                                            <span
                                                id="progress-percentage">{{ round(($import->imported_rows / $import->total_rows) * 100, 1) }}</span>%
                                            complete
                                        </span>
                                    </div>
                                    <div class="progress" style="height: 8px;">
                                        @php
                                            $progress = ($import->imported_rows / $import->total_rows) * 100;
                                        @endphp
                                        <div class="progress-bar bg-success" role="progressbar"
                                            style="width: {{ $progress }}%" aria-valuenow="{{ $progress }}"
                                            aria-valuemin="0" aria-valuemax="100" id="progress-bar">
                                        </div>
                                    </div>
                                </div>

                                <div class="row g-y-16">
                                    <div class="col-6">
                                        <div class="ot-card border radius-8 p-16 text-center">
                                            <div class="text-20 fw-bold text-primary" id="total-rows">
                                                {{ $import->total_rows }}</div>
                                            <div class="text-12 text-subtitle">{{ _trans('common.Total Rows') }}</div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="ot-card border radius-8 p-16 text-center">
                                            <div class="text-20 fw-bold text-success" id="imported-rows">
                                                {{ $import->imported_rows }}</div>
                                            <div class="text-12 text-subtitle">{{ _trans('common.Imported') }}</div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="ot-card border radius-8 p-16 text-center">
                                            <div class="text-20 fw-bold text-warning" id="skipped-rows">
                                                {{ $import->skipped_rows }}</div>
                                            <div class="text-12 text-subtitle">{{ _trans('common.Skipped') }}</div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="ot-card border radius-8 p-16 text-center">
                                            <div class="text-20 fw-bold text-danger" id="error-rows">
                                                {{ $import->error_rows }}</div>
                                            <div class="text-12 text-subtitle">{{ _trans('common.Errors') }}</div>
                                        </div>
                                    </div>
                                </div>
                            @else
                                <div class="ot-card bg-primary-soft border-left-primary p-20 text-center">
                                    <x-common.icons name="document-copy" class="text-primary mb-8" size="32"
                                        stroke-width="1.5" />
                                    <p class="text-subtitle mb-0">{{ _trans('common.No import data available yet') }}</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            @if ($import->error_message)
                <div class="ot-card bg-danger-soft border-left-danger mb-20">
                    <div class="d-flex align-items-center gap-10 mb-16">
                        <x-common.icons name="close-circle" class="text-danger" size="20" stroke-width="1.5" />
                        <h6 class="mb-0 text-danger">{{ _trans('common.Error Information') }}</h6>
                    </div>
                    <div class="mb-16">
                        <h6 class="mb-8">{{ _trans('common.Error Message') }}</h6>
                        <div class="ot-card bg-white border p-16" id="error-message">
                            <p class="text-danger mb-0">{{ $import->error_message }}</p>
                        </div>
                    </div>

                    @if ($import->stack_trace)
                        <div>
                            <h6 class="mb-8">{{ _trans('common.Stack Trace') }}</h6>
                            <div class="ot-card bg-white border p-16" id="stack-trace">
                                <pre class="text-12 text-subtitle mb-0" style="max-height: 300px; overflow-y: auto; white-space: pre-wrap;">{{ $import->stack_trace }}</pre>
                            </div>
                        </div>
                    @endif
                </div>
            @endif

            @if (!$import->isInProgress() && !$import->isFailed())
                <div class="d-flex justify-content-end mt-20">
                    <form action="{{ route('bulk-import.history.destroy', $import->id) }}" method="POST"
                        onsubmit="return confirm('{{ _trans('common.Are you sure you want to delete this import?') }}')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn-cancel-soft d-flex align-items-center gap-6">
                            <x-common.icons name="trash" class="" size="16" stroke-width="1.5" />
                            {{ _trans('common.Delete Import') }}
                        </button>
                    </form>
                </div>
            @endif
        </div>
    </x-container>
@endsection

@pushonce('script')
    <script src="{{ asset('vendor/bulk-import/js/import-status.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Elements to update
            const statusBadge = document.getElementById('import-status-badge');
            const progressBar = document.getElementById('import-progress-bar');
            const importedRowsEl = document.getElementById('import-imported-rows');
            const skippedRowsEl = document.getElementById('import-skipped-rows');
            const errorRowsEl = document.getElementById('import-error-rows');
            const totalRowsEl = document.getElementById('import-total-rows');
            const durationEl = document.getElementById('import-duration');
            const updatedAtEl = document.getElementById('import-updated-at');
            const errorMessageEl = document.getElementById('import-error-message');

            // Status badge colors
            const statusColors = {
                'pending': 'bg-yellow-500',
                'processing': 'bg-blue-500',
                'completed': 'bg-green-500',
                'failed': 'bg-red-500',
                'cancelled': 'bg-gray-500'
            };

            // Only initialize tracking for in-progress imports
            const importStatus = '{{ $import->status }}';
            if (['pending', 'processing'].includes(importStatus)) {
                const tracker = new ImportStatusTracker({
                    importId: {{ $import->id }},
                    userId: {{ Auth::id() }},
                    onUpdate: function(data) {
                        // Update status badge
                        if (statusBadge) {
                            statusBadge.textContent = data.status;
                            // Remove existing background classes
                            Object.values(statusColors).forEach(color => {
                                statusBadge.classList.remove(color);
                            });
                            // Add new background class
                            statusBadge.classList.add(statusColors[data.status] || 'bg-gray-500');
                        }

                        // Update progress bar
                        if (progressBar) {
                            progressBar.style.width = data.progress_percentage + '%';
                            progressBar.setAttribute('aria-valuenow', data.progress_percentage);
                            progressBar.textContent = data.progress_percentage + '%';
                        }

                        // Update row counts
                        if (importedRowsEl) importedRowsEl.textContent = data.imported_rows;
                        if (skippedRowsEl) skippedRowsEl.textContent = data.skipped_rows;
                        if (errorRowsEl) errorRowsEl.textContent = data.error_rows;
                        if (totalRowsEl) totalRowsEl.textContent = data.total_rows;

                        // Update duration
                        if (durationEl) durationEl.textContent = data.duration || '-';

                        // Update error message if there is one
                        if (errorMessageEl && data.error_message) {
                            errorMessageEl.textContent = data.error_message;
                            errorMessageEl.parentElement.classList.remove('hidden');
                        }

                        // Update last updated time
                        if (updatedAtEl) {
                            const date = new Date(data.updated_at);
                            updatedAtEl.textContent = date.toLocaleString();
                        }

                        // Reload page when status changes to a final state to show all updated info
                        if (['completed', 'failed', 'cancelled'].includes(data.status)) {
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        }
                    }
                });

                // Clean up the tracker when leaving the page
                window.addEventListener('beforeunload', function() {
                    tracker.destroy();
                });
            }
        });
    </script>
@endpushonce
