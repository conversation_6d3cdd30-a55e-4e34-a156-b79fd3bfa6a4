<?php

namespace Modules\SingleDeviceLogin\Http\Controllers;

use App\Repositories\DesignationRepository;
use App\Repositories\UserRepository;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\SingleDeviceLogin\Models\DeviceLog;
use Throwable;

class SingleDeviceLoginController extends Controller
{
    protected $user;

    protected $designation;

    public function __construct(UserRepository $user, DesignationRepository $designation)
    {
        $this->user = $user;
        $this->designation = $designation;
    }

    public function index(Request $request)
    {
        try {
            $data['title'] = _trans('common.Employee Device List');
            $data['collection'] = DeviceLog::with('user')->paginate(10);

            return view('singledevicelogin::index')->with($data);
        } catch (Exception $exception) {
            return redirect()->back()->with('error', $exception->getMessage());
        }
    }

    public function resetDevice($user_id, $device)
    {
        try {
            $result = DeviceLog::where('user_id', $user_id)
                ->where('last_login_device', $device)
                ->delete();

            if ($result) {
                return redirect()->back()->with('success', 'Device reset successfully.');
            } else {
                return redirect()->back()->with('error', 'Something went wrong.');
            }
        } catch (Throwable $th) {
            return redirect()->back()->with('error', $th->getMessage());
        }
    }
}
