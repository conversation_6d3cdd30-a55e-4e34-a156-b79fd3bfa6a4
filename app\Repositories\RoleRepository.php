<?php

namespace App\Repositories;

use App\Models\Role;
use App\Models\RoleWisePermission;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class RoleRepository
{
    public function __construct(
        protected Role $model,
        protected RoleWisePermission $roleWisePermission
    ) {}

    public function getPaginateData($request, $fields = ['*'])
    {
        return $this->model->select($fields)
            ->latest('id')
            ->withCount('users') // total user count per role
            ->with(['users' => function ($q) {
                $q->inRandomOrder()->limit(3); // get 3 random users per role
            }, 'permissions'])
            ->when($request->search, function ($query) use ($request) {
                $query->where(function ($q) use ($request) {
                    $q->where('name', 'like', '%'.$request->search.'%');
                });
            })
            ->when($request->status, function ($query) use ($request) {
                $query->where('status', $request->status);
            })
            ->paginate($request->limit ?? 10);
    }

    public function getAll()
    {
        return $this->model->query()
            ->select('id', 'name', 'slug')
            ->where('status', 'active')
            ->where('slug', '!=', 'superadmin')
            ->where('permissions', '!=', '')
            ->get();
    }

    public function store($request)
    {
        return DB::transaction(function () use ($request) {
            $role = new $this->model;
            $role->name = $request['name'];
            $role->slug = Str::slug($request['name'], '-');
            $role->status = $request['status'];
            $role->web_login = $request['web_login'] ?? 0;
            $role->app_login = $request['app_login'] ?? 0;
            $role->save();

            $this->roleWisePermission->create([
                'role_id' => $role->id,
                'permissions' => $request['permissions'] ?? [],
            ]);

            return $role;
        });
    }

    public function update($id, $request)
    {
        return DB::transaction(function () use ($id, $request) {
            $role = $this->show($id);

            // Update role fields
            $role->name = $request['name'];
            $role->slug = Str::slug($request['name'], '-');
            $role->status = $request['status'];
            $role->web_login = $request['web_login'] ?? 0;
            $role->app_login = $request['app_login'] ?? 0;
            $role->save();

            // Update or create RoleWisePermission
            $this->roleWisePermission->updateOrCreate(
                ['role_id' => $role->id],
                ['permissions' => $request['permissions'] ?? []]
            );

            // Update user permissions
            foreach ($role->users as $user) {
                $user->permissions()->updateOrCreate(
                    ['user_id' => $user->id],
                    ['permissions' => $request['permissions'] ?? []]
                );
            }

            return $role;
        });
    }

    public function show($id, $with = [])
    {
        return $this->model->with($with)->find($id);
    }

    public function destroy($id)
    {
        $role = $this->model->findOrFail($id);
        if (in_array($role->id, [1, 2, 3])) {
            return false;
        } else {
            return $role->delete();
        }
    }
}
