<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

use function redirect;

class RedirectIfAuthenticated
{
    public function handle(Request $request, Closure $next, ...$guards)
    {
        $guards = empty($guards) ? [null] : $guards;

        foreach ($guards as $guard) {
            if (Auth::guard($guard)->check()) {
                $redirectMap = [
                    'admin_dashboard' => 'admin.dashboard',
                    'hr_dashboard' => 'hr.dashboard',
                    'staff_dashboard' => 'staff.dashboard',
                ];

                foreach ($redirectMap as $permission => $routeName) {
                    if (hasPermission($permission)) {
                        return redirect()->route($routeName);
                    }
                }

                // Fallback if no matching permission
                return redirect()->route('staff.dashboard');
            }
        }

        return $next($request);
    }
}
