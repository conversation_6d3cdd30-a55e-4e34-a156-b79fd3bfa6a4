<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON>les\EmployeePerformance\Http\Controllers\AwardController;
use Mo<PERSON>les\EmployeePerformance\Http\Controllers\AwardTypeController;
use Modules\EmployeePerformance\Http\Controllers\EmployeePerformanceController;

Route::middleware(['web', 'demo.mode', 'xss', 'TimeZone', 'admin'])->group(function () {
    Route::controller(EmployeePerformanceController::class)->prefix('employee-performance')->name('employee-performance.')->group(function () {
        Route::get('/', 'index')->name('index')->middleware('PermissionCheck:performance_read');
        Route::get('/create', 'create')->name('create')->middleware('PermissionCheck:performance_create');
        Route::post('/store', 'store')->name('store')->middleware('PermissionCheck:performance_create');
        Route::get('/edit/{id}', 'edit')->name('edit')->middleware('PermissionCheck:performance_update');
        Route::put('/update/{id}', 'update')->name('update')->middleware('PermissionCheck:performance_update');
        Route::get('/delete/{id}', 'destroy')->name('destroy')->middleware('PermissionCheck:performance_delete');
    });

    Route::controller(AwardTypeController::class)->prefix('award/type')->name('award_type.')->group(function () {
        Route::any('/', 'index')->name('index')->middleware('PermissionCheck:award_type_read');
        Route::post('store', 'store')->name('store')->middleware('PermissionCheck:award_type_create');
        Route::put('update/{id}', 'update')->name('update')->middleware('PermissionCheck:award_type_update');
        Route::get('delete/{id}', 'delete')->name('delete')->middleware('PermissionCheck:award_type_delete');
    });

    Route::controller(AwardController::class)->prefix('award')->name('award.')->group(function () {
        Route::any('/', 'index')->name('index')->middleware('PermissionCheck:award_read');
        Route::get('/get-unseen-award', 'getUnseenAward')->name('unseen')->middleware('PermissionCheck:award_read');
        Route::post('/seen-award', 'seenAward')->name('seen')->middleware('PermissionCheck:award_read');
        Route::get('create', 'create')->name('create')->middleware('PermissionCheck:award_create');
        Route::post('store', 'store')->name('store')->middleware('PermissionCheck:award_create');
        Route::get('edit/{id}', 'edit')->name('edit')->middleware('PermissionCheck:award_update');
        Route::put('update/{id}', 'update')->name('update')->middleware('PermissionCheck:award_update');
        Route::get('delete/{id}', 'delete')->name('delete')->middleware('PermissionCheck:award_delete');
        Route::get('view/{id}', 'view')->name('view')->middleware('PermissionCheck:award_read');
    });
});
