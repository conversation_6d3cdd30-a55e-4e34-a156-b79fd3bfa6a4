<?php

namespace App\Traits;

use App\Events\TodayActivity;
use App\Models\Attendance\Attendance;
use App\Models\User;
use App\Services\FileUploadService;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\AreaBasedAttendance\Entities\LocationBind;
use Modules\IpBasedAttendance\Entities\IpSetup;
use Modules\IpBasedAttendance\Entities\UserIpBind;
use Modules\Tardy\Http\Repositories\TardyRecordRepository;

trait LogAttendance
{
    protected $now = null;

    protected $user = null;

    public function __construct(protected Attendance $attendance) {}

    public function syncAttendance($type, $request = null, $title = null, $reason = null)
    {
        $this->now = $request->date ? $this->customNow($request->date, $request->time) : now();
        $this->user = $request->user_id ? User::find($request->user_id) : Auth::user();

        if ($type != 'check_in') {
            $attendance = Attendance::where('user_id', $this->user->id)
                ->where('date', '>=', $request->date ?? date('Y-m-d', strtotime('-1 days')))
                ->whereNotNull('check_in')
                ->when(! $request->date, fn ($query) => $query->whereNull('check_out'))
                ->first();
        }

        match ($type) {
            'check_in' => $this->handleCheckIn($request),
            'break' => $this->handleBreak($attendance, $title, $reason),
            'back' => $this->handleBack($attendance),
            'check_out' => $this->handleCheckOut($attendance, $request),
            default => null,
        };

        if ($type != 'check_in') {
            $this->finalizeAttendance($type, $attendance, null, $request);
        }

        // Broadcast event
        try {
            broadcast(new TodayActivity)->toOthers();
        } catch (Exception $e) {
            Log::info('Broadcast failed: ');
        }
    }

    protected function handleCheckIn($request = null)
    {
        return DB::transaction(function () use ($request) {
            if (! $request->date) {
                // Auto check out previous day
                $this->autoCheckOut();

                $hasAttendance = Attendance::where('user_id', $this->user->id)
                    ->where('check_in_info->auto_check_out_time', '>=', $this->now->toDateTimeString())
                    ->latest('id')
                    ->first();

                // Check if already checked in
                if ($hasAttendance) {
                    $lastCheckOutDate = Carbon::parse($hasAttendance->check_out)->format('Y-m-d');
                    $lastCheckOutTime = Carbon::parse($hasAttendance->check_out)->format('H:i');

                    $hasAttendance->check_out = null;
                    $hasAttendance->checkout_status = null;
                    $hasAttendance->early_exit_duration = null;
                    $hasAttendance->check_out_info = null;
                    $hasAttendance->status = 'check_in';

                    $hasAttendance->save();

                    // Start break from last checkout time
                    $this->now = $this->customNow($lastCheckOutDate, $lastCheckOutTime);
                    $this->handleBreak($hasAttendance, null, 'Check out converted into break');
                    $this->finalizeAttendance('break', $hasAttendance, null, $request);

                    // End break by current time
                    $this->now = now();
                    $this->handleBack($hasAttendance);
                    $this->finalizeAttendance('back', $hasAttendance, null, $request);

                    return true;
                }
            }

            $attendanceConfig = $this->user->attendanceConfig;
            $dutySchedule = $this->user->dutySchedules()?->first();

            if (! $dutySchedule) {
                throwException('Your are not assigned to any schedule!');
            }

            if (! $this->scheduleExist($dutySchedule, $this->now->format('H:i:s'))) {
                throwException(__('You do not have any schedule now. Allowed time is from :from to :to.', [
                    'from' => Carbon::parse($dutySchedule->start_time)->subMinutes($dutySchedule->early_checkin_buffer)->format('h:i A'),
                    'to' => formatDateTime($dutySchedule->end_time, 'h:i A'),
                ]));
            }

            // Check location
            if (isModuleActive('AreaBasedAttendance') && ! $attendanceConfig->is_free_location && ! $this->locationCheck($request)) {
                throwException('Your location is not valid!');
            }

            // Check ip
            if (isModuleActive('IpBasedAttendance') && ! $attendanceConfig->is_free_ip && ! $this->isIpRestricted()) {
                throwException('Your ip address is not valid!');
            }

            $today = $this->getAttendanceDate($dutySchedule);

            // Check weekend
            if ($this->isWeekend($attendanceConfig->weekends)) {
                throwException('Today is a weekend!');
            }

            // Check holiday
            if ($this->user->department?->isHoliday($today)) {
                throwException('Today is a holiday!');
            }

            // Check leave
            if ($this->user->approvedLeaveDates($today)) {
                throwException('You are on leave today!');
            }

            $attendanceStatus = $this->checkInStatus($dutySchedule, $today);
            $checkInStatus = $attendanceStatus['checkin_status'];
            $lateDuration = $attendanceStatus['late_duration'];

            $formateDateTime = $this->getFormateDateTime($dutySchedule->end_time);

            $authCheckOutTime = $formateDateTime->addMinutes((int) $dutySchedule->max_over_time)->format('Y-m-d H:i:s');

            $checkInInfo = [
                'schedule_start_time' => $dutySchedule->start_time,
                'schedule_end_time' => $dutySchedule->end_time,
                'checkin_consider_time' => $dutySchedule->checkin_consider_time,
                'checkout_consider_time' => $dutySchedule->checkout_consider_time,
                'auto_check_out_time' => $authCheckOutTime,
                'ip_address' => @getUserIp(),
                'latitude' => @$request->latitude ?? null,
                'longitude' => @$request->longitude ?? null,
                'location' => getUserAddress($request->latitude, $request->longitude),
            ];

            $attendance = new $this->attendance;
            $attendance->user_id = $this->user->id;
            $attendance->date = $this->now->format('Y-m-d');
            $attendance->check_in = $this->now->toDateTimeString();
            $attendance->checkin_status = $checkInStatus;
            $attendance->late_duration = $lateDuration;
            $attendance->check_in_info = $checkInInfo;
            $attendance->duty_schedule_id = $dutySchedule->id;
            $attendance->status = 'check_in';

            // Selfie image
            if (isEnableSelfieBaseAttendance() && $request->selfie_image) {
                $checkInImage = [
                    'disk' => config('filesystems.default'),
                    'file' => FileUploadService::file($request->selfie_image, 'attendance'),
                ];

                $attendance->check_in_image = json_encode($checkInImage);
            }

            $attendance->save();

            // Check tardy is applicable
            if (isModuleActive('Tardy') && $checkInStatus === 'late') {
                $this->tardyLog($attendance->id, 'check_in', $lateDuration, $request->reason);
            }

            return $attendance;
        });
    }

    protected function handleBreak($model, $title = 'Break', $reason = '')
    {
        if (! $model || $model->status !== 'check_in') {
            return;
        }

        $logs = collect($model->attendance_log ?? []);
        $now = $this->now;

        $logs->push([
            'start_time' => $now->format('H:i:s'),
            'end_time' => null,
            'difference' => null,
            'type' => 'break',
            'title' => $title,
            'reason' => $reason,
        ]);

        $model->update([
            'attendance_log' => $logs->toArray(),
            'status' => 'break',
        ]);
    }

    protected function handleBack($model)
    {
        if (! $model || $model->status !== 'break') {
            return;
        }

        $logs = collect($model->attendance_log ?? []);
        $now = $this->now;

        $lastBreakIndex = $logs->keys()->last(fn ($key) => $logs[$key]['type'] === 'break' && empty($logs[$key]['end_time']));

        // Add End Log for Break
        if (! is_null($lastBreakIndex)) {
            $start = Carbon::createFromFormat('H:i:s', $logs[$lastBreakIndex]['start_time']);
            $break = $logs[$lastBreakIndex];
            $break['end_time'] = $now->format('H:i:s');
            $break['difference'] = $start->diffInMinutes($now);
            $logs->put($lastBreakIndex, $break);
        }

        $model->update([
            'attendance_log' => $logs->toArray(),
            'status' => 'check_in',
        ]);
    }

    protected function handleCheckOut($attendance, $request = null)
    {
        return DB::transaction(function () use ($attendance, $request) {
            if (! $attendance) {
                throwException('Attendance not found!');
            }

            $authUser = $this->user->load('attendanceConfig');
            $attendanceConfig = $authUser->attendanceConfig;
            $attendanceStatus = $this->checkOutStatus($attendance->check_in_info['schedule_end_time'], $attendance->check_in_info['checkout_consider_time']);
            $checkoutStatus = $attendanceStatus['checkout_status'];
            $earlyExitDuration = $attendanceStatus['early_exit_duration'];

            // Check location
            if (isModuleActive('AreaBasedAttendance') && ! $attendanceConfig->is_free_location && ! $this->locationCheck($request)) {
                throwException('Your location is not valid!');
            }

            // Check IP
            if (isModuleActive('IpBasedAttendance') && ! $attendanceConfig->is_free_ip && ! $this->isIpRestricted()) {
                throwException('Your ip address is not valid!');
            }

            $checkOutInfo = [
                'ip_address' => getUserIp(),
                'latitude' => @$request->latitude,
                'longitude' => @$request->longitude,
                'location' => getUserAddress(@$request->latitude, @$request->longitude),
            ];

            $checkOutTime = $this->now->toDateTimeString();

            $attendance->check_out = $checkOutTime;
            $attendance->stay_time = $this->calculateStayTime($attendance->check_in, $checkOutTime);
            $attendance->checkout_status = $checkoutStatus;
            $attendance->early_exit_duration = $earlyExitDuration;
            $attendance->check_out_info = $checkOutInfo;
            $attendance->status = 'check_out';

            // Selfie image
            if (isEnableSelfieBaseAttendance() && $request->selfie_image) {
                $checkOutImage = [
                    'disk' => config('filesystems.default'),
                    'file' => FileUploadService::file($request->selfie_image, 'attendance'),
                ];

                $attendance->check_out_image = json_encode($checkOutImage);
            }

            // Log
            $logs = collect($attendance->attendance_log ?? []);
            $now = $this->now;

            $lastBreakIndex = $logs->keys()->last(fn ($key) => $logs[$key]['type'] === 'break' && empty($logs[$key]['end_time']));
            // Add End Log for Break
            if (! is_null($lastBreakIndex)) {
                $start = Carbon::createFromFormat('H:i:s', $logs[$lastBreakIndex]['start_time']);
                $break = $logs[$lastBreakIndex];
                $break['end_time'] = $now->format('H:i:s');
                $break['difference'] = $start->diffInMinutes($now);
                $logs->put($lastBreakIndex, $break);
            }

            $attendance->attendance_log = $logs->toArray();
            $attendance->update();

            return $attendance;
        });
    }

    protected function autoCheckOut()
    {
        $attendance = Attendance::where('user_id', $this->user->id)
            ->whereIn('status', ['check_in', 'break'])
            ->whereNull('check_out')
            ->latest('id')
            ->first();

        if ($attendance) {
            // Log
            $logs = collect($attendance->attendance_log ?? []);
            $now = Carbon::parse($attendance->date.' '.$attendance->check_in_info['schedule_end_time']);

            $lastBreakIndex = $logs->keys()->last(fn ($key) => $logs[$key]['type'] === 'break' && empty($logs[$key]['end_time']));
            // Add End Log for Break
            if (! is_null($lastBreakIndex)) {
                // $start = Carbon::createFromFormat('H:i:s', $logs[$lastBreakIndex]['start_time'])
                //     ->setDate($now->year, $now->month, $now->day);

                $start = $this->getFormateDateTime($logs[$lastBreakIndex]['start_time']);

                $break = $logs[$lastBreakIndex];
                $break['end_time'] = $now->format('H:i:s');
                $break['difference'] = $start->diffInMinutes($now);

                $logs->put($lastBreakIndex, $break);
            }

            $attendance->attendance_log = $logs->toArray();
            $attendance->status = 'check_out';
            $attendance->stay_time = $this->calculateStayTime($attendance->check_in, $now);
            $attendance->update();

            $this->finalizeAttendance('check_out', $attendance, $now);
        }
    }

    public function calculateStayTime($checkInTime, $checkOutTime)
    {
        $checkIn = Carbon::parse($checkInTime);
        $checkOut = Carbon::parse($checkOutTime);

        $diffInSeconds = $checkOut->timestamp - $checkIn->timestamp;

        return $diffInSeconds >= 0 ? gmdate('H:i:s', $diffInSeconds) : '00:00:00';
    }

    protected function finalizeAttendance($type, $model, $time = null, $request = null)
    {
        $logs = collect($model->attendance_log ?? []);

        $now = $time ?? $this->now;

        $breakLogs = $logs->where('type', 'break')->values();

        // $schedule_start = Carbon::createFromFormat('H:i:s', $model->check_in_info['schedule_start_time'] ?? '00:00:00');
        // $schedule_end = Carbon::createFromFormat('H:i:s', $model->check_in_info['schedule_end_time'] ?? '23:59:59');

        $schedule_start = $this->getFormateDateTime($model->check_in_info['schedule_start_time'] ?? '00:00:00');
        $schedule_end = $this->getFormateDateTime($model->check_in_info['schedule_end_time'] ?? '23:59:59');

        $consider_buffer = (int) ($model->check_in_info['checkout_consider_time'] ?? 0);

        // $completedBreaks = $breakLogs->whereNotNull('end_time')->filter(
        //     fn($b) => Carbon::createFromFormat('H:i:s', $b['end_time'])->lte($schedule_end)
        // );

        $completedBreaks = $breakLogs->whereNotNull('end_time')->filter(
            fn ($b) => $this->getFormateDateTime($b['end_time'])->lte($schedule_end)
        );

        $break_time = $completedBreaks->sum('difference');

        $checkIn = $model->check_in ? Carbon::parse($model->check_in) : null;
        $checkOut = $model->check_out ? Carbon::parse($model->check_out) : ($type === 'check_out' ? $now : null);

        $nowOrOut = $checkOut ?? $now;

        $worked_time = $checkIn ? $checkIn->diffInMinutes($nowOrOut) - $break_time : 0;
        $stay_time = $checkIn ? $checkIn->diffInMinutes($nowOrOut) : 0;
        $late_duration = ($checkIn && $checkIn->gt($schedule_start)) ? $schedule_start->diffInMinutes($checkIn) : 0;

        $early_exit = 0;
        $over_time = 0;
        $checkout_status = null;

        if ($checkOut) {
            if ($time) {
                // For auto check out
                $checkout_status = 'auto_check_out';
            } else {
                if ($checkOut->lt($schedule_end)) {
                    $early_exit = $checkOut->diffInMinutes($schedule_end);
                    $checkout_status = 'left_early';
                } else {
                    $considered = $schedule_end->copy()->addMinutes($consider_buffer);
                    $checkout_status = $checkOut->lte($considered) ? 'left_timely' : 'left_later';

                    if ($checkOut->gt($schedule_end)) {
                        $over_time = $schedule_end->diffInMinutes($checkOut);
                    }
                }
            }
        }

        $fmt = fn ($min) => $min <= 0 ? '00:00:00' : sprintf('%02d:%02d:%02d', floor($min / 60), $min % 60, 0);

        $data = [
            'attendance_log' => $breakLogs->toArray(),
            'status' => $model->status,
            'break_time' => $fmt($break_time),
            'worked_time' => $fmt($worked_time),
            'stay_time' => $fmt($stay_time),
            'late_duration' => $fmt($late_duration),
            'early_exit_duration' => $fmt($early_exit),
            'over_time' => $fmt($over_time),
        ];

        if ($type === 'check_out') {
            $data['check_out'] = $now->format('Y-m-d H:i:s');
            $data['checkout_status'] = $checkout_status;
        }

        $model->update($data);

        if (isModuleActive('Tardy') && ($type === 'back' || $type === 'check_out')) {
            $duration = $type === 'back' ? $model->break_time : $model->early_exit_duration;
            $type = $type === 'back' ? 'break' : $type;

            if ($type === 'break' || $model->checkout_status === 'left_early') {
                $this->tardyLog($model->id, $type, $duration, $request->reason);
            }
        }
    }

    public function tardyLog($attendanceId, $type, $duration, $reason = null)
    {
        (new TardyRecordRepository)->store($attendanceId, $type, $duration, $reason);
    }

    public function scheduleExist($dutySchedule, $checkInTime)
    {
        $startTime = Carbon::parse($dutySchedule->start_time)
            ->subMinutes($dutySchedule->early_checkin_buffer)
            ->format('H:i:s');

        $endTime = Carbon::parse($dutySchedule->end_time)->format('H:i:s');

        // Check if shift spans over midnight
        if ($startTime > $endTime) {
            // Overnight shift: match if check-in is >= start OR <= end
            return $checkInTime >= $startTime || $checkInTime <= $endTime;
        } else {
            // Normal shift
            return $checkInTime >= $startTime && $checkInTime <= $endTime;
        }
    }

    public function locationCheck($request)
    {
        $locations = LocationBind::active()
            ->where(function ($query) {
                $query->whereNull('user_id')->orWhere('user_id', $this->user->id);
            })->get();

        foreach ($locations ?? [] as $location) {
            $distance = $this->distanceCalculate($request->latitude, $request->longitude, $location->latitude, $location->longitude);

            if ($distance <= intval($location->distance)) {
                return true;
            }
        }

        return false;
    }

    public function distanceCalculate($lat1, $lon1, $lat2, $lon2)
    {
        $earthRadius = 6371000; // Earth's radius in meters

        // Convert degrees to radians
        $lat1Rad = deg2rad($lat1);
        $lon1Rad = deg2rad($lon1);
        $lat2Rad = deg2rad($lat2);
        $lon2Rad = deg2rad($lon2);

        // Haversine formula
        $latDiff = $lat2Rad - $lat1Rad;
        $lonDiff = $lon2Rad - $lon1Rad;

        $a = sin($latDiff / 2) ** 2 +
        cos($lat1Rad) * cos($lat2Rad) *
        sin($lonDiff / 2) ** 2;

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        // Distance in meters
        return $earthRadius * $c;
    }

    public function isIpRestricted(): bool
    {
        $ipAddress = getUserIp();

        return UserIpBind::whereJsonContains('ip_addresses', $ipAddress)->exists()
        || IpSetup::active()->where('ip_address', $ipAddress)->exists();
    }

    public function getAttendanceDate($dutySchedule)
    {
        $now = $this->now;
        $shiftStart = $dutySchedule->start_time;
        $shiftEnd = $dutySchedule->end_time;
        $maxOvertimeMin = $dutySchedule->max_over_time;
        $endOnSameDay = $dutySchedule->end_on_same_date;

        if ($endOnSameDay) {
            return $now->format('Y-m-d');
        }

        $nowTime = Carbon::parse($now);

        // Assume shift is scheduled for the same date as $now
        $baseDate = $nowTime->format('Y-m-d');

        $shiftStartDateTime = Carbon::parse("{$baseDate} {$shiftStart}");
        $shiftEndDateTime = Carbon::parse("{$baseDate} {$shiftEnd}");

        // If shift crosses midnight (e.g., 23:00 - 07:00), add 1 day to end
        if ($shiftEndDateTime <= $shiftStartDateTime) {
            $shiftEndDateTime->modify('+1 day');
        }

        // Extend shift end by max overtime
        $maxEndTime = clone $shiftEndDateTime;
        $maxEndTime->modify("+{$maxOvertimeMin} minutes");

        // If now is before shift start, assume it belongs to previous day's shift
        if ($nowTime < $shiftStartDateTime) {
            $shiftStartDateTime->modify('-1 day');

            return $shiftStartDateTime->format('Y-m-d');
        }

        // If now is within shift + overtime window, return shift start date
        if ($nowTime <= $maxEndTime) {
            return $shiftStartDateTime->format('Y-m-d');
        }

        // Otherwise, belongs to next day's shift
        return $shiftStartDateTime->modify('+1 day')->format('Y-m-d');
    }

    public function isWeekend(array $weekends)
    {
        $currentDay = $this->now->format('l');

        return in_array($currentDay, $weekends);
    }

    public function checkInStatus($dutySchedule, $dutyDate): array
    {
        $checkIn = $this->now;
        $scheduleTime = $dutySchedule->start_time;
        $considerTime = $dutySchedule->checkin_consider_time;
        $startTime = Carbon::parse($dutyDate.' '.$scheduleTime);

        // Add consider time to start time (convert minutes to seconds)
        $considerTimeInSeconds = (int) $considerTime * 60;
        $startTimeWithConsider = $startTime->copy()->addSeconds($considerTimeInSeconds);

        $diffInSeconds = $startTime->diffInSeconds($checkIn, false);
        $time = gmdate('H:i:s', abs($diffInSeconds));

        if ($checkIn->lt($startTime)) {
            // Check in before start time
            $response = ['checkin_status' => 'on_time', 'late_duration' => '00:00:00'];
        } elseif ($checkIn->lte($startTimeWithConsider)) {
            // Check in within start time + consider time
            $response = ['checkin_status' => 'on_time', 'late_duration' => '00:00:00'];
        } else {
            // Check in after start time + consider time
            $response = ['checkin_status' => 'late', 'late_duration' => $time];
        }

        return $response;
    }

    public function checkOutStatus($scheduleEndTime, $considerTime): array
    {
        $checkOut = $this->now;
        $dutyDate = $this->now->format('Y-m-d');

        $endTime = Carbon::parse($dutyDate.' '.$scheduleEndTime);

        // Add consider time to end time (convert minutes to seconds)
        $considerTimeInSeconds = (int) $considerTime * 60;
        $endTimeWithConsider = $endTime->copy()->addSeconds($considerTimeInSeconds);

        $diffInSeconds = $endTime->diffInSeconds($checkOut, false);
        $time = gmdate('H:i:s', abs($diffInSeconds));

        if ($checkOut->lt($endTime)) {
            // Check out before end time
            $response = ['checkout_status' => 'left_timely', 'early_exit_duration' => '00:00:00'];
        } elseif ($checkOut->lte($endTimeWithConsider)) {
            // Check out within end time + consider time
            $response = ['checkout_status' => 'left_on_time', 'early_exit_duration' => null];
        } else {
            // Check out after end time + consider time
            $response = ['checkout_status' => 'left_later', 'early_exit_duration' => '00:00:00'];
        }

        return $response;
    }

    public function getFormateDateTime($time, $format = 'Y-m-d H:i:s')
    {
        return Carbon::createFromFormat(
            $format,
            $this->now->format('Y-m-d').' '.$time,
            config('app.timezone')
        );
    }

    public function customNow($date, $time)
    {
        return Carbon::createFromFormat(
            'Y-m-d H:i',
            $date.' '.$time,
            config('app.timezone')
        );
    }
}
