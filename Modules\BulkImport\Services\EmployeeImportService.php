<?php

declare(strict_types=1);

namespace Modules\BulkImport\Services;

use App\Models\User;
use App\Repositories\UserRepositoryV2;
use Exception;
use Illuminate\Support\Facades\Log;

class EmployeeImportService
{
    public function __construct(
        protected UserRepositoryV2 $userRepository
    ) {}

    /**
     * Import employee data following the same pattern as UserControllerV2
     *
     * @param  array  $rows  CSV/Excel rows
     * @param  array  $mapping  Field mapping
     * @param  bool  $hasHeader  Whether file has header
     * @return array Import statistics
     */
    public function importEmployees(array $rows, array $mapping, bool $hasHeader = true): array
    {
        $totalRows = count($rows);
        $importedRows = 0;
        $skippedRows = 0;
        $errorRows = 0;

        // Remove header if present
        if ($hasHeader && ! empty($rows)) {
            array_shift($rows);
            $totalRows--;
        }

        Log::info('Starting employee import', [
            'total_rows' => $totalRows,
            'mapping' => $mapping,
        ]);

        foreach ($rows as $rowIndex => $row) {
            try {
                // Process the employee data following UserControllerV2 pattern
                $result = $this->processEmployeeRow($row, $mapping, $rowIndex);

                if ($result['success']) {
                    $importedRows++;
                    Log::info('Employee imported successfully', [
                        'row_index' => $rowIndex,
                        'employee_id' => $result['employee_id'] ?? null,
                    ]);
                } else {
                    $skippedRows++;
                    Log::warning('Employee row skipped', [
                        'row_index' => $rowIndex,
                        'reason' => $result['reason'] ?? 'Unknown',
                    ]);
                }

            } catch (Exception $e) {
                $errorRows++;
                Log::error("Error importing employee row {$rowIndex}: ".$e->getMessage(), [
                    'row' => $row,
                    'mapping' => $mapping,
                    'exception' => $e,
                ]);
            }
        }

        return [
            'total' => $totalRows,
            'imported' => $importedRows,
            'skipped' => $skippedRows,
            'errors' => $errorRows,
        ];
    }

    /**
     * Process a single employee row following UserControllerV2 pattern
     * This mirrors the multi-step process: storeEmployeeSetup -> storePersonalInfo -> storeOfficialSetup -> updatePersonalDocument -> storeSalaryConfiguration
     */
    protected function processEmployeeRow(array $row, array $mapping, int $rowIndex): array
    {
        try {
            // Map CSV columns to database fields
            $mappedData = $this->mapRowData($row, $mapping);

            // Validate required fields
            if (empty($mappedData['name']) || empty($mappedData['email'])) {
                return [
                    'success' => false,
                    'reason' => 'Missing required fields (name or email)',
                ];
            }

            // Check if employee already exists
            $existingEmployee = User::where('email', $mappedData['email'])->first();
            if ($existingEmployee) {
                return [
                    'success' => false,
                    'reason' => 'Employee with this email already exists',
                ];
            }

            // Step 1: Employee Setup (storeEmployeeSetup)
            $user = $this->storeEmployeeSetup($mappedData);

            // Step 2: Personal Info (storePersonalInfo)
            $this->storePersonalInfo($user->id, $mappedData);

            // Step 3: Official Setup (storeOfficialSetup)
            $this->storeOfficialSetup($user->id, $mappedData);

            // Step 4: Personal Documents (updatePersonalDocument)
            $this->storePersonalDocument($user->id, $mappedData);

            // Step 5: Salary Configuration (storeSalaryConfiguration)
            $this->storeSalaryConfiguration($user->id, $mappedData);

            return [
                'success' => true,
                'employee_id' => $user->id,
            ];

        } catch (Exception $e) {
            Log::error('Error processing employee row: '.$e->getMessage(), [
                'row_index' => $rowIndex,
                'row' => $row,
                'exception' => $e,
            ]);
            throw $e;
        }
    }

    /**
     * Map CSV row data to structured array
     */
    protected function mapRowData(array $row, array $mapping): array
    {
        $mappedData = [];

        foreach ($mapping as $index => $field) {
            if ($field !== null && $field !== '' && isset($row[$index])) {
                $value = trim($row[$index]);
                if (! empty($value)) {
                    $mappedData[$field] = $value;
                }
            }
        }

        return $mappedData;
    }

    /**
     * Step 1: Employee Setup - Following UserRepositoryV2::storeEmployeeSetup
     */
    protected function storeEmployeeSetup(array $data): User
    {
        $payload = [
            'name' => $data['name'] ?? '',
            'email' => $data['email'] ?? '',
            'phone' => $data['phone'] ?? '',
            'employee_id' => $data['employee_id'] ?? '',
            'job_type' => $this->normalizeJobType($data['job_type'] ?? 'Full Time'),
            'department_id' => $this->getDepartmentId($data['department_id'] ?? ''),
            'designation_id' => $this->getDesignationId($data['designation_id'] ?? ''),
            'role_id' => $this->getRoleId($data['role_id'] ?? ''),
            'status' => $this->normalizeEmployeeStatus($data['status'] ?? 'active'),
            'joining_date' => $data['joining_date'] ?? now()->toDateString(),
            'attendance_method' => [$this->normalizeAttendanceMethod($data['attendance_method'] ?? 'manual')],
            'duty_schedule_id' => $this->getDutyScheduleId($data['duty_schedule_id'] ?? ''),
        ];

        return $this->userRepository->storeEmployeeSetup($payload);
    }

    /**
     * Step 2: Personal Info - Following UserRepositoryV2::storePersonalInfo
     */
    protected function storePersonalInfo(int $userId, array $data): void
    {
        $payload = [
            'user_id' => $userId,
            'gender' => $this->normalizeGender($data['gender'] ?? null),
            'country_id' => $this->getCountryId($data['country_id'] ?? ''),
            'address' => $data['address'] ?? '',
            'religion' => $data['religion'] ?? 'Not Specified',
            'marital_status' => $data['marital_status'] ?? 'Single',
            'blood_group' => $data['blood_group'] ?? 'Not Specified',
            'birth_date' => $data['birth_date'] ?? null,
            'speak_language' => $data['speak_language'] ?? 'English',
            'profile_percentage' => 25, // After personal info
        ];

        $this->userRepository->storePersonalInfo($userId, $payload);
    }

    /**
     * Step 3: Official Setup - Following UserRepositoryV2::updateOfficialSetup
     */
    protected function storeOfficialSetup(int $userId, array $data): void
    {
        $payload = [
            'user_id' => $userId,
            'manager_id' => $this->getManagerId($data['manager_id'] ?? ''),
            'time_zone' => 'UTC',
            'is_free_ip' => false,
            'is_free_location' => false,
            'ip_addresses' => '',
            'locations' => [],
            'profile_percentage' => 50, // After official setup
        ];

        $this->userRepository->updateOfficialSetup($userId, $payload);

        // Handle weekends separately since updateOfficialSetup doesn't include it
        $this->updateAttendanceConfigWeekends($userId, $data);
    }

    /**
     * Step 4: Personal Documents - Following UserRepositoryV2::updatePersonalDocument
     */
    protected function storePersonalDocument(int $userId, array $data): void
    {
        $payload = [
            'user_id' => $userId,
            'passport_number' => $data['passport_number'] ?? '',
            'passport_expire_date' => null,
            'labour_identification_number' => '',
            'labour_identification_expire_date' => null,
            'social_security_number' => '',
            'nid_card_number' => $data['aadhar_number'] ?? '', // Map aadhar to nid
            'tin_number' => $data['pan_number'] ?? '', // Map pan to tin
            'profile_percentage' => 75, // After documents
        ];

        $this->userRepository->updatePersonalDocument($userId, $payload);
    }

    /**
     * Step 5: Salary Configuration - Following UserRepositoryV2::updateSalaryConfiguration
     */
    protected function storeSalaryConfiguration(int $userId, array $data): void
    {
        $payload = [
            'user_id' => $userId,
            'contract_start_date' => $data['contract_start_date'] ?? now()->toDateString(),
            'payslip_type' => $this->normalizePayslipType($data['payslip_type'] ?? 'Monthly'),
            'gross_salary' => (float) ($data['gross_salary'] ?? 0),
            'basic_salary' => (float) ($data['basic_salary'] ?? 0),
            'payment_method' => $this->normalizePaymentMethod($data['payment_method'] ?? 'Bank Transfer'),
            'bank_account' => $data['bank_account'] ?? '',
            'bank_name' => $data['bank_name'] ?? '',
            'allowances' => [],
            'deductions' => [],
            'profile_percentage' => 100, // Complete profile
        ];

        $this->userRepository->updateSalaryConfiguration($userId, $payload);
    }

    /**
     * Helper methods to get IDs from names/values
     */
    protected function getDepartmentId($value): ?int
    {
        if (is_numeric($value)) {
            return (int) $value;
        }

        // If it's a name, you might want to look it up
        // For now, return null if not numeric
        return null;
    }

    protected function getDesignationId($value): ?int
    {
        if (is_numeric($value)) {
            return (int) $value;
        }

        return null;
    }

    protected function getManagerId($value): ?int
    {
        if (is_numeric($value)) {
            return (int) $value;
        }

        return null;
    }

    protected function getRoleId($value): ?int
    {
        if (is_numeric($value)) {
            return (int) $value;
        }

        return null;
    }

    protected function getCountryId($value): ?int
    {
        if (is_numeric($value)) {
            return (int) $value;
        }

        return null;
    }

    protected function getDutyScheduleId($value): ?int
    {
        if (is_numeric($value)) {
            return (int) $value;
        }

        return null;
    }

    /**
     * Normalize job type to match database enum values
     */
    protected function normalizeJobType(string $jobType): string
    {
        $mapping = [
            'Full Time' => 'full_time',
            'Part Time' => 'part_time',
            'full time' => 'full_time',
            'part time' => 'part_time',
            'fulltime' => 'full_time',
            'parttime' => 'part_time',
        ];

        return $mapping[$jobType] ?? 'full_time';
    }

    /**
     * Normalize employee status to match database enum values
     */
    protected function normalizeEmployeeStatus(string $status): string
    {
        $mapping = [
            'active' => 'permanent',
            'inactive' => 'suspended',
            'Active' => 'permanent',
            'Inactive' => 'suspended',
            'Permanent' => 'permanent',
            'Probation' => 'probition', // Note: keeping the typo from the enum
            'Contract' => 'contract',
            'Suspended' => 'suspended',
            'Terminated' => 'terminated',
            'Retired' => 'retired',
            'Layoff' => 'layoff',
            'Resigned' => 'resigned',
        ];

        return $mapping[$status] ?? 'permanent';
    }

    /**
     * Normalize attendance method to match the system's attendance method structure
     */
    protected function normalizeAttendanceMethod(string $method): string
    {
        $mapping = [
            'Manual' => 'normal_attendance',
            'manual' => 'normal_attendance',
            'Normal Attendance' => 'normal_attendance',
            'normal attendance' => 'normal_attendance',
            'Face Recognition' => 'normal_attendance', // Default to normal if face recognition module not active
            'face recognition' => 'normal_attendance',
            'Fingerprint' => 'normal_attendance', // Default to normal if fingerprint module not active
            'fingerprint' => 'normal_attendance',
            'QR Code' => 'qr_based_attendance',
            'qr code' => 'qr_based_attendance',
            'Qr Based Attendance' => 'qr_based_attendance',
            'Selfie' => 'selfie_based_attendance',
            'selfie' => 'selfie_based_attendance',
            'Selfie Based Attendance' => 'selfie_based_attendance',
        ];

        $normalizedMethod = $mapping[$method] ?? 'normal_attendance';

        // Check if the method requires a specific module to be active
        if ($normalizedMethod === 'qr_based_attendance' && ! $this->isModuleActive('QrBasedAttendance')) {
            return 'normal_attendance';
        }

        if ($normalizedMethod === 'selfie_based_attendance' && ! $this->isModuleActive('SelfieBasedAttendance')) {
            return 'normal_attendance';
        }

        return $normalizedMethod;
    }

    /**
     * Check if a module is active
     */
    protected function isModuleActive(string $module): bool
    {
        return function_exists('isModuleActive') ? isModuleActive($module) : false;
    }

    /**
     * Normalize gender to match database enum values
     */
    protected function normalizeGender(?string $gender): ?string
    {
        if (! $gender) {
            return null;
        }

        $mapping = [
            'Male' => 'male',
            'Female' => 'female',
            'Other' => 'other',
            'male' => 'male',
            'female' => 'female',
            'other' => 'other',
        ];

        return $mapping[$gender] ?? null;
    }

    /**
     * Normalize payslip type to match database enum values
     */
    protected function normalizePayslipType(string $payslipType): string
    {
        $mapping = [
            'Monthly' => 'monthly',
            'Weekly' => 'weekly',
            'Daily' => 'daily',
            'Hourly' => 'hourly',
            'monthly' => 'monthly',
            'weekly' => 'weekly',
            'daily' => 'daily',
            'hourly' => 'hourly',
            'Bi-weekly' => 'weekly', // Map bi-weekly to weekly
        ];

        return $mapping[$payslipType] ?? 'monthly';
    }

    /**
     * Normalize payment method to match database enum values
     */
    protected function normalizePaymentMethod(string $paymentMethod): string
    {
        $mapping = [
            'Bank Transfer' => 'bank',
            'Cash' => 'cash',
            'Check' => 'cheque',
            'Cheque' => 'cheque',
            'bank' => 'bank',
            'cash' => 'cash',
            'cheque' => 'cheque',
        ];

        return $mapping[$paymentMethod] ?? 'cash';
    }

    /**
     * Normalize weekend data to array format
     */
    protected function normalizeWeekends(string $weekends): array
    {
        // Handle comma-separated values
        if (strpos($weekends, ',') !== false) {
            $weekendArray = array_map('trim', explode(',', $weekends));
        } else {
            // Single weekend value
            $weekendArray = [trim($weekends)];
        }

        // Normalize weekend names to proper format
        $normalizedWeekends = [];
        foreach ($weekendArray as $weekend) {
            $normalizedWeekends[] = $this->normalizeWeekendName($weekend);
        }

        return array_unique($normalizedWeekends);
    }

    /**
     * Normalize individual weekend name
     */
    protected function normalizeWeekendName(string $weekend): string
    {
        $mapping = [
            'saturday' => 'Saturday',
            'sunday' => 'Sunday',
            'friday' => 'Friday',
            'monday' => 'Monday',
            'tuesday' => 'Tuesday',
            'wednesday' => 'Wednesday',
            'thursday' => 'Thursday',
        ];

        $lowerWeekend = strtolower(trim($weekend));

        return $mapping[$lowerWeekend] ?? ucfirst($lowerWeekend);
    }

    /**
     * Update attendance config weekends separately
     */
    protected function updateAttendanceConfigWeekends(int $userId, array $data): void
    {
        $user = \App\Models\User::find($userId);
        if ($user && $user->attendanceConfig) {
            $user->attendanceConfig->update([
                'weekends' => $this->normalizeWeekends($data['weekend'] ?? 'Saturday, Sunday'),
            ]);
        }
    }
}
