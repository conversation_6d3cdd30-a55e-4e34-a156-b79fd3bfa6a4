<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;

class Admin
{
    public function handle($request, Closure $next)
    {
        if (! Auth::check()) {
            return redirect('/');
        }

        App::setLocale(Auth::user()->lang ?? globalSetting('lang', 'base'));

        $isRunningBreak = attendanceStatus('break');

        $allowedRoutes = [
            'admin.dashboard',
            'get.dashboard.data',
            'attendance.break.end',
            'attendance.checkout.modal',
            'staff.dashboard',
            'hr.dashboard',
            'today.activity',
        ];

        if ($isRunningBreak && ! request()->routeIs(...$allowedRoutes)) {
            return redirect()->route('staff.dashboard');
        }

        return $next($request);
    }
}
