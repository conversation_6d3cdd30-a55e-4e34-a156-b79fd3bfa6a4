<?php

namespace App\Http\Controllers\Setting;

use App\Http\Controllers\Controller;
use App\Http\Requests\Setting\UpdateSettingsRequest;
use App\Models\Setting\Setting;
use App\Repositories\CurrencyRepository;
use App\Repositories\LanguageRepository;
use App\Repositories\Settings\SettingRepository;
use App\Services\FileUploadService;
use Exception;
use Illuminate\Support\Facades\DB;

class SettingsController extends Controller
{
    public function __construct(
        protected SettingRepository $settingRepo,
        protected CurrencyRepository $currencyRepo,
        protected LanguageRepository $languageRepo,
        protected Setting $model) {}

    public function index()
    {
        try {
            $data['title'] = _trans('settings.Settings');
            $data['settings'] = $this->model->where('visibility', 'visible')
                ->get()
                ->groupBy('group');
            $data['currencies'] = $this->currencyRepo->model->get()->pluck('name', 'id')->toArray();

            return view('backend.settings.general_setting', compact('data'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function update(UpdateSettingsRequest $request)
    {
        DB::beginTransaction();

        try {
            foreach ($request->except('_token') as $key => $setting) {
                $inputType = $setting['type'] ?? null;
                $newValue = $setting['value'] ?? null;
                $oldValue = $setting['old_value'] ?? null;

                // Skip update if values are identical
                if ($newValue == $oldValue) {
                    continue;
                }

                if ($inputType === 'file' || $inputType === 'image') {
                    if ($request->hasFile("{$key}.value")) {
                        $uploadedFile = $request->file("{$key}.value");

                        // Upload and store using FileUploadService
                        $path = FileUploadService::file($uploadedFile, 'addon_settings');

                        $value = [
                            'disk' => config('filesystems.default'),
                            'files' => [$path],
                        ];

                        $this->model->where('key', $key)->update([
                            'value' => $value,
                        ]);
                    }
                } else {
                    // Update simple (non-file) values
                    $this->model->where('key', $key)->update([
                        'value' => $newValue,
                    ]);
                }
            }

            DB::commit();

            return redirect()->back()->with('success', _trans('common.Settings updated successfully.'));
        } catch (Exception $e) {
            DB::rollBack();

            return catchHandler($e); // Your global exception handler
        }
    }
}
