<?php

namespace Modules\EmployeePerformance\Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Modules\EmployeePerformance\Entities\Performance;

class PerformanceSeeder extends Seeder
{
    public function run()
    {
        Performance::truncate();

        $users = User::where('id', '!=', 1)->pluck('id')->toArray();

        if (empty($users)) {
            $this->command->warn('⚠️ No users found to seed performance data.');

            return;
        }

        $data = [];
        $totalRecords = rand(60, 70);

        for ($i = 0; $i < $totalRecords; $i++) {
            $userId = $users[array_rand($users)]; // pick a random user

            $data[] = [
                'user_id' => $userId,
                'team_leader_or_rep' => fake()->randomElement([
                    'Outstanding Performance Award',
                    'Team Player Award',
                    'Employee of the Month',
                ]),
                'shift' => fake()->randomElement(['Day', 'Night']),
                'employment_status' => fake()->randomElement(['Permanent', 'On Probation']),
                'overall_performance' => fake()->randomElement(['Below Average', 'Good', 'Average']),
                'leave_status' => fake()->randomElement(['Less', 'Moderate', 'High', 'Excessive']),
                'leaves_on_monday_or_friday' => fake()->boolean(),
                'verbal_warning_count' => fake()->numberBetween(0, 10),
                'write_up_count' => fake()->numberBetween(0, 10),
                'tl_ranking' => fake()->randomElement(['Below Average', 'Good', 'Average']),
                'tl_review' => fake()->sentence(),
                'hr_ranking' => fake()->randomElement(['Below Average', 'Good', 'Average']),
                'hr_review' => fake()->sentence(),
                'date' => fake()->dateTimeThisYear(),
                'created_by' => 1,
                'updated_by' => 1,
                'company_id' => 1,
                'branch_id' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        Performance::insert($data);

        $this->command->info('✅ Seeded '.count($data).' performance records.');
    }
}
