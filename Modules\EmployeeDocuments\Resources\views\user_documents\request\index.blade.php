@extends('backend.layouts.app')
@section('title', @$data['title'])
@section('content')
    <div class="row">
        <div class="col-lg-12 invoice_table_action">
            <x-container :title="$data['title']">
                <x-table :bulkAction="true" buttonTitle="Add New" buttonRoute="{{ route('document-request.create') }}"
                    permission="official_document_request_create">
                    <!--  table start -->
                    <table class="table table-bordered" id="table">
                        <thead class="thead">
                            <tr>
                                <th class="sorting-asc w-60 no-export">
                                    <div class="check-box">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="all_check" />
                                        </div>
                                    </div>
                                </th>
                                <th>{{ _trans('common.SL') }}</th>
                                <th>{{ _trans('common.Employee') }}</th>
                                <th>{{ _trans('common.Request Type') }}</th>
                                <th>{{ _trans('common.Request Date') }}</th>
                                <th>{{ _trans('common.File') }}</th>
                                <th class="text-center">{{ _trans('common.Informed') }}</th>
                                <th class="text-center">{{ _trans('common.Status') }}</th>
                                <th class="w-90 no-export">{{ _trans('common.Action') }}</th>
                            </tr>
                        </thead>
                        <tbody class="tbody">
                            @forelse ($data['items'] as $key => $row)
                                <tr>
                                    <td class="no-export">
                                        <div class="check-box">
                                            <div class="form-check">
                                                <input class="form-check-input column_id" id="column_{{ $row->id }}"
                                                    onclick="columnID({{ $row->id }})" type="checkbox"
                                                    name="column_id[]" value="{{ $row->id }}">
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ $key + 1 }}</td>
                                    <td>{{ $row->user->name ?? 'N/A' }}</td>
                                    <td>{{ $row->userDocumentType->name ?? 'N/A' }}</td>
                                    <td>{{ $row->request_date ?? 'N/A' }}</td>
                                    <td>
                                        @if ($row->response_file)
                                            <a href="{{ asset($row->response_file) }}" target="_blank"
                                                class="text-success">
                                                <i class="las la-cloud-download-alt"></i>
                                                {{ _trans('common.Download') }}
                                            </a>
                                        @else
                                            <span>No File</span>
                                        @endif
                                    </td>
                                    <td>{{ @$row->informedTo->name }}</td>
                                    <td class="text-center">
                                        <x-common.badge :status="$row->status" :data="[
                                            'pending' => 'warning',
                                            'approved_by_hr' => 'success',
                                            'approved_by_admin' => 'success',
                                            'rejected_by_hr' => 'destructive',
                                            'rejected_by_admin' => 'destructive',
                                        ]" />
                                    </td>
                                    <td class="no-export">
                                        <div class="dropdown dropdown-action">
                                            <button type="button" class="btn-dropdown" data-bs-toggle="dropdown"
                                                aria-expanded="false">
                                                <i class="las la-ellipsis-h"></i>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end">
                                                @if (hasPermission('official_document_request_read'))
                                                    <a class="dropdown-item"
                                                        href="{{ route('document-request.approve.form', $row->id) }}">
                                                        <x-common.icons name="eye" class="text-subtitle" size="20"
                                                            stroke-width="1.5" />
                                                        {{ _trans('common.View') }}
                                                    </a>
                                                @endif
                                                @if (hasPermission('official_document_request_update') &&
                                                        (!$row->approved_by && !$row->final_approved_by && !$row->rejected_by))
                                                    <a class="dropdown-item"
                                                        href="{{ route('document-request.edit', $row->id) }}">
                                                        <x-common.icons name="edit" class="text-title" size="16"
                                                            stroke-width="1.5" />
                                                        {{ _trans('common.Edit') }}
                                                    </a>
                                                @endif

                                                @if (hasPermission('official_document_request_delete') &&
                                                        (!$row->approved_by && !$row->final_approved_by && !$row->rejected_by))
                                                    <x-table.action.delete
                                                        url="{{ route('document-request.destroy', $row->id) }}">
                                                    </x-table.action.delete>
                                                @endif
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <x-table.empty colspan="12"></x-table.empty>
                            @endforelse
                        </tbody>
                    </table>
                    <!--  table end -->
                    <x-table.pagination :data="$data['items']" />
                </x-table>
            </x-container>
        </div>
        <div class="col-md-6 invoice_view_action d-none overflow-hidden mb-30">
        </div>
    </div>
    </div>
@endsection
