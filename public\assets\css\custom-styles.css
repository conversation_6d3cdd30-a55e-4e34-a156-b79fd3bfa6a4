.z-10 {
    z-index: 10;
}

.z-15 {
    z-index: 15;
}

.z-20 {
    z-index: 20;
}

.z-100 {
    z-index: 100;
}

.tyne-datepicker {
    position: relative;
    z-index: 12;
}

.tyne-datepicker-container {
    position: relative;
}

.tyne-datepicker-container .datepicker-picker {
    box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .15);
    padding: 14px;
    border: 0;
    border-radius: 4px;
}

.tyne-datepicker-container .datepicker-controls {
    grid-gap: 8px;
}

.tyne-datepicker-container .datepicker-controls .button.prev-button,
.tyne-datepicker-container .datepicker-controls .button.next-button {
    width: 40px;
    height: 40px;
    border: 1px solid var(--ot-primary-btn);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-color: var(--white-color);
    flex-shrink: 0;
    flex-basis: 40px;
    color: var(--ot-primary-btn);
    transition: .3s;
}

.tyne-datepicker-container .datepicker-controls .button.prev-button:hover,
.tyne-datepicker-container .datepicker-controls .button.next-button:hover {
    background: var(--ot-primary-btn);
    color: var(--white-color)
}

.tyne-datepicker-container .datepicker-controls .button:not(.prev-button):not(.next-button) {
    height: 40px;
    white-space: nowrap;
    border-radius: 30px;
    background-color: var(--ot-primary-btn);
    color: var(--white-color);
    font-size: 12px;
    text-transform: uppercase;
}

.tyne-datepicker-container .datepicker .days-of-week {
    display: flex;
    background: var(--ot-bg-primary);
    padding: 4px 6px;
    border-radius: 6px;
    margin: 8px 0;
}

.tyne-datepicker-container .datepicker .dow {
    font-size: 14px;
    font-weight: 600;
    height: 1.5rem;
    text-transform: uppercase;
}

.tyne-datepicker-container .datepicker-controls {}

.tyne-datepicker-container .datepicker-cell.selected,
.tyne-datepicker-container .datepicker-cell.selected:hover {
    background-color: var(--ot-primary-btn);
}

.tyne-datepicker-container .datepicker-footer {
    background-color: transparent;
}

.tyne-datepicker-container .datepicker-footer .datepicker-controls {
    padding-top: 8px;
}

.tyne-datepicker-container .datepicker-footer .datepicker-controls .button.clear-button {
    height: 40px;
    white-space: nowrap;
    border-radius: 30px;
    background-color: var(--ot-bg-primary);
    color: var(--white-color);
    font-size: 12px;
    text-transform: uppercase;
    color: var(--ot-primary-text);
    transition: .3s;
    margin: 0;
}

.tyne-datepicker-container .date-icon {
    right: 5px;
    z-index: 12;
}

.sidebar {
    & .sidebar-menu {
        & .sidebar-menu-section {
            & .sidebar-dropdown-menu {
                & li.sidebar-menu-item {
                    &>ul.child-menu-list {
                        &::before {
                            z-index: 122;
                        }
                    }
                }
            }
        }
    }
}

/* // TABLE STYLES */
.tyne-bordered-table> :not(caption)>*>* {
    border-width: 1px !important;
}

.table.tyne-bordered-table {}

.table.tyne-bordered-table thead tr:first-child th,
.weekend-cell,
.holiday-cell {
    background-color: #F9FAFB !important;
}

.table.tyne-bordered-table thead th {
    font-size: 14px;
    font-weight: 700;
    color: #161F2B;
    padding: 11px 12px;
    text-transform: capitalize;
    white-space: nowrap;
}

.table.tyne-bordered-table thead td,
.table.tyne-bordered-table tbody td {
    padding: 12px;
    font-size: 14px;
    font-weight: 400;
    color: #545C66;
    min-width: 100px;
}

.table.tyne-bordered-table tbody th {
    white-space: nowrap;
    min-width: 100px;
}

.table.tyne-bordered-table {}

.table.tyne-bordered-table {}


.attendant_report_info {
    display: flex;
    align-items: center;
    gap: 12px;

    .avatar_img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
    }

    .attendant_content {
        display: flex;
        flex-direction: column;
        gap: 4px;

        span {
            font-size: 12px;
            color: #919AA4;

            &.link {
                font-size: 12px;
                color: #545C66;
            }
        }


    }
}

.cell_bg_gray {
    background-color: #F5F5F7 !important;
}

.horizontal-separator {
    width: 1px;
    height: 12px;
    background-color: #E0E0E0;
}

/* TAG VARIANT */
.tag-variant {
    font-size: 14px;
    font-weight: 600;
    color: #161F2B;
    text-transform: capitalize;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;

    .badge {
        width: 12px;
        height: 12px;
        background-color: gray;
        flex-shrink: 0;
        display: inline-block;
        border-radius: 1px;
        flex-shrink: 0;
        padding: 0;
        flex-basis: 12px;

        &[variant="success"] {
            background-color: #16B274;
        }

        &[variant="danger"] {
            background-color: #CD4040;
        }

        &[variant="warning"] {
            background-color: #FF991A;
        }

        &[variant="info"] {
            background-color: #7A63FF;
        }

        &[variant="secondary"] {
            background-color: #161F2B;
        }
    }
}

.bulk-card-widget {
    border-radius: 8px;
    border: 1px dashed #D6E2EF;
    background: #F9FBFF;
    padding: 24px;

    .icon {
        width: 64px;
        flex-shrink: 0;
    }

    .tyne-badge {
        border-radius: 30px;
    }

    .bulk-card-body {
        margin-bottom: 22px;
    }
}

.bulk-card-widget-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 24px;
}

.recent-import-list {
    & .recent-import-item:first-child {
        border-top: 1px solid var(--ot-primary-border);
    }

    .recent-import-item {
        border-bottom: 1px solid var(--ot-primary-border);
        padding: 22px 0 24px 0;
    }
}

.bulk-import-modal-body {}

.bulk-import-modal-body .bulk-import-guideline {
    background-color: #F4F8FE;
    padding: 16px;
    border-radius: 8px;

    ul {
        color: #545C66;
        margin-left: 10px !important;
    }
}

.bulk-import-modal-body .bulk-import-file-upload {
    padding: 16px;
    border-radius: 8px;
    border: 1px solid var(--ot-primary-border);

    .icon {
        width: 70px;
        height: 70px;
        margin: 0 auto;
        margin-bottom: 13px;
    }
}

.bulk-import-modal-body .bulk-import-demo-example {
    background-color: rgba(255, 153, 26, 0.10);
    border: 1px solid rgba(255, 153, 26, 0.30);
}

.green-btn {
    background-color: #16B274;
    color: #fff;
    border: 1px solid #16B274;
    border-radius: 8px;
    padding: 1px 10px;
    font-size: 14px;
    font-weight: 600;
    max-height: 24px;
    transition: .3s;

    &:hover {
        background: rgba(22, 178, 116, .9);
    }
}

.box-hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 11;
    opacity: 0;
}