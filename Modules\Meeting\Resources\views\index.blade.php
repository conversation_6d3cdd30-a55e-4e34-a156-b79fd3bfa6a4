@php use Carbon\Carbon; @endphp
@extends('backend.layouts.app')
@section('title', @$title)
@section('content')

    <div class="row g-y-16 mb-16">
        <div class="col-lg-12">
            <div class="ot-card">
                <h3 class="card-title d-flex align-items-center gap-10 mb-25">
                    <x-common.icons name="calendar" class="text-primary" size="22" stroke-width="1.5" />
                    <span>Meeting </span>
                </h3>

                <div class="user-related-info d-flex align-items-center justify-content-between flex-wrap gap-20">

                    <div class="user-related-info-item d-flex align-items-center gap-10">
                        <div class="contents">
                            <h6 class="title fw-bold mb-6 text-primary text-16">1</h6>
                            <p class="paragraph mb-0 text-subtitle fw-semibold text-capitalize">Today Scheduled</p>
                        </div>
                    </div>
                    <span class="line-style"></span>
                    <div class="user-related-info-item d-flex align-items-center gap-10">
                        <div class="contents">
                            <h6 class="title fw-bold mb-6 text-info text-16">0</h6>
                            <p class="paragraph mb-0 text-subtitle fw-semibold text-capitalize">upcoming</p>
                        </div>
                    </div>
                    <span class="line-style"></span>
                    <div class="user-related-info-item d-flex align-items-center gap-10">
                        <div class="contents">
                            <h6 class="title fw-bold mb-6 text-success text-16">1</h6>
                            <p class="paragraph mb-0 text-subtitle fw-semibold text-capitalize">completed</p>
                        </div>
                    </div>
                    <span class="line-style"></span>
                    <div class="user-related-info-item d-flex align-items-center gap-10">
                        <div class="contents">
                            <h6 class="title fw-bold mb-6 text-warning text-16">0</h6>
                            <p class="paragraph mb-0 text-subtitle fw-semibold text-capitalize">Pending</p>
                        </div>
                    </div>
                    <span class="line-style"></span>
                    <div class="user-related-info-item d-flex align-items-center gap-10">
                        <div class="contents">
                            <h6 class="title fw-bold mb-6 text-danger text-16">0</h6>
                            <p class="paragraph mb-0 text-subtitle fw-semibold text-capitalize">cancelled</p>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>



    <x-container :title="@$data['title']" buttonTitle="Create" buttonRoute="{{ route('meetings.create') }}" class="mb-3">
        <x-table :searchBox="true" :exportOption="true" buttonType="modal" modalId="leaveTypeForm">

            data

        </x-table>
    </x-container>
@endsection
