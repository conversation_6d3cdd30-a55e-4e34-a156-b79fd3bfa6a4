<?php

namespace Modules\EmployeePerformance\Repositories;

use Illuminate\Support\Facades\Auth;
use Modules\EmployeePerformance\Entities\Performance;

class EmployeePerformanceRepository
{
    public function __construct(
        protected Performance $model
    ) {}

    public function getPaginateData($request, $fields = ['*'])
    {
        return $this->model::select($fields)->latest('id')
            ->when($request->search, function ($query) use ($request) {
                $query->where(function ($q) use ($request) {
                    $q->where('employment_status', 'like', '%'.$request->search.'%')
                        ->orWhere('overall_performance', 'like', '%'.$request->search.'%');
                });
            })
            ->paginate($request->limit ?? 10);
    }

    public function store($request)
    {
        $request['created_by'] = Auth::id();

        return $this->model->create($request);

    }

    public function show($id)
    {
        return $this->model->findOrFail($id);
    }

    public function update($id, $request)
    {
        $request['updated_by'] = Auth::id();
        $performance = $this->model->findOrFail($id);
        $performance->update($request);

        return $performance;
    }

    public function destroy($id)
    {
        $performance = $this->model->findOrFail($id);
        $performance->delete();

        return $performance;
    }
}
