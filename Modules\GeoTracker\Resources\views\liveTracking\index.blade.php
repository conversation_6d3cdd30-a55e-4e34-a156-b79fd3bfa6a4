@extends('backend.layouts.app')

@section('title', @$title)

@section('content')
    <div class="table-content table-basic">
        <section class="ot-card">
            @if (@globalSetting('google_map_key', 'integration'))
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-lg-12 text-center " style="height: 50px">
                            <h2 class="card-title d-flex gap-10 align-items-center mb-0">
                                {{ _trans('common.Live Location Tracking') }}
                            </h2>

                            <img src="{{ asset('modules/geotracker/images/ajax-loader.gif') }}" alt="onest-hrm"
                                class="img img-responsive text-center justify-center" id="loader"
                                style="width: 65px; margin:0 auto; z-index:99999;">
                        </div>
                        <div class="col-lg-2 d-none ">
                            <div class="table-responsive" style="height: 90vh; ">
                                <table class="table table-bordered table-striped" style="display: none" id="table">
                                    <thead>
                                        <th>Employees</th>
                                    </thead>
                                    <tbody id="tbody">

                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <div id="map" style="height: 70vh; width: 100%;"></div>
                        </div>
                    </div>
                </div>
            @else
                <div class="row p-5 mt-3 mb-3 ">
                    <div class="col-lg-12">
                        <h3 class="danger text-danger"> {{ _trans('error.Please set google map API Key') }}</h3>
                        <a href="{{ route('manage.integrations.view') }}"
                            class="btn btn-primary">{{ _trans('common.Update Google Map API Key') }}
                        </a>
                    </div>
                </div>
                <div class="row mt-3 mb-3  p-5">
                    <h3>Create a Google Cloud Platform (GCP) Project:</h3>
                    <div class="col-lg-12">
                        <ol>
                            <li>Go to the <a href="https://console.cloud.google.com/" target="_blank"
                                    class="text-info">Google Cloud Console</a>.</li>
                            <li>If you're not already signed in, sign in with your Google account.</li>
                            <li>Click the project drop-down at the top left of the page and then click "New
                                Project".
                            </li>
                            <li>Enter a name for your project, select an organization (if applicable), and choose a
                                billing account. Click "Create" to create the project.</li>
                        </ol>

                        <h3>Enable the Google Maps JavaScript API:</h3>
                        <ol>
                            <li>In the GCP Console, navigate to the "APIs &amp; Services" &gt; "Library" section.
                            </li>
                            <li>Search for "Google Maps JavaScript API" and click on it.</li>
                            <li>Click the "Enable" button.</li>
                        </ol>

                        <h3>Create an API Key:</h3>
                        <ol>
                            <li>In the "APIs &amp; Services" &gt; "Credentials" section, click on "Create
                                Credentials".
                            </li>
                            <li>Select "API key" from the drop-down menu.</li>
                            <li>Your API key will be displayed on the next screen. Make sure to restrict the usage
                                of
                                your API key for security purposes (optional but recommended).</li>
                        </ol>
                    </div>

                </div>
            @endif
        </section>
    </div>
@endsection

@push('script')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/firebase/8.2.2/firebase-app.min.js" referrerpolicy="no-referrer">
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/firebase/8.2.2/firebase-database.min.js"
        referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/firebase/8.2.2/firebase-firestore.min.js"
        referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js" referrerpolicy="no-referrer">
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.36/moment-timezone-with-data.min.js"
        referrerpolicy="no-referrer"></script>

    <script
        src="https://maps.googleapis.com/maps/api/js?key={{ @globalSetting('google_map_key', 'integration') }}&callback=initMap">
    </script>

    <script type="module">
        const config = {
            apiKey: `{{ @globalSetting('firebase_api_key', 'integration') }}`,
            authDomain: `{{ @globalSetting('firebase_auth_domain', 'integration') }}`,
            projectId: `{{ @globalSetting('firebase_project_id', 'integration') }}`,
            storageBucket: `{{ @globalSetting('firebase_storage_bucket', 'integration') }}`,
            messagingSenderId: `{{ @globalSetting('firebase_messaging_sender_id', 'integration') }}`,
            appId: `{{ @globalSetting('firebase_app_id', 'integration') }}`,
            measurementId: `{{ @globalSetting('firebase_measurement_id', 'integration') }}`
        };

        firebase.initializeApp(config);

        const db = firebase.firestore();
        const collectionName = `{{ @globalSetting('firebase_auth_collection_name', 'integration') }}`;

        // Now fetch data from Firestore
        readDataFromFirestore(collectionName)
            .then((data) => {
                drawMap(data);
            })
            .catch((error) => {
                console.error("Failed to retrieve data from Firestore:", error);
            });

        function drawMap(data) {
            const locations = data;

            const map = new google.maps.Map(document.getElementById("map"), {
                center: {
                    lat: 23.810331,
                    lng: 90.412521
                }, // Set the initial map center
                zoom: 12, // Adjust the initial zoom level as needed
            });

            const customMarkerIcon = {
                url: "{{ asset('modules/geotracker/images/live.gif') }}",
                scaledSize: new google.maps.Size(40, 40),
            };

            google.maps.event.addListenerOnce(map, "tilesloaded", function() {
                document.getElementById("loader").style.display = "none";
            });

            locations.forEach((location) => {
                const marker = new google.maps.Marker({
                    position: {
                        lat: location.latitude,
                        lng: location.longitude
                    },
                    map: map,
                    title: location.address,
                    icon: customMarkerIcon,
                });

                const infoWindow = new google.maps.InfoWindow({
                    content: `
                        <h6><strong>${location.employee_name} [#${location.employee_id}]</strong></h6>
                        <strong>${location.city}</strong><br>
                        ${location.address}
                    `,
                });

                marker.addListener("click", () => {
                    infoWindow.open(map, marker);
                });
            });
        }

        function readDataFromFirestore(collectionName) {
            const collectionRef = db.collection(collectionName);

            const currentDate = new Date();
            const last30MinutesDate = new Date(currentDate.getTime() - 30 * 60 * 1000); // Subtract 30 minutes

            return collectionRef
                .where("datetime", ">=", last30MinutesDate)
                .orderBy("datetime", "desc")
                .get()
                .then((querySnapshot) => {
                    const data = [];
                    querySnapshot.forEach((doc) => {
                        const docData = doc.data();

                        // Optional: Convert Firestore timestamp to JS Date string
                        if (docData.datetime && docData.datetime.toDate) {
                            docData.datetime = docData.datetime.toDate().toLocaleString();
                        }

                        data.push({
                            id: doc.id,
                            ...docData
                        });
                    });

                    return data;
                })
                .catch((error) => {
                    console.error("Error reading data from Firestore:", error);
                    throw error;
                });
        }

        // Show the loader initially
        document.getElementById("loader").style.display = "block";

        // Function to reload the page
        function reloadPage() {
            location.reload();
        }

        // Set a timeout to reload the page every 2 minutes (120,000 milliseconds)
        setTimeout(reloadPage, 120000);
    </script>
@endpush
