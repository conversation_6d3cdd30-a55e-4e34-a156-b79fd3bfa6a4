<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LoginController extends Controller
{
    public function signIn()
    {
        $data = [];

        if (Auth::check()) {
            return redirect()->route('admin.dashboard');
        }

        if (config('app.debug') === true || config('app.demo_login') === true) {
            $data['users'] = User::select('id', 'name', 'email')->take(4)->get();
        }

        return view('auth.admin_login', $data);
    }

    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|exists:users,email',
            'password' => 'required',
            'device_uuid' => 'required',
        ], [
            'email.exists' => 'The email does not exist in our records',
        ]);

        try {
            $credentials = $request->only('email', 'password');

            $user = User::with(['role', 'permissions', 'dutySchedules', 'attendanceConfig'])->where('email', $request->email)->first();

            if (! $user) {
                return redirect()->back()->withInput($request->only('email'))->withErrors(['email' => 'The email does not exist in our records']);
            }

            if ($user->dutySchedules->count() < 1 || $user->attendanceConfig == null) {
                return redirect()->back()->withInput($request->only('email'))->withErrors(['login_form_error' => 'Your account is not fully configured yet. Please contact with your administrator.']);
            }

            if (! $user->isActive()) {
                return redirect()->back()->withInput($request->only('email'))->withErrors(['login_form_error' => 'Your account is not active. Please contact with your administrator.']);
            }

            session()->put('session_branch_id', $user->branch_id);
            session()->put('session_company_id', $user->company_id);

            if (isModuleActive('SingleDeviceLogin')) {
                if (in_array($user->role->slug, ['super-admin']) || hasPermission('ignore_single_device_login', $user)) {
                    return $this->userLogin($credentials, $user, $request->device_uuid);
                }

                if ($user->isLoggedInFromDevice($request->device_uuid)) {
                    return $this->userLogin($credentials, $user, $request->device_uuid);
                } else {
                    return redirect()->back()->withInput($request->only('email'))->withErrors(['email' => 'User already registered with another device']);
                }
            } else {
                return $this->userLogin($credentials, $user, $request->device_uuid);
            }
        } catch (Exception $exception) {
            return catchHandler($exception);
        }
    }

    public function userLogin($input, $user, $uuid)
    {
        if (isModuleActive('SingleDeviceLogin')) {
            if ($user->role->web_login != 1 && ! $user->hasPermission('ignore_single_device_login')) {
                throwException('You don\'t have permission to login');
            }
        }

        if (Auth::attempt($input)) {
            if (isModuleActive('SingleDeviceLogin')) {
                $user->updateDeviceLog([
                    'device_uuid' => $uuid,
                    'device_info' => request()->server('HTTP_USER_AGENT'),
                    'last_login_device' => 'web',
                ]);
            }

            if (hasPermission('admin_dashboard')) {
                return redirect()->route('admin.dashboard')->with('success', 'Login successful');
            } elseif (hasPermission('hr_dashboard')) {
                return redirect()->route('hr.dashboard')->with('success', 'Login successful');
            } elseif (hasPermission('staff_dashboard')) {
                return redirect()->route('staff.dashboard')->with('success', 'Login successful');
            } else {
                return redirect()->route('staff.dashboard')->with('success', 'Login successful');
            }
        } else {
            return redirect()->back()->withErrors(['login_form_error' => 'Credentials do not match our records']);
        }
    }

    public function redirectLogin()
    {
        return redirect()->route('sign.in');
    }
}
