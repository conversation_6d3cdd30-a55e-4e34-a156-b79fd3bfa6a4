<?php

namespace Modules\EmployeePerformance\Http\Controllers;

use App\Repositories\UserRepositoryV2;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\EmployeePerformance\Http\Requests\StoreEmployeePerformance;
use Modules\EmployeePerformance\Repositories\EmployeePerformanceRepository;

class EmployeePerformanceController extends Controller
{
    public function __construct(
        protected EmployeePerformanceRepository $repository,
        protected UserRepositoryV2 $userRepository

    ) {}

    public function index(Request $request)
    {
        $data['title'] = _trans('common.Employee Performance');
        $data['collection'] = $this->repository->getPaginateData($request);

        return view('employeeperformance::performance.index')->with($data);
    }

    public function create()
    {
        $data['title'] = _trans('common.Create Employee Performance');
        $data['users'] = $this->userRepository->employees();

        return view('employeeperformance::performance.create')->with($data);
    }

    public function store(StoreEmployeePerformance $request)
    {
        try {
            $this->repository->store($request->validated());

            return redirect()->route('employee-performance.index')->with('success', 'Employee performance created successfully.');
        } catch (Exception $e) {
            return redirect()->back()->with('error', 'Something went wrong. Please try again.');
        }
    }

    public function edit($id)
    {
        $data['title'] = _trans('common.Edit Employee Performance');
        $data['users'] = $this->userRepository->employees();
        $data['performance'] = $this->repository->show($id);

        return view('employeeperformance::performance.edit')->with($data);
    }

    public function update(StoreEmployeePerformance $request, $id)
    {
        try {
            $this->repository->update($id, $request->validated());

            return redirect()->route('employee-performance.index')->with('success', 'Employee performance updated successfully.');
        } catch (Exception $e) {
            return redirect()->back()->with('error', 'Something went wrong. Please try again.');
        }
    }

    public function destroy($id)
    {
        try {
            $this->repository->destroy($id);

            return redirect()->route('employee-performance.index')->with('success', 'Employee performance deleted successfully.');
        } catch (Exception $e) {
            return redirect()->back()->with('error', 'Something went wrong. Please try again.');
        }
    }
}
