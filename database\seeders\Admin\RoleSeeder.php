<?php

namespace Database\Seeders\Admin;

use App\Models\Role;
use App\Models\RoleWisePermission;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{
    public function run()
    {
        $input = session()->get('input');

        $roles = [
            'super-admin' => 'Super admin',
            'hr' => 'HR',
            'staff' => 'Staff',
        ];

        foreach ($roles as $key => $roleName) {
            $role = Role::firstOrCreate([
                'slug' => $key,
                'company_id' => $input['company_id'] ?? 1,
            ], [
                'name' => $roleName,
                'app_login' => 1,
                'web_login' => 1,
            ]);

            // Store role permissions in RoleWisePermission
            RoleWisePermission::updateOrCreate(
                ['role_id' => $role->id],
                ['permissions' => $this->getPermissions($key)]
            );
        }
    }

    public function getPermissions($key)
    {
        switch ($key) {
            case 'super-admin':
                return $this->adminPermissions();
                break;
            case 'hr':
                return $this->hrPermissions();
                break;
            case 'staff':
                return $this->staffPermissions();
                break;
            default:
                return $this->staffPermissions();
        }
    }

    public function adminPermissions(): array
    {
        $permissions = [
            // ================== Employee management ====================
            // Dashboard
            'admin_dashboard',
            'hr_dashboard',
            'staff_dashboard',

            'branch_switch',

            // Department
            'department_read',
            'department_create',
            'department_update',
            'department_delete',

            // Designation
            'designation_read',
            'designation_create',
            'designation_update',
            'designation_delete',

            // Role
            'role_read',
            'role_create',
            'role_update',
            'role_delete',

            // Employee
            'user_read',
            'user_create',
            'user_update',
            'user_delete',
            'profile_read',
            'profile_update',
            'user_permission_update',
            'user_banned',
            'user_unbanned',
            'user_restore',
            'password_reset_mail',
            'password_update',
            'profile_image_view',

            // Official document type
            'official_document_type_read',
            'official_document_type_create',
            'official_document_type_update',
            'official_document_type_delete',

            // Official document request
            'official_document_request_read',
            'official_document_request_create',
            'official_document_request_update',
            'official_document_request_delete',

            // Complain
            'complain_read',
            'complain_create',
            'complain_update',
            'complain_delete',

            // Verbal Warning
            'verbal_warning_read',
            'verbal_warning_create',
            'verbal_warning_update',
            'verbal_warning_delete',

            // Performance
            'performance_read',
            'performance_create',
            'performance_update',
            'performance_delete',

            // ================== Attendance management ====================
            // Weekend
            'weekend_read',
            'weekend_update',

            // Holiday
            'holiday_read',
            'holiday_create',
            'holiday_update',
            'holiday_delete',

            // Schedule
            'schedule_read',
            'schedule_create',
            'schedule_update',
            'schedule_delete',

            // Duty calender
            'duty_calendar_read',
            'duty_calendar_create',
            'duty_calendar_update',

            // Ip
            'ip_read',
            'ip_create',
            'ip_update',
            'ip_delete',

            // Location
            'location_read',
            'location_create',
            'location_update',
            'location_delete',

            // Attendance
            'attendance_read',
            'attendance_create',
            'attendance_update',
            'attendance_delete',

            // ================== Leave management ====================
            // Leave type
            'leave_type_read',
            'leave_type_create',
            'leave_type_update',
            'leave_type_delete',

            // Leave assign
            'leave_assign_read',
            'leave_assign_create',
            'leave_assign_update',
            'leave_assign_delete',

            // Leave request
            'leave_request_read',
            'leave_request_create',
            'leave_request_update',
            'leave_request_delete',
            'leave_request_approve',
            'leave_request_reject',
            'leave_request_refer',

            // Leave balance
            'leave_balance_read',
            'leave_balance_create',
            'leave_balance_update',
            'leave_balance_delete',

            // ================== Payroll management ====================
            // Commission
            'commission_read',
            'commission_create',
            'commission_update',
            'commission_delete',

            // Setup
            'setup_read',
            'set_contract',
            'set_commission',

            // Advance type
            'advance_type_read',
            'advance_type_create',
            'advance_type_update',
            'advance_type_delete',

            // Advance
            'advance_read',
            'advance_create',
            'advance_update',
            'advance_delete',
            'advance_approved',
            'advance_pay',

            // Salary generate
            'salary_read',
            'salary_create',
            'salary_update',
            'salary_delete',
            'salary_calculate',
            'salary_pay',
            'salary_payslip',

            // ================== Communication management ====================
            // Meeting
            'meeting_read',
            'meeting_create',
            'meeting_update',
            'meeting_delete',

            // Appointment
            'appointment_read',
            'appointment_create',
            'appointment_update',
            'appointment_delete',
            'appointment_approve',
            'appointment_reject',

            // Notice
            'notice_read',
            'notice_create',
            'notice_update',
            'notice_delete',

            // Bulletin
            'bulletin_read',
            'bulletin_create',
            'bulletin_update',
            'bulletin_delete',

            // ================== Office work management ====================
            // Client
            'client_read',
            'client_create',
            'client_update',
            'client_delete',

            // Project
            'project_read',
            'project_create',
            'project_update',
            'project_delete',
            'project_member_read',
            'project_member_delete',
            'project_complete',
            'project_payment',

            // Project file
            'project_file_read',
            'project_file_create',
            'project_file_update',
            'project_file_delete',
            'project_file_comment',

            // Project discussion
            'project_discussion_read',
            'project_discussion_create',
            'project_discussion_update',
            'project_discussion_delete',
            'project_discussion_comment',

            // Project note
            'project_note_read',
            'project_note_create',
            'project_note_update',
            'project_note_delete',

            // Task
            'task_read',
            'task_create',
            'task_update',
            'task_delete',
            'task_assign_read',
            'task_assign_delete',
            'task_complete',
            'task_member_read',

            // Task discussion
            'task_discussion_read',
            'task_discussion_create',
            'task_discussion_update',
            'task_discussion_delete',
            'task_discussion_comment',

            // Task note
            'task_note_read',
            'task_note_create',
            'task_note_update',
            'task_note_delete',

            // Task file
            'task_file_read',
            'task_file_create',
            'task_file_update',
            'task_file_delete',
            'task_file_comment',

            // Travel plan
            'travel_plan_read',
            'travel_plan_create',
            'travel_plan_update',
            'travel_plan_delete',
            'travel_plan_approve',
            'travel_plan_reject',

            // Travel meeting
            'travel_meeting_read',
            'travel_meeting_create',
            'travel_meeting_update',
            'travel_meeting_delete',

            // Travel expense
            'travel_expense_read',
            'travel_expense_create',
            'travel_expense_update',
            'travel_expense_delete',
            'travel_expense_approve',

            // Travel workflow
            'travel_workflow_read',
            'travel_workflow_create',
            'travel_workflow_update',
            'travel_workflow_delete',
            'travel_workflow_approve',

            // Award type
            'award_type_read',
            'award_type_create',
            'award_type_update',
            'award_type_delete',

            // Award
            'award_read',
            'award_create',
            'award_update',
            'award_delete',

            // Visit
            'visit_read',
            'visit_update',

            // support ticket
            'support_ticket_read',
            'support_ticket_create',
            'support_ticket_reply',
            'support_ticket_delete',

            // ================== Report management ====================
            // Report
            'live_tracking_read',
            'location_timeline_read',
            'attendance_report_read',
            'payment_report_read',
            'visit_report_read',
            'leave_report_read',

            // ================== Account management ====================
            // Account
            'account_read',
            'account_create',
            'account_update',
            'account_delete',

            // Deposit
            'deposit_read',
            'deposit_create',
            'deposit_update',
            'deposit_delete',

            // Expense
            'expense_read',
            'expense_create',
            'expense_update',
            'expense_delete',
            'expense_approve',
            'expense_pay',

            // Transaction
            'transaction_read',
            'transaction_update',
            'transaction_delete',

            // Deposit category
            'deposit_category_read',
            'deposit_category_create',
            'deposit_category_update',
            'deposit_category_delete',

            // Expense category
            'expense_category_read',

            // Payment method
            'payment_method_read',
            'payment_method_create',
            'payment_method_update',
            'payment_method_delete',

            // ================== Setting management ====================
            // Setting
            'general_settings_read',
            'general_settings_update',
            'email_setup_read',
            'email_setup_update',
            'firebase_setup_read',
            'firebase_setup_update',
            'geocoding_setup_read',
            'geocoding_setup_update',
            'pusher_setup_read',
            'pusher_setup_update',
            'storage_setup_read',
            'storage_setup_update',
            'database_backup',
            'app_theme_setup_read',
            'app_theme_setup_update',
            'app_screen_setting',
            'user_device_read',
            'promotion_read',
            'resignation_read',
            'termination_read',
            'verbal_warning_read',
            'reset_device',

            // Currency
            'currency_read',
            'currency_create',
            'currency_update',
            'currency_delete',

            // Language
            'language_read',
            'language_create',
            'language_update',
            'language_delete',
            'language_setup',
            'language_make_default',

            // Branch
            'branch_read',
            'branch_create',
            'branch_update',
            'branch_delete',

            // Configuration
            'configuration_read',
            'configuration_update',

            // Activation
            'activation_read',
            'activation_update',

            // Branding
            'branding_read',
            'branding_update',

            // Profile
            'contract_profile',
            'attendance_profile',
            'notice_profile',
            'leave_request_profile',
            'visit_profile',
            'phonebook_profile',
            'appointment_profile',
            'advance_profile',
            'commission_profile',
            'salary_profile',
            'project_profile',
            'task_profile',
            'award_profile',
            'travel_profile',

            'tardy_group_read',
            'tardy_group_create',
            'tardy_group_update',
            'tardy_group_delete',

            'tardy_rule_read',
            'tardy_rule_create',
            'tardy_rule_update',
            'tardy_rule_delete',

            'tardy_rule_assign_read',
            'tardy_rule_assign_create',
            'tardy_rule_assign_update',
            'tardy_rule_assign_delete',

            'tardy_record_read',
            'tardy_record_create',
            'tardy_record_update',
            'tardy_record_delete',

            'tardy_request_read',
            'tardy_request_create',
            'tardy_request_update',
            'tardy_request_delete',
            'tardy_request_approve',
            'tardy_request_reject',

            'duty_calendar_read',
            'duty_calendar_create',
            'duty_calendar_update',
            'duty_calendar_delete',

            'asset_category_read',
            'asset_category_create',
            'asset_category_update',
            'asset_category_delete',
            'asset_read',
            'asset_history_read',

            'ignore_single_device_login',
        ];

        if (isModuleActive('SingleDeviceLogin')) {
            $permissions[] = 'ignore_single_device_login';
        }

        return $permissions;
    }

    public function hrPermissions(): array
    {
        return [
            // ================== Employee management ====================
            // Dashboard
            'hr_dashboard',

            // Department
            'department_read',
            'department_create',
            'department_update',
            'department_delete',

            // Designation
            'designation_read',
            'designation_create',
            'designation_update',
            'designation_delete',

            // Role
            'role_read',
            'role_create',
            'role_update',
            'role_delete',

            // Employee
            'user_read',
            'user_create',
            'user_update',
            'user_delete',
            'profile_read',
            'profile_update',
            'user_permission_update',
            'user_banned',
            'user_unbanned',
            'user_restore',
            'password_reset_mail',
            'password_update',
            'profile_image_view',

            // Official document type
            'official_document_type_read',
            'official_document_type_create',
            'official_document_type_update',
            'official_document_type_delete',

            // Official document request
            'official_document_request_read',
            'official_document_request_create',
            'official_document_request_update',
            'official_document_request_delete',

            // Complain
            'complain_read',
            'complain_create',
            'complain_update',
            'complain_delete',

            // Verbal Warning
            'verbal_warning_read',
            'verbal_warning_create',
            'verbal_warning_update',
            'verbal_warning_delete',

            // Performance
            'performance_read',
            'performance_create',
            'performance_update',
            'performance_delete',

            // ================== Attendance management ====================
            // Weekend
            'weekend_read',
            'weekend_update',

            // Holiday
            'holiday_read',
            'holiday_create',
            'holiday_update',
            'holiday_delete',

            // Schedule
            'schedule_read',
            'schedule_create',
            'schedule_update',
            'schedule_delete',

            // Duty calender
            'duty_calendar_read',
            'duty_calendar_create',
            'duty_calendar_update',

            // Ip
            'ip_read',
            'ip_create',
            'ip_update',
            'ip_delete',

            // Location
            'location_read',
            'location_create',
            'location_update',
            'location_delete',

            // Attendance
            'attendance_read',
            'attendance_create',
            'attendance_update',
            'attendance_delete',

            // ================== Leave management ====================
            // Leave type
            'leave_type_read',
            'leave_type_create',
            'leave_type_update',
            'leave_type_delete',

            // Leave assign
            'leave_assign_read',
            'leave_assign_create',
            'leave_assign_update',
            'leave_assign_delete',

            // Leave request
            'leave_request_read',
            'leave_request_create',
            'leave_request_update',
            'leave_request_delete',
            'leave_request_approve',
            'leave_request_reject',
            'leave_request_refer',

            // Leave balance
            'leave_balance_read',
            'leave_balance_create',
            'leave_balance_update',
            'leave_balance_delete',

            // ================== Payroll management ====================
            // Commission
            'commission_read',
            'commission_create',
            'commission_update',
            'commission_delete',

            // Setup
            'setup_read',
            'set_contract',
            'set_commission',

            // Advance type
            'advance_type_read',
            'advance_type_create',
            'advance_type_update',
            'advance_type_delete',

            // Advance
            'advance_read',
            'advance_create',
            'advance_update',
            'advance_delete',
            'advance_approved',
            'advance_pay',

            // Salary generate
            'salary_generate_read',
            'salary_generate_create',
            'salary_generate_update',
            'salary_generate_delete',

            // Tax rebate slab
            'tax_rebate_slab_read',
            'tax_rebate_slab_create',
            'tax_rebate_slab_update',
            'tax_rebate_slab_delete',

            // ================== Communication management ====================
            // Meeting
            'meeting_read',
            'meeting_create',
            'meeting_update',
            'meeting_delete',

            // Appointment
            'appointment_read',
            'appointment_create',
            'appointment_update',
            'appointment_delete',
            'appointment_approve',
            'appointment_reject',

            // Notice
            'notice_read',
            'notice_create',
            'notice_update',
            'notice_delete',

            // Bulletin
            'bulletin_read',
            'bulletin_create',
            'bulletin_update',
            'bulletin_delete',

            // ================== Office work management ====================
            // Client
            'client_read',
            'client_create',
            'client_update',
            'client_delete',

            // Project
            'project_read',
            'project_create',
            'project_update',
            'project_delete',
            'project_member_read',
            'project_member_delete',
            'project_complete',
            'project_payment',

            // Project file
            'project_file_read',
            'project_file_create',
            'project_file_update',
            'project_file_delete',
            'project_file_comment',

            // Project discussion
            'project_discussion_read',
            'project_discussion_create',
            'project_discussion_update',
            'project_discussion_delete',
            'project_discussion_comment',

            // Project note
            'project_note_read',
            'project_note_create',
            'project_note_update',
            'project_note_delete',

            // Task
            'task_read',
            'task_create',
            'task_update',
            'task_delete',
            'task_assign_read',
            'task_assign_delete',
            'task_complete',
            'task_member_read',

            // Task discussion
            'task_discussion_read',
            'task_discussion_create',
            'task_discussion_update',
            'task_discussion_delete',
            'task_discussion_comment',

            // Task note
            'task_note_read',
            'task_note_create',
            'task_note_update',
            'task_note_delete',

            // Task file
            'task_file_read',
            'task_file_create',
            'task_file_update',
            'task_file_delete',
            'task_file_comment',

            // Travel plan
            'travel_plan_read',
            'travel_plan_create',
            'travel_plan_update',
            'travel_plan_delete',
            'travel_plan_approve',
            'travel_plan_reject',

            // Travel meeting
            'travel_meeting_read',
            'travel_meeting_create',
            'travel_meeting_update',
            'travel_meeting_delete',

            // Travel expense
            'travel_expense_read',
            'travel_expense_create',
            'travel_expense_update',
            'travel_expense_delete',
            'travel_expense_approve',

            // Travel workflow
            'travel_workflow_read',
            'travel_workflow_create',
            'travel_workflow_update',
            'travel_workflow_delete',
            'travel_workflow_approve',

            // Award type
            'award_type_read',
            'award_type_create',
            'award_type_update',
            'award_type_delete',

            // Award
            'award_read',
            'award_create',
            'award_update',
            'award_delete',

            // Visit
            'visit_read',
            'visit_update',

            // ================== Report management ====================
            // Report
            'live_tracking_read',
            'location_timeline_read',
            'attendance_report_read',
            'payment_report_read',
            'visit_report_read',
            'leave_report_read',

            // ================== Account management ====================
            // Account
            'account_read',
            'account_create',
            'account_update',
            'account_delete',

            // Deposit
            'deposit_read',
            'deposit_create',
            'deposit_update',
            'deposit_delete',

            // Expense
            'expense_read',
            'expense_create',
            'expense_update',
            'expense_delete',
            'expense_approve',
            'expense_pay',

            // Transaction
            'transaction_read',
            'transaction_update',
            'transaction_delete',

            // Deposit category
            'deposit_category_read',
            'deposit_category_create',
            'deposit_category_update',
            'deposit_category_delete',

            // Expense category
            'expense_category_read',

            // Payment method
            'payment_method_read',
            'payment_method_create',
            'payment_method_update',
            'payment_method_delete',

            // ================== Setting management ====================
            // Setting
            'general_settings_read',
            'general_settings_update',
            'email_setup_read',
            'email_setup_update',
            'firebase_setup_read',
            'firebase_setup_update',
            'geocoding_setup_read',
            'geocoding_setup_update',
            'pusher_setup_read',
            'pusher_setup_update',
            'storage_setup_read',
            'storage_setup_update',
            'database_backup',
            'app_theme_setup_read',
            'app_theme_setup_update',
            'app_screen_setting',

            // Currency
            'currency_read',
            'currency_create',
            'currency_update',
            'currency_delete',

            // Language
            'language_read',
            'language_create',
            'language_update',
            'language_delete',
            'language_setup',
            'language_make_default',

            // Configuration
            'configuration_read',
            'configuration_update',

            // Activation
            'activation_read',
            'activation_update',

            // Branding
            'branding_read',
            'branding_update',

            // Profile
            'contract_profile',
            'attendance_profile',
            'notice_profile',
            'leave_request_profile',
            'visit_profile',
            'phonebook_profile',
            'appointment_profile',
            'advance_profile',
            'commission_profile',
            'salary_profile',
            'project_profile',
            'task_profile',
            'award_profile',
            'travel_profile',

            'tardy_group_read',
            'tardy_group_create',
            'tardy_group_update',
            'tardy_group_delete',

            'tardy_rule_read',
            'tardy_rule_create',
            'tardy_rule_update',
            'tardy_rule_delete',

            'tardy_rule_assign_read',
            'tardy_rule_assign_create',
            'tardy_rule_assign_update',
            'tardy_rule_assign_delete',

            'tardy_record_read',
            'tardy_record_create',
            'tardy_record_update',
            'tardy_record_delete',

            'tardy_request_read',
            'tardy_request_create',
            'tardy_request_update',
            'tardy_request_delete',
            'tardy_request_approve',
            'tardy_request_reject',

            'duty_calendar_read',
            'duty_calendar_create',
            'duty_calendar_update',
        ];
    }

    public function staffPermissions(): array
    {
        return [
            // ================== Employee management ====================
            // Dashboard
            'staff_dashboard',

            // Employee
            'profile_read',
            'profile_update',
            'user_permission_update',
            'password_reset_mail',
            'password_update',
            'profile_image_view',

            // Official document request
            'official_document_request_read',
            'official_document_request_create',
            'official_document_request_update',
            'official_document_request_delete',

            // Complain
            'complain_read',
            'complain_create',
            'complain_update',

            // Verbal Warning
            'verbal_warning_read',
            'verbal_warning_create',
            'verbal_warning_update',
            'verbal_warning_delete',

            // Performance
            'performance_read',
            'performance_create',
            'performance_update',
            'performance_delete',

            // ================== Attendance management ====================
            // Duty calender
            'duty_calendar_read',

            // Attendance
            'attendance_read',
            'attendance_create',

            // ================== Leave management ====================
            // Leave request
            'leave_request_read',
            'leave_request_create',
            'leave_request_delete',

            // Leave balance
            'leave_balance_read',

            // ================== Payroll management ====================
            // Advance
            'advance_read',

            // Salary generate
            'salary_read',

            // ================== Communication management ====================
            // Meeting
            'meeting_read',
            'meeting_create',
            'meeting_update',
            'meeting_delete',

            // Appointment
            'appointment_read',
            'appointment_create',
            'appointment_update',
            'appointment_delete',

            // Notice
            'notice_read',

            // Bulletin
            'bulletin_read',

            // ================== Office work management ====================
            // Project
            'project_read',
            'project_create',
            'project_update',
            'project_delete',
            'project_member_read',
            'project_member_delete',
            'project_complete',
            'project_payment',

            // Project file
            'project_file_read',
            'project_file_create',
            'project_file_update',
            'project_file_delete',
            'project_file_comment',

            // Project discussion
            'project_discussion_read',
            'project_discussion_create',
            'project_discussion_update',
            'project_discussion_delete',
            'project_discussion_comment',

            // Project note
            'project_note_read',
            'project_note_create',
            'project_note_update',
            'project_note_delete',

            // Task
            'task_read',
            'task_create',
            'task_update',
            'task_delete',
            'task_assign_read',
            'task_assign_delete',
            'task_complete',
            'task_member_read',

            // Task discussion
            'task_discussion_read',
            'task_discussion_create',
            'task_discussion_update',
            'task_discussion_delete',
            'task_discussion_comment',

            // Task note
            'task_note_read',
            'task_note_create',
            'task_note_update',
            'task_note_delete',

            // Task file
            'task_file_read',
            'task_file_create',
            'task_file_update',
            'task_file_delete',
            'task_file_comment',

            // Travel plan
            'travel_plan_read',
            'travel_plan_create',
            'travel_plan_update',
            'travel_plan_delete',

            // Travel meeting
            'travel_meeting_read',
            'travel_meeting_create',
            'travel_meeting_update',
            'travel_meeting_delete',

            // Travel expense
            'travel_expense_read',
            'travel_expense_create',
            'travel_expense_update',
            'travel_expense_delete',

            // Travel workflow
            'travel_workflow_read',
            'travel_workflow_create',
            'travel_workflow_update',
            'travel_workflow_delete',

            // Award
            'award_read',

            // Visit
            'visit_read',
            'visit_update',

            // ================== Profile management ====================
            // Profile
            'contract_profile',
            'attendance_profile',
            'notice_profile',
            'leave_request_profile',
            'visit_profile',
            'phonebook_profile',
            'appointment_profile',
            'advance_profile',
            'commission_profile',
            'salary_profile',
            'project_profile',
            'task_profile',
            'award_profile',
            'travel_profile',

            'tardy_record_read',
            'tardy_record_create',
            'tardy_record_update',

            'tardy_request_read',
            'tardy_request_create',
            'tardy_request_update',

            'duty_calendar_read',
        ];
    }
}
