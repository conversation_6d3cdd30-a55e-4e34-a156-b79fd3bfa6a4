<?php

namespace Modules\EmployeePerformance\Http\Controllers;

use App\Http\Controllers\Controller;
use Exception;
use Illuminate\Http\Request;
use Modules\EmployeePerformance\Http\Requests\AwardRequest;
use Modules\EmployeePerformance\Repositories\AwardRepository;

use function redirect;
use function view;

class AwardController extends Controller
{
    protected $repository;

    public function __construct(AwardRepository $repository)
    {
        $this->repository = $repository;
    }

    public function index(Request $request)
    {
        try {
            $data['title'] = _trans('award.Rewards');
            $data['class'] = 'award_table_class';

            $data['employees'] = $this->repository->employees();
            $data['award_types'] = $this->repository->types();
            $data['collection'] = $this->repository->getPaginateData($request);

            return view('employeeperformance::award.index')->with($data);
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function create()
    {
        try {
            $data['title'] = _trans('award.Create Reward');
            $data['employees'] = $this->repository->employees();
            $data['award_types'] = $this->repository->types();

            return view('employeeperformance::award.create')->with($data);
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function store(AwardRequest $request)
    {
        try {
            $this->repository->store($request->validated());

            return redirect()->route('award.index')->with('success', _trans('award.created successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function view($id)
    {
        try {
            $data['title'] = _trans('award.View Reward');
            $data['availableAwardTypes'] = $this->repository->availableAwardTypes();
            $data['award'] = $this->repository->show($id);

            return view('employeeperformance::award.view')->with($data);
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function edit($id)
    {
        try {
            $data['title'] = _trans('award.Edit Reward');
            $data['employees'] = $this->repository->employees();
            $data['award_types'] = $this->repository->types();
            $data['award'] = $this->repository->show($id);

            return view('employeeperformance::award.edit')->with($data);
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function update(AwardRequest $request, $id)
    {
        try {
            $this->repository->update($id, $request->validated());

            return redirect()->route('award.index')->with('success', _trans('award.updated successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function delete($id)
    {
        try {
            $this->repository->delete($id);

            return redirect()->route('award.index')->with('success', _trans('award.Deleted successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function getUnseenAward()
    {
        try {
            $unseenAward = $this->repository->getUnseenAward();

            return successResponse(_trans('common.Unseen Rewards'), $unseenAward);
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function seenAward(Request $request)
    {
        try {
            $this->repository->seenAward($request);

            return successResponse(_trans('common.Award marked as seen'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }
}
