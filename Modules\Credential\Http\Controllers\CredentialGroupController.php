<?php

namespace Modules\Credential\Http\Controllers;

use Illuminate\Contracts\View\View;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use Modules\Credential\Entities\CredentialGroup;

use function _trans;

class CredentialGroupController extends Controller
{
    protected $types = [
        'social media',
        'messaging & chat',
        'video conferencing',
        'productivity tools',
        'office & document tools',
        'email & calendar',
        'design & creative tools',
        'development & coding',
        'cloud storage & file sharing',
        'streaming & entertainment',
        'browsers',
        'password managers & security',
    ];

    public function __construct(protected CredentialGroup $model, protected CredentialGroup $credentialGroup) {}

    /**
     * Display a listing of active credential groups with active credentials.
     *
     * Groups are filtered to include only those with at least one active credential.
     * The result is ordered by name and type, then grouped by type.
     *
     * @return View The credential groups index view.
     */
    public function index()
    {
        $data['title'] = _trans('common.Credential Groups');
        $data['formTitle'] = _trans('common.Add New Credential');
        $user = Auth::user();

        $data['collection'] = $this->model->where('status', 'active')
            ->with(['credentials' => function ($query) use ($user) {
                $query->where('status', 'active')
                    ->orWhere(function ($q) use ($user) {
                        $q->where('status', 'inactive')
                            ->where('created_by', $user->id);
                    });
            }])
            ->whereHas('credentials', function ($query) use ($user) {
                $query->where(function ($q) use ($user) {
                    $q->where('status', 'active')
                        ->orWhere(function ($q) use ($user) {
                            $q->where('status', 'inactive')
                                ->where('created_by', $user->id);
                        });
                });
            })
            ->withCount(['credentials as accessible_credentials_count' => function ($query) use ($user) {
                $query->where(function ($q) use ($user) {
                    $q->where('status', 'active')
                        ->orWhere(function ($q) use ($user) {
                            $q->where('status', 'inactive')
                                ->where('created_by', $user->id);
                        });
                })->whereHas('accesses', function ($q) use ($user) {
                    $q->where(function ($subQ) use ($user) {
                        $subQ->where('user_id', $user->id)
                            ->orWhere('branch_id', $user->branch_id)
                            ->orWhere('department_id', $user->department_id);
                    });
                });
            }])
            ->having('accessible_credentials_count', '>', 0)
            ->orderBy('name')
            ->orderBy('type')
            ->paginate(12);

        $data['credentialGroups'] = $this->credentialGroup
            ->select('id', 'name', 'slug', 'company_id')
            ->where('company_id', Auth::user()->company_id)
            ->get();

        return view('credential::index', $data);
    }
}
