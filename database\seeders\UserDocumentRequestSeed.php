<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;
use Modules\EmployeeDocuments\Entities\UserDocumentRequest;

class UserDocumentRequestSeed extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Disable foreign key constraints using the same approach as RegularMigrate
        Schema::disableForeignKeyConstraints();

        // Define the possible request types
        $requestTypes = ['NOC', 'Salary Certificate', 'Others'];

        UserDocumentRequest::insert([
            'status' => 'active',
            'user_id' => 1,
            'request_type' => $requestTypes[rand(0, 2)],
            'request_description' => 'Sample request description '. 1,
            'approved' => rand(0, 1),
            'request_date' => now()->subDays(rand(25, 30)),
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Re-enable foreign key constraints
        Schema::enableForeignKeyConstraints();
    }
}
