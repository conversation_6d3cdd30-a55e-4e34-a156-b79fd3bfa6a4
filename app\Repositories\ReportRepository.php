<?php

namespace App\Repositories;

use App\Models\Attendance\Attendance;
use App\Models\Attendance\Weekend;
use App\Models\Holiday;
use App\Models\Leave\LeaveRequest;
use App\Models\User;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Support\Facades\Auth;

class ReportRepository
{
    public function __construct(
        protected LeaveRequest $leaveRequestModel,
        protected Attendance $attendanceModel,
        protected User $userModel,
        protected Weekend $weekendModel,
        protected Holiday $holidayModel
    ) {}

    public function leaveReport($request, $fields = ['*'])
    {
        return $this->leaveRequestModel->select($fields)
            ->latest()
            ->with('applicant', 'type', 'referredTo')
            ->when($request->search, function ($query) use ($request) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->whereHas('applicant', function ($q2) use ($search) {
                        $q2->where('name', 'like', "%{$search}%")
                            ->orWhereHas('designation', function ($q3) use ($search) {
                                $q3->where('title', 'like', "%{$search}%");
                            });
                    });
                });
            })
            ->when($request->status, function ($query) use ($request) {
                $query->where('status', $request->status);
            })
            ->paginate($request->limit ?? 10);
    }

    public function attendanceReport($request, $fields = ['*'])
    {
        $userId = $request->user_id;
        $month = $request->dob ? Carbon::parse($request->dob) : now();
        $startOfMonth = $month->copy()->startOfMonth();

        if ($month->isSameMonth(now())) {
            $endOfMonth = now()->copy()->endOfDay();
        } elseif ($month->lt(now())) {
            $endOfMonth = $month->copy()->endOfMonth();
        } else {
            return collect();
        }

        $weekendDays = $this->weekendModel
            ->where('is_weekend', 1)
            ->pluck('name')
            ->map(fn ($d) => strtolower($d))
            ->toArray();

        $holidays = $this->holidayModel
            ->whereBetween('date', [$startOfMonth, $endOfMonth])
            ->pluck('date')
            ->map(fn ($d) => Carbon::parse($d)->toDateString())
            ->flip();

        $usersQuery = $this->userModel->select($fields);
        if ($userId) {
            $usersQuery->where('id', $userId);
        }

        $usersPaginated = $usersQuery->paginate($request->limit ?? 10);

        $userIds = $usersPaginated->pluck('id');

        $attendances = $this->attendanceModel
            ->whereIn('user_id', $userIds)
            ->whereBetween('date', [$startOfMonth, $endOfMonth])
            ->get()
            ->groupBy('user_id')
            ->map(fn ($rows) => $rows->pluck('date')->map(fn ($d) => Carbon::parse($d)->toDateString())->flip());

        $leaves = $this->leaveRequestModel
            ->whereIn('user_id', $userIds)
            ->where(function ($q) use ($startOfMonth, $endOfMonth) {
                $q->whereBetween('leave_from', [$startOfMonth, $endOfMonth])
                    ->orWhereBetween('leave_to', [$startOfMonth, $endOfMonth])
                    ->orWhere(function ($nested) use ($startOfMonth, $endOfMonth) {
                        $nested->where('leave_from', '<=', $startOfMonth)
                            ->where('leave_to', '>=', $endOfMonth);
                    });
            })
            ->get()
            ->groupBy('user_id');

        $period = CarbonPeriod::create($startOfMonth, $endOfMonth);

        $report = $usersPaginated->getCollection()->map(function ($user) use ($attendances, $leaves, $period, $weekendDays, $holidays) {
            $days = [];
            $userAttendances = $attendances->get($user->id, collect());
            $userLeaves = $leaves->get($user->id, collect());

            foreach ($period as $date) {
                $dateStr = $date->toDateString();
                $dayName = strtolower($date->format('l'));
                $status = 'Absent';

                if (isset($holidays[$dateStr])) {
                    $status = 'Holiday';
                } elseif (in_array($dayName, $weekendDays)) {
                    $status = 'Weekend';
                } elseif (isset($userAttendances[$dateStr])) {
                    $status = 'Present';
                } elseif ($userLeaves->contains(fn ($leave) => $date->between($leave->leave_from, $leave->leave_to))) {
                    $status = 'Leave';
                }

                $days[$dateStr] = $status;
            }

            return [
                'user_id' => $user->id,
                'name' => $user->name,
                'employee_id' => $user->employee_id,
                'image' => $user->image,
                'month' => $date->format('F Y'),
                'days' => $days,
            ];
        });

        $usersPaginated->setCollection($report);

        return $usersPaginated;
    }

    public function attendanceDetails($request, $fields = ['*'])
    {
        $userId = $request->user_id;
        $month = $request->dob ? Carbon::parse($request->dob) : now();
        $startOfMonth = $month->copy()->startOfMonth();

        if ($month->isSameMonth(now())) {
            $endOfMonth = now()->copy()->endOfDay();
        } elseif ($month->lt(now())) {
            $endOfMonth = $month->copy()->endOfMonth();
        } else {
            return collect();
        }

        $weekendDays = $this->weekendModel
            ->where('is_weekend', 1)
            ->pluck('name')
            ->map(fn ($d) => strtolower($d))
            ->toArray();

        $holidays = $this->holidayModel
            ->whereBetween('date', [$startOfMonth, $endOfMonth])
            ->pluck('date')
            ->map(fn ($d) => Carbon::parse($d)->toDateString())
            ->flip();

        $usersQuery = $this->userModel->select($fields);
        if ($userId) {
            $usersQuery->where('id', $userId);
        }

        $usersPaginated = $usersQuery->paginate($request->limit ?? 10);
        $userIds = $usersPaginated->pluck('id');

        $attendances = $this->attendanceModel
            ->select('user_id', 'date', 'check_in', 'check_out', 'stay_time', 'late_duration', 'early_exit_duration', 'worked_time')
            ->whereIn('user_id', $userIds)
            ->whereBetween('date', [$startOfMonth, $endOfMonth])
            ->get()
            ->groupBy('user_id')
            ->map(function ($rows) {
                return $rows->mapWithKeys(function ($row) {
                    $date = Carbon::parse($row->date)->toDateString();

                    return [
                        $date => [
                            'user_id' => $row->user_id,
                            'date' => $date,
                            'check_in' => $row->check_in ? Carbon::parse($row->check_in)->format('H:i:s') : null,
                            'check_out' => $row->check_out ? Carbon::parse($row->check_out)->format('H:i:s') : null,
                            'stay_time' => $row->stay_time ? Carbon::parse($row->stay_time)->format('H:i:s') : null,
                            'late_duration' => $row->late_duration ? Carbon::parse($row->late_duration)->format('H:i:s') : null,
                            'early_exit_duration' => $row->early_exit_duration ? Carbon::parse($row->early_exit_duration)->format('H:i:s') : null,
                            'worked_time' => $row->worked_time ? Carbon::parse($row->worked_time)->format('H:i:s') : null,
                        ],
                    ];
                });
            });

        $leaves = $this->leaveRequestModel
            ->whereIn('user_id', $userIds)
            ->where(function ($q) use ($startOfMonth, $endOfMonth) {
                $q->whereBetween('leave_from', [$startOfMonth, $endOfMonth])
                    ->orWhereBetween('leave_to', [$startOfMonth, $endOfMonth])
                    ->orWhere(function ($nested) use ($startOfMonth, $endOfMonth) {
                        $nested->where('leave_from', '<=', $startOfMonth)
                            ->where('leave_to', '>=', $endOfMonth);
                    });
            })
            ->get()
            ->groupBy('user_id');

        $period = CarbonPeriod::create($startOfMonth, $endOfMonth);

        $report = $usersPaginated->getCollection()->map(function ($user) use ($attendances, $leaves, $period, $weekendDays, $holidays) {
            $days = [];
            $userAttendances = $attendances->get($user->id, collect());
            $userLeaves = $leaves->get($user->id, collect());

            foreach ($period as $date) {
                $dateStr = $date->toDateString();
                $dayName = strtolower($date->format('l'));

                if (isset($holidays[$dateStr])) {
                    $days[$dateStr] = ['status' => 'Holiday'];
                } elseif (in_array($dayName, $weekendDays)) {
                    $days[$dateStr] = ['status' => 'Weekend'];
                } elseif (isset($userAttendances[$dateStr])) {
                    $days[$dateStr] = $userAttendances[$dateStr]; // full attendance details
                } elseif ($userLeaves->contains(fn ($leave) => $date->between($leave->leave_from, $leave->leave_to))) {
                    $days[$dateStr] = ['status' => 'Leave'];
                } else {
                    $days[$dateStr] = ['status' => 'Absent'];
                }
            }

            return [
                'user_id' => $user->id,
                'name' => $user->name,
                'employee_id' => $user->employee_id,
                'image' => $user->image,
                'month' => $period->first()->format('F Y'),
                'days' => $days,
            ];
        });

        $usersPaginated->setCollection($report);

        return $usersPaginated;
    }

    public function processChronologicalDays($collection)
    {
        $firstEmployee = $collection->first();
        if (! $firstEmployee || ! isset($firstEmployee['days'])) {
            return [];
        }

        $days = $firstEmployee['days'];
        $chronologicalDays = [];

        foreach ($days as $date => $dayData) {
            $carbonDate = Carbon::parse($date);

            if (isset($dayData['status'])) {
                if ($dayData['status'] === 'Weekend') {
                    $chronologicalDays[$date] = [
                        'type' => 'weekend',
                        'date' => $date,
                        'formatted_date' => $carbonDate->format('d M Y'),
                        'day_indicator' => 'W',
                    ];
                } elseif ($dayData['status'] === 'Holiday') {
                    $chronologicalDays[$date] = [
                        'type' => 'holiday',
                        'date' => $date,
                        'formatted_date' => $carbonDate->format('d M Y'),
                        'day_indicator' => 'H',
                    ];
                }
            } else {
                // Regular attendance day
                $chronologicalDays[$date] = [
                    'type' => 'regular',
                    'date' => $date,
                    'formatted_date' => $carbonDate->format('d F Y'),
                ];
            }
        }

        // Sort all days chronologically by date
        ksort($chronologicalDays);

        return $chronologicalDays;
    }

    public function processEmployeeStats($collection)
    {
        $employeeStats = [];

        foreach ($collection as $employee) {
            $totalAbsent = 0;
            $totalPresent = 0;
            $employeeDays = $employee['days'];

            foreach ($employeeDays as $dayData) {
                if (isset($dayData['status'])) {
                    if ($dayData['status'] === 'Absent') {
                        $totalAbsent++;
                    } elseif (
                        $dayData['status'] === 'Weekend' ||
                        $dayData['status'] === 'Holiday'
                    ) {
                        // Weekends and holidays are not counted in absence/presence
                        continue;
                    } elseif (
                        $dayData['status'] === 'Present' ||
                        (isset($dayData['check_in']) && $dayData['check_in'])
                    ) {
                        $totalPresent++;
                    }
                } else {
                    $totalPresent++;
                }
            }

            // Process image data
            $imageData = json_decode($employee['image'], true);
            $imageUrl = $imageData && isset($imageData['file'])
                ? asset('storage/'.$imageData['file'])
                : asset('assets/images/avatar.png');

            $employeeStats[$employee['user_id']] = [
                'totalAbsent' => $totalAbsent,
                'totalPresent' => $totalPresent,
                'imageUrl' => $imageUrl,
            ];
        }

        return $employeeStats;
    }

    /**
     * Format time string to readable format (e.g., "5Hrs 30Min")
     */
    private function formatTimeString($timeString, $default = '--')
    {
        if (! $timeString || $timeString === '--') {
            return $default;
        }

        try {
            // Handle different time formats
            if (preg_match('/(\d{4}-\d{2}-\d{2})\s+(\d{2}:\d{2}:\d{2})/', $timeString, $matches)) {
                // Full datetime format: "2025-09-01 09:49:00"
                $time = Carbon::parse($matches[2]);
                $hours = $time->hour;
                $minutes = $time->minute;
            } elseif (preg_match('/(\d{2}):(\d{2}):(\d{2})/', $timeString, $matches)) {
                // Time format: "09:49:00" or "08:14:00"
                $hours = (int) $matches[1];
                $minutes = (int) $matches[2];
            } else {
                return $default;
            }

            // Format as "5Hrs 30Min" or "30Min" if hours is 0
            if ($hours > 0) {
                return $minutes > 0 ? "{$hours}Hrs {$minutes}Min" : "{$hours}Hrs";
            } else {
                return $minutes > 0 ? "{$minutes}Min" : '0Min';
            }
        } catch (\Exception $e) {
            return $default;
        }
    }

    /**
     * Format check-in/check-out time to show only time (e.g., "09:49 AM")
     */
    private function formatCheckTime($timeString, $default = '--')
    {
        if (! $timeString || $timeString === '--') {
            return $default;
        }

        try {
            // Handle different time formats
            if (preg_match('/(\d{4}-\d{2}-\d{2})\s+(\d{2}:\d{2}:\d{2})/', $timeString, $matches)) {
                // Full datetime format: "2025-09-01 09:49:00"
                $time = Carbon::parse($matches[2]);

                return $time->format('h:i A');
            } elseif (preg_match('/(\d{2}):(\d{2}):(\d{2})/', $timeString, $matches)) {
                // Time format: "09:49:00"
                $time = Carbon::parse($matches[0]);

                return $time->format('h:i A');
            } else {
                return $default;
            }
        } catch (\Exception $e) {
            return $default;
        }
    }

    public function monthlyTimesheet($request)
    {
        $employeeId = $request->get('employee_id') ?? Auth::id();
        $month = $request->dob ? Carbon::parse($request->dob) : now();
        $startOfMonth = $month->copy()->startOfMonth();
        $endOfMonth = $month->copy()->endOfMonth();

        // Fetch weekend days (0=Sunday, 6=Saturday) - cache this if possible
        $weekendDays = $this->weekendModel
            ->where('is_weekend', 1)
            ->pluck('name')
            ->map(fn ($d) => strtolower($d))
            ->toArray();

        // Fetch holidays for the month
        $holidays = $this->holidayModel
            ->whereBetween('date', [$startOfMonth, $endOfMonth])
            ->pluck('date')
            ->map(fn ($d) => Carbon::parse($d)->toDateString())
            ->flip(); // Use flip for O(1) lookup

        // Optimized query: Fetch user with eager loaded relationships
        $user = $this->userModel
            ->select('id', 'name', 'designation_id')
            ->with([
                'designation:id,title', // Only select needed fields
                'attendances' => function ($query) use ($startOfMonth, $endOfMonth) {
                    $query->select('id', 'user_id', 'date', 'check_in', 'check_out', 'stay_time')
                        ->whereBetween('date', [$startOfMonth, $endOfMonth])
                        ->orderBy('date'); // Order for better performance
                },
                'approvedLeaves' => function ($query) use ($startOfMonth, $endOfMonth) {
                    $query->select('id', 'user_id', 'reason', 'leave_from', 'leave_to')
                        ->where('status', 'approved') // Only approved leaves
                        ->where(function ($q) use ($startOfMonth, $endOfMonth) {
                            $q->whereBetween('leave_from', [$startOfMonth, $endOfMonth])
                                ->orWhereBetween('leave_to', [$startOfMonth, $endOfMonth])
                                ->orWhere(function ($q2) use ($startOfMonth, $endOfMonth) {
                                    $q2->where('leave_from', '<=', $startOfMonth)
                                        ->where('leave_to', '>=', $endOfMonth);
                                });
                        });
                },
            ])
            ->where('id', $employeeId)
            ->first();

        if (! $user) {
            return null;
        }

        // Pre-process attendance and leave data for O(1) lookup
        $attendanceMap = $user->attendances->keyBy('date');
        $leaveMap = collect();

        // Process leaves to create a map of dates
        foreach ($user->approvedLeaves as $leave) {
            $leaveStart = Carbon::parse($leave->leave_from);
            $leaveEnd = Carbon::parse($leave->leave_to);

            for ($date = $leaveStart->copy(); $date->lte($leaveEnd); $date->addDay()) {
                $leaveMap->put($date->toDateString(), $leave);
            }
        }

        $data = [];
        $totalWorkedMinutes = 0;
        $totalWorkingDays = 0;
        $totalPossibleWorkingDays = 0;

        // Generate date period for the month
        $period = CarbonPeriod::create($startOfMonth, $endOfMonth);

        foreach ($period as $day) {
            $dayName = strtolower($day->format('l'));
            $dateString = $day->toDateString();
            $isWeekend = in_array($dayName, $weekendDays);
            $isHoliday = isset($holidays[$dateString]);

            // Count only working days for totals
            if (! $isWeekend && ! $isHoliday) {
                $totalPossibleWorkingDays++;
            }

            $attendance = $attendanceMap->get($dateString);
            $leave = $leaveMap->get($dateString);

            // Process stay time and calculate worked minutes
            $stayTime = '--';
            $reason = '--';

            if ($attendance && $attendance->stay_time) {
                $stayTime = $attendance->stay_time;
                $reason = 'Work';

                // Parse stay_time to calculate total minutes
                if (preg_match('/(\d+):(\d+):(\d+)/', $attendance->stay_time, $matches)) {
                    $hours = (int) $matches[1];
                    $minutes = (int) $matches[2];
                    $seconds = (int) $matches[3];

                    if (! $isWeekend && ! $isHoliday) {
                        $totalWorkedMinutes += ($hours * 60) + $minutes + ($seconds / 60);
                        $totalWorkingDays++;
                    }
                }
            } elseif ($leave) {
                $stayTime = '--';
                $reason = $leave->reason;
            }

            $data[] = [
                'day' => $day->format('l'),
                'date' => $dateString,
                'check_in' => $this->formatCheckTime($attendance->check_in ?? '--'),
                'check_out' => $this->formatCheckTime($attendance->check_out ?? '--'),
                'stay_time' => $this->formatTimeString($stayTime),
                'reason' => $reason,
                'is_weekend' => $isWeekend,
                'is_holiday' => $isHoliday,
            ];
        }

        // Calculate attendance percentage
        $attendancePercentage = $totalPossibleWorkingDays > 0
            ? round(($totalWorkingDays / $totalPossibleWorkingDays) * 100, 2)
            : 0;

        // Calculate total worked hours
        $totalHours = intdiv($totalWorkedMinutes, 60);
        $totalMinutes = $totalWorkedMinutes % 60;

        // Calculate total possible working hours (8 hours/day)
        $totalPossibleMinutes = $totalPossibleWorkingDays * 8 * 60;
        $possibleHours = intdiv($totalPossibleMinutes, 60);
        $possibleMinutes = $totalPossibleMinutes % 60;

        return [
            'id' => $user->id,
            'name' => $user->name,
            'designation' => $user->designation->title ?? 'N/A',
            'month' => $month->format('F Y'),
            'attendance' => "{$attendancePercentage}%",
            'total_working_hours' => "{$totalHours}H:{$totalMinutes}M / {$possibleHours}H:{$possibleMinutes}M",
            'total_working_days' => "{$totalWorkingDays} days / {$totalPossibleWorkingDays} days",
            'data' => $data,
        ];
    }
}
