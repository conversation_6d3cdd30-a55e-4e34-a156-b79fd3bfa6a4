@extends('backend.layouts.app')
@section('title', @$title)
@section('content')

    <x-container :title="@$title">
        <x-table :bulkAction="false" buttonTitle="Create" buttonRoute="{{ route('employee-performance.create') }}"
            permission="performance_create">
            <table class="table table-bordered" id="table">
                <thead class="thead">
                    <tr>
                        <th class="sorting-asc w-60 no-export">
                            <div class="check-box">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="all_check" />
                                </div>
                            </div>
                        </th>
                        <th class="w-60">{{ _trans('common.SL') }}</th>
                        <th class="sorting-asc">{{ _trans('common.Date') }}</th>
                        <th class="sorting-asc">{{ _trans('common.Employee') }}</th>
                        <th class="sorting-asc">{{ _trans('common.Team Leader / Rep') }}</th>
                        <th class="sorting-asc">{{ _trans('common.Employment Status') }}</th>
                        <th class="sorting-asc">{{ _trans('common.Leave Status') }}</th>
                        <th class="sorting-asc mxw-100">{{ _trans('common.Overall Performance') }}</th>
                        <th class="sorting-asc">{{ _trans('common.Count') }}</th>
                        <th class="sorting-asc no-export">{{ _trans('common.Actions') }}</th>
                    </tr>
                </thead>
                <tbody class="tbody ">
                    @forelse ($collection as $key => $row)
                        <tr>
                            <td class="no-export">
                                <div class="check-box">
                                    <div class="form-check">
                                        <input class="form-check-input column_id" id="column_{{ $row->id }}"
                                            onclick="columnID({{ $row->id }})" type="checkbox" name="column_id[]"
                                            value="{{ $row->id }}">
                                    </div>
                                </div>
                            </td>
                            <td>{{ $loop->iteration }}</td>
                            <td>{{ date('m/d/Y', strtotime($row->date)) }}</td>
                            <td>{{ @$row->user->name }}</td>
                            <td>{{ $row->team_leader_or_rep }}</td>
                            <td>{{ $row->employment_status }}</td>
                            <td>{{ $row->leave_status }}</td>
                            <td>{{ $row->overall_performance }}</td>
                            <td>
                                <p class="mb-0">Verbal Warning : {{ $row->verbal_warning_count }}</p>
                                <p class="mb-0">Write Up Warning : {{ $row->write_up_count }}</p>
                            </td>
                            <td class="no-export">
                                <div class="dropdown dropdown-action">
                                    <button type="button" class="btn-dropdown" data-bs-toggle="dropdown"
                                        aria-expanded="false">
                                        <i class="las la-ellipsis-h"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        @if (hasPermission('performance_update'))
                                            <a href="{{ route('employee-performance.edit', $row->id) }}"
                                                class="dropdown-item">
                                                <x-common.icons name="edit" class="text-title" size="16"
                                                    stroke-width="1.5" />
                                                {{ _trans('common.Edit') }}
                                            </a>
                                        @endif
                                        @if (hasPermission('performance_delete'))
                                            <x-table.action.delete
                                                url="{{ route('employee-performance.destroy', $row->id) }}">
                                            </x-table.action.delete>
                                        @endif
                                    </ul>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <x-table.empty :colspan="10" />
                    @endforelse
                </tbody>
            </table>
            <x-table.pagination :data="@$collection"></x-table.pagination>
        </x-table>
    </x-container>
@endsection
