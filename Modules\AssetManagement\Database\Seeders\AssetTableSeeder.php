<?php

namespace Modules\AssetManagement\Database\Seeders;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Seeder;
use Modules\AssetManagement\Entities\Asset;
use Modules\AssetManagement\Entities\AssetCategory;

class AssetTableSeeder extends Seeder
{
    public function run()
    {
        Model::unguard();

        $categories = AssetCategory::pluck('id', 'title')->toArray();
        $users = User::pluck('id')->toArray();
        $dummyAttachment = json_encode([
            'disk' => 'public',
            'files' => ['uploads/2025/08/26/asset_manage/dummy.jpg'],
        ]);

        $totalRecords = rand(60, 70); // number of assets to create
        $assets = [];

        for ($i = 1; $i <= $totalRecords; $i++) {
            $category = $categories[array_rand($categories)];
            $assignedUser = ! empty($users) ? $users[array_rand($users)] : null;

            $assets[] = [
                'name' => 'Asset '.$i,
                'asset_code' => 'AS-'.str_pad($i, 3, '0', STR_PAD_LEFT),
                'brand' => ['Dell', 'Cisco', 'Toyota', 'Bosch', 'Herman Miller'][array_rand(['Dell', 'Cisco', 'Toyota', 'Bosch', 'Herman Miller'])],
                'model' => 'Model '.rand(100, 999),
                'serial_number' => 'SN'.rand(100000, 999999),
                'invoice_number' => 'INV'.str_pad($i, 3, '0', STR_PAD_LEFT),
                'purchase_cost' => rand(100, 25000),
                'purchase_date' => now()->subDays(rand(0, 1000))->format('Y-m-d'),
                'warranty_expiry' => now()->addYears(rand(1, 5))->format('Y-m-d'),
                'description' => 'Description for asset '.$i,
                'attachments' => $dummyAttachment,
                'category_id' => $category,
                'vendor_location' => 'Vendor '.rand(1, 20),
                'assigned_to' => $assignedUser,
                'status' => ['available', 'maintenance', 'assigned'][array_rand(['available', 'maintenance', 'assigned'])],
                'condition' => ['new', 'good', 'fair'][array_rand(['new', 'good', 'fair'])],
                'maintenance_status' => ['not_required', 'scheduled'][array_rand(['not_required', 'scheduled'])],
                'created_by' => 1,
                'updated_by' => 1,
                'company_id' => 1,
                'branch_id' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        Asset::insert($assets);
    }
}
