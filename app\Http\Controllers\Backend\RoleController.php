<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\RoleStoreRequest;
use App\Models\Role;
use App\Permissions\PermissionManager;
use App\Repositories\RoleRepository;
use Exception;
use Illuminate\Http\Request;

class RoleController extends Controller
{
    public function __construct(
        protected RoleRepository $repository,
        protected Role $model
    ) {}

    public function index(Request $request)
    {
        $data['title'] = _trans('common.Roles');
        $data['collection'] = $this->repository->getPaginateData($request);

        if ($request->filled('id')) {
            $data['formTitle'] = _trans('common.Edit Role');
            $data['role'] = $this->repository->show($request->id);
        }

        return view('backend.roles.index')->with($data);
    }

    public function show($id)
    {
        return $this->repository->show($id);
    }

    public function create(PermissionManager $permissions)
    {
        $data['title'] = _trans('common.Add New Role');
        $data['permissions'] = $permissions->all();

        return view('backend.roles.create')->with($data);
    }

    public function store(RoleStoreRequest $request)
    {
        try {
            $this->repository->store($request->validated());

            return redirect()->route('roles.index')->with('success', _trans('alert.Role created successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function edit(PermissionManager $permissions, $id)
    {
        $data['title'] = 'Edit Role';
        $data['role'] = $this->repository->show($id);
        $data['permissions'] = $permissions->all();

        return view('backend.roles.edit')->with($data);
    }

    public function update(RoleStoreRequest $request, $id)
    {
        try {
            $this->repository->update($id, $request->validated());

            return redirect()->route('roles.index')->with('success', _trans('alert.Role updated successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function destroy($id)
    {
        try {
            $result = $this->repository->destroy($id);

            if (! $result) {
                return redirect()->route('roles.index')->with('error', _trans('alert.You cannot delete this role'));
            }

            return redirect()->route('roles.index')->with('success', _trans('alert.Role deleted successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }
}
