<?php

namespace App\Http\Controllers\Backend;

use App\Enums\EmployeeStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\ChangePasswordRequest;
use App\Http\Requests\DepartmentReqeust;
use App\Http\Requests\DesignationReqeust;
use App\Http\Requests\User\EmployeeSetupRequest;
use App\Http\Requests\User\OfficialSetupRequest;
use App\Http\Requests\User\PersonalDocumentRequest;
use App\Http\Requests\User\PersonalInfoRequest;
use App\Http\Requests\User\SalaryConfigurationRequest;
use App\Models\Attendance\Attendance;
use App\Models\User;
use App\Permissions\PermissionManager;
use App\Repositories\DepartmentRepository;
use App\Repositories\DesignationRepository;
use App\Repositories\DutyCalendarRepository;
use App\Repositories\DutyScheduleRepository;
use App\Repositories\ProfileRepository;
use App\Repositories\UserRepositoryV2;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Modules\AssetManagement\Repositories\AssetRepository;
use Throwable;

class UserControllerV2 extends Controller
{
    public function __construct(
        protected UserRepositoryV2 $repository,
        protected ProfileRepository $profile,
        protected DesignationRepository $designation,
        protected DepartmentRepository $department,
        protected DutyScheduleRepository $dutyScheduleRepository,
        protected DutyCalendarRepository $calendarRepository,
        protected AssetRepository $assetRepository
    ) {}

    public function index(Request $request)
    {
        $data['title'] = _trans('common.Employee List');
        $data['designations'] = $this->repository->designations();
        $data['statuses'] = EmployeeStatus::labels();
        $data['departments'] = $this->repository->departments();
        $data['employeeSummary'] = $this->repository->employeeSummary();
        $data['collection'] = $this->repository->getPaginateData($request);

        return view('backend.user.index')->with($data);
    }

    public function create()
    {
        $data = $this->employeeCreateEditData();
        $data['title'] = _trans('common.Add Employee');

        if (request()->has('step') && ! request()->has('process_id')) {
            return redirect()->route('user.form');
        }

        if (request()->has('process_id')) {
            $user_id = decrypt(request('process_id'));
            $data['user'] = $this->repository->show($user_id, ['attendanceConfig', 'personalDocument', 'dutySchedules']);
            $data['processId'] = encrypt($user_id);
        }

        return view('backend.user.create')->with($data);
    }

    private function employeeCreateEditData()
    {
        $data['designations'] = $this->repository->designations();
        $data['departments'] = $this->repository->departments();
        $data['roles'] = $this->repository->roles();
        $data['countries'] = $this->repository->countries();
        $data['attendance_methods'] = $this->getAttendanceMethod();
        $data['managers'] = $this->repository->managers();
        $data['timezones'] = globalSetting('timezone', 'base');
        $data['weekends'] = $this->repository->weekends();
        $data['defaultWeekends'] = $this->repository->defaultWeekends();
        $data['dutySchedules'] = $this->repository->dutySchedules();
        $data['statuses'] = EmployeeStatus::labels();

        return $data;
    }

    private function getAttendanceMethod()
    {
        $attendanceData = [
            'Normal Attendance' => 'normal_attendance',
        ];

        $optionalMethods = [
            'QrBasedAttendance' => ['Qr Based Attendance' => 'qr_based_attendance'],
            'SelfieBasedAttendance' => ['Selfie Based Attendance' => 'selfie_based_attendance'],
        ];

        foreach ($optionalMethods as $module => $method) {
            if (isModuleActive($module)) {
                $attendanceData += $method;
            }
        }

        return $attendanceData;
    }

    public function storeEmployeeSetup(EmployeeSetupRequest $request)
    {
        try {
            $user = $this->repository->storeEmployeeSetup($request->validated());
            if ($request->filled('next_step')) {

                return redirect()->route('user.form', ['step' => $request->next_step, 'process_id' => encrypt($user->id)])->with('success', _trans('alert.Employee Setup Successfully'));
            }

            return redirect()->route('user.index')->with('success', _trans('message.Added Successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function storePersonalInfo(PersonalInfoRequest $request)
    {
        try {
            $user = $this->repository->storePersonalInfo($request->user_id, $request->validated());

            if ($request->filled('next_step')) {
                return redirect()->route('user.form', ['step' => $request->next_step, 'process_id' => encrypt($user->id)])->with('success', _trans('alert.Personal Info Successfully'));
            }

            return redirect()->route('user.index')->with('success', _trans('message.Added Successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function storeOfficialSetup(OfficialSetupRequest $request)
    {
        try {
            $user = $this->repository->updateOfficialSetup($request->user_id, $request->validated());
            if ($request->filled('next_step')) {
                return redirect()->route('user.form', ['step' => $request->next_step, 'process_id' => encrypt($user->id)])->with('success', _trans('alert.Official Setup Successfully'));
            }

            return redirect()->route('user.index')->with('success', _trans('message.Added Successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function storeSalaryConfiguration(SalaryConfigurationRequest $request)
    {
        try {
            $data = $request->validated();

            // Process allowances and deductions to filter out disabled items
            if (isset($data['allowances'])) {
                $data['allowances'] = array_filter($data['allowances'], function ($allowance) {
                    return isset($allowance['enabled']) && $allowance['enabled'];
                });
            }

            if (isset($data['deductions'])) {
                $data['deductions'] = array_filter($data['deductions'], function ($deduction) {
                    return isset($deduction['enabled']) && $deduction['enabled'];
                });
            }

            $this->repository->updateSalaryConfiguration($request->user_id, $data);

            if ($request->filled('next_step')) {
                return redirect()->route('user.form', ['step' => $request->next_step, 'process_id' => encrypt($request->user_id)])->with('success', _trans('alert.Salary Configuration Saved Successfully'));
            }

            return redirect()->route('user.index')->with('success', _trans('message.Added Successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function storePersonalDocument(PersonalDocumentRequest $request)
    {
        try {
            $this->repository->updatePersonalDocument($request->user_id, $request->validated());
            if ($request->filled('next_step')) {
                return redirect()->route('user.form', ['step' => $request->next_step, 'process_id' => encrypt($request->user_id)])->with('success', _trans('alert.Personal Document Successfully'));
            }

            return redirect()->route('user.index')->with('success', _trans('message.Added Successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function updateEmployeeSetup(EmployeeSetupRequest $request, $id)
    {
        try {
            $user = $this->repository->updateEmployeeSetup($id, $request->validated());
            if ($request->filled('next_step')) {
                return redirect()->route('user.form', ['step' => $request->next_step, 'process_id' => encrypt($user->id)]);
            }

            return redirect()->route('user.index')->with('success', _trans('alert.Updated Successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function createDepartment(DepartmentReqeust $request)
    {
        try {
            $department = $this->department->store($request->validated());

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $department->id,
                    'title' => $department->title,
                ],
            ]);
        } catch (Throwable $th) {
            return response()->json([
                'success' => false,
                'data' => [],
            ]);
        }
    }

    public function createDesignation(DesignationReqeust $request)
    {
        try {
            $designation = $this->designation->store($request->validated());

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $designation->id,
                    'title' => $designation->title,
                ],
            ]);
        } catch (Throwable $th) {
            return response()->json([
                'success' => false,
                'data' => [],
            ]);
        }
    }

    public function createDutySchedule(Request $request)
    {
        $this->validate($request, [
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after_or_equal:start_time',
            'consider_time' => 'nullable|numeric',
            'status_id' => 'required',
        ]);

        try {
            $schedule = $this->dutyScheduleRepository->store($request);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $schedule->id,
                    'shift' => @$schedule->shift.' ['.Carbon::parse($schedule->start_time)->format('h:i: A').' - '.Carbon::parse($schedule->end_time)->format('h:i: A').']',
                ],
            ]);
        } catch (Throwable $th) {
            return response()->json([
                'success' => false,
                'data' => [],
            ]);
        }
    }

    public function permissionEdit(PermissionManager $permissions, $id)
    {
        $data['title'] = _trans('common.Permission Edit');
        $data['user'] = $this->repository->show($id, 'permissions');
        $data['permissions'] = $permissions->all();

        return view('backend.user.employee.permission')->with($data);
    }

    public function permissionUpdate(Request $request, $id)
    {
        try {
            $this->repository->permissionUpdate($request, $id);

            return redirect()->route('user.index')->with('success', _trans('alert.Permission updated successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function changeStatus(User $user, $status)
    {
        try {
            $this->repository->changeStatus($user, $status);

            return redirect()->back()->with('success', _trans('alert.User Status Change Successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function changePassword(ChangePasswordRequest $request)
    {
        try {
            $user = Auth::user();

            if (! $user) {
                throw new Exception('User not found');
            }

            $payload = $request->validated();

            if (! Hash::check($payload['current_password'], $user->password)) {
                return redirect()->back()->withErrors([
                    'current_password' => _trans('alert.Current password is incorrect'),
                ]);
            }

            $user->update([
                'password' => Hash::make($payload['password']),
            ]);

            Auth::logout();

            return redirect()->route('login')->with('success', _trans('alert.Password updated successfully'));
        } catch (Throwable $e) {
            return catchHandler($e);
        }
    }

    public function delete(User $user)
    {
        try {
            $this->repository->delete($user);

            return redirect()->back()->with('success', _trans('alert.User Deleted Successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function trashed(Request $request)
    {
        $data['title'] = _trans('common.Employee Trash List');
        $data['statuses'] = array_column(EmployeeStatus::cases(), 'value');
        $data['designations'] = $this->designation->getActiveAll();
        $data['collection'] = $this->repository->trashedData($request);

        return view('backend.user.trashed')->with($data);
    }

    public function restore($id)
    {
        try {
            $this->repository->restore($id);

            return redirect()->back()->with('success', _trans('alert.User restore successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function deletePermanently($id)
    {
        try {
            $this->repository->deletePermanently($id);

            return redirect()->back()->with('success', _trans('alert.User Permanently Deleted'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function sendResetMail($id)
    {
        try {
            $this->repository->sendResetMail($id);

            return redirect()->route('user.index')->with('success', _trans('alert.Mail sent successfully.'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function makeHR(Request $request, $user_id)
    {
        try {
            $this->repository->makeHR($user_id);

            return redirect()->route('user.index')->with('success', _trans('alert.HR role updated successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function profile(Request $request)
    {
        $slug = $request->slug;
        if ($request->filled('id')) {
            $user = $this->repository->show($request->id, ['attendanceConfig', 'personalDocument', 'dutySchedules', 'salaryConfig', 'designation', 'manager', 'department']);
        } else {
            $user = Auth::user();
        }

        $data['title'] = _trans('common.Employee Details');
        $data['slug'] = $slug;
        $data['user'] = $user;
        $data['attendance'] = Attendance::where('user_id', $user->id)->first();

        $data['leaveSummary']['types'] = $user->leaveSummary();
        $data['leaveSummary']['annual_leave'] = $user->leaveSummary()->where('is_paid', 1)->sum('assigns_days') ?? 0;
        $data['leaveSummary']['total_taken'] = $user->leaveSummary()->where('is_paid', 1)->sum('approved_days') ?? 0;
        $data['leaveSummary']['pending_leave'] = $user->leaveSummary()->where('is_paid', 1)->sum('remaining_days') ?? 0;

        if ($slug == 'notice') {
            $data['notices'] = $this->profile->getNoticeList($request);
        } elseif ($slug == 'appointment') {
            $data['appointments'] = $this->profile->getAppointmentList($request);
        } elseif ($slug == 'time-log') {
            $attendances = $this->profile->attendance($user->id, 7);
            $data['time_log_reports'] = [
                'time_logs' => $attendances,
                'total_worked_time' => $this->sumTimeColumn($attendances, 'worked_time'),
                'total_break_time' => $this->sumTimeColumn($attendances, 'break_time'),
                'total_overtime' => $this->sumTimeColumn($attendances, 'over_time'),
                'average_late_time' => $this->avgTimeColumn($attendances, 'late_duration'),
                'late_in_days' => $attendances->where('checkin_status', 'late')->count(),
                'left_early_days' => $attendances->where('checkout_status', 'left_early')->count(),
            ];
        } elseif ($slug == 'monthly-report') {
            $daysInCurrentMonth = Carbon::now()->daysInMonth;
            $attendances = $this->profile->attendance($user->id, $daysInCurrentMonth);
            $totalWorkedTime = $this->sumTimeColumn($attendances, 'worked_time');
            $breakTime = $this->sumTimeColumn($attendances, 'break_time');
            $overtime = $this->sumTimeColumn($attendances, 'over_time');
            $totalStayTime = $this->sumTimeColumn($attendances, 'stay_time');

            $request->merge([
                'employee_id' => $user->id,
                'month' => Carbon::now()->month,
                'year' => Carbon::now()->year,
            ]);
            $workingDays = $this->calendarRepository->getDutyCalendar($request);
            $totalSeconds = $this->secondsFromTime($totalWorkedTime) + $this->secondsFromTime($breakTime) + $this->secondsFromTime($overtime);
            $workedPercent = $totalSeconds > 0 ? ($this->secondsFromTime($totalWorkedTime) / $totalSeconds) * 100 : 0;
            $breakPercent = $totalSeconds > 0 ? ($this->secondsFromTime($breakTime) / $totalSeconds) * 100 : 0;
            $overtimePercent = $totalSeconds > 0 ? ($this->secondsFromTime($overtime) / $totalSeconds) * 100 : 0;

            $data['monthly_reports'] = [
                'total_working_days' => count($workingDays),
                'total_worked_time' => $totalWorkedTime,
                'total_break_time' => $breakTime,
                'total_overtime' => $overtime,
                'late_in_days' => $attendances->where('checkin_status', 'late')->count(),
                'worked_percent' => $workedPercent,
                'break_percent' => $breakPercent,
                'overtime_percent' => $overtimePercent,
                'total_stay_time' => $totalStayTime,
            ];
        } elseif ($slug == 'asset') {
            $data['collection'] = $this->assetRepository->getLogsPaginateData($request);
        }

        return view('backend.user.profile.details')->with($data);
    }

    private function sumTimeColumn($collection, $column): string
    {
        $totalSeconds = $collection->sum(function ($row) use ($column) {
            $time = $row[$column] ?? '00:00:00';

            // Ensure we have hours, minutes, seconds
            $parts = explode(':', $time);

            // If only MM:SS is given, prepend 0 hours
            if (count($parts) === 2) {
                array_unshift($parts, 0);
            }

            // Pad to 3 elements if needed
            [$h, $m, $s] = array_pad($parts, 3, 0);

            return ((int) $h * 3600) + ((int) $m * 60) + (int) $s;
        });

        // If total seconds > 24 hours, convert manually to hours
        $hours = floor($totalSeconds / 3600);
        $minutes = floor(($totalSeconds % 3600) / 60);
        $seconds = $totalSeconds % 60;

        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }

    private function avgTimeColumn($collection, $column): string
    {
        $count = $collection->count();
        if ($count === 0) {
            return '00:00:00';
        }

        $totalSeconds = $collection->sum(function ($row) use ($column) {
            $time = $row[$column] ?? '00:00:00';

            // Ensure it has 3 parts
            $parts = explode(':', $time);

            // If only MM:SS is given, prepend hours as 0
            if (count($parts) === 2) {
                array_unshift($parts, 0);
            }

            // If parts are invalid, default to 0
            [$h, $m, $s] = array_pad($parts, 3, 0);

            return ((int) $h * 3600) + ((int) $m * 60) + (int) $s;
        });

        $averageSeconds = $totalSeconds / $count;

        return gmdate('H:i:s', $averageSeconds);
    }

    private function secondsFromTime($time)
    {
        [$h, $m, $s] = array_pad(explode(':', $time ?? '00:00:00'), 3, 0);

        return ((int) $h * 3600) + ((int) $m * 60) + ((int) $s);
    }
}
