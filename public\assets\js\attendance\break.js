"use strict";
var url = $("#url").val();
var _token = $('meta[name="csrf-token"]').attr("content");

function breakBtnHold() {
  let duration = 1600,
    success = (button) => {
      //Success function
      $(".progress").hide();
      button.classList.add("success");
      takeBreak($("#breakStartEndUrl").val());
    };

  document.querySelectorAll(".break-button-hold").forEach((button) => {
    button.style.setProperty("--duration", duration + "ms");
    ["mousedown", "touchstart", "keypress"].forEach((e) => {
      button.addEventListener(e, (ev) => {
        if (
          e != "keypress" ||
          (e == "keypress" &&
            ev.which == 32 &&
            !button.classList.contains("process"))
        ) {
          button.classList.add("process");
          button.timeout = setTimeout(success, duration, button);
        }
      });
    });
    ["mouseup", "mouseout", "touchend", "keyup"].forEach((e) => {
      button.addEventListener(
        e,
        (ev) => {
          if (e != "keyup" || (e == "keyup" && ev.which == 32)) {
            button.classList.remove("process");
            clearTimeout(button.timeout);
          }
        },
        false
      );
    });
  });
}

breakBtnHold();

var takeBreak = (url) => {
  $.ajax({
    type: "POST",
    url: url,
    data: {
      remark: $("#reason").val(),
    },
    success: function (data) {
      if (data?.success) {
        Toast.fire({
          icon: "success",
          title: data.message,
          timer: 1500,
        });
        setTimeout(function () {
          location.reload();
        }, 1500);
      }
    },
    error: function (data) {
      if (data?.responseJSON?.message) {
        Toast.fire({
          icon: "error",
          title: data?.responseJSON?.message ?? "Something went wrong.",
        });
        $(".progress").show();
        $("#break-button-hold").removeClass("success");
      }
    },
  });
};
