<?php

declare(strict_types=1);

namespace Modules\BulkImport\Jobs;

use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use Modules\BulkImport\Entities\ImportHistory;
use Modules\BulkImport\Services\AttendanceImportService;
use Modules\BulkImport\Services\EmployeeImportService;
use Modules\BulkImport\Services\ImportHistoryService;
use Modules\BulkImport\Services\LeaveImportService;
use Throwable;

class BulkImportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The name of the connection the job should be sent to.
     *
     * @var string|null
     */
    public $connection;

    /**
     * The name of the queue the job should be sent to.
     *
     * @var string
     */
    public $queue;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries;

    /**
     * The maximum number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout;

    /**
     * The path to the file.
     */
    protected string $filePath;

    /**
     * The model class to import data to.
     */
    protected string $modelClass;

    /**
     * The field mapping array.
     */
    protected array $mapping;

    /**
     * Whether the file has a header row.
     */
    protected bool $hasHeader;

    /**
     * Optional user to notify upon completion.
     */
    protected ?int $userId;

    /**
     * The import history ID, if any.
     */
    protected ?int $importHistoryId = null;

    /**
     * Create a new job instance.
     *
     * @param  string  $filePath  The path to the file
     * @param  string  $modelClass  The model class to import data to
     * @param  array  $mapping  The field mapping array
     * @param  bool  $hasHeader  Whether the file has a header row
     * @param  int|null  $userId  Optional user ID to notify upon completion
     * @param  int|null  $importHistoryId  Optional existing import history ID
     */
    public function __construct(
        string $filePath,
        string $modelClass,
        array $mapping,
        bool $hasHeader = true,
        ?int $userId = null,
        ?int $importHistoryId = null
    ) {
        $this->filePath = $filePath;
        $this->modelClass = $modelClass;
        $this->mapping = $mapping;
        $this->hasHeader = $hasHeader;
        $this->userId = $userId;
        $this->importHistoryId = $importHistoryId;

        // Set queue properties from config and fallback to Laravel's queue config
        $this->connection = config('bulkimport.queue.connection', 'sync');
        $this->queue = config('bulkimport.queue.name', 'default');
        $this->tries = config('bulkimport.queue.tries', 3);
        $this->timeout = config('bulkimport.queue.timeout', 900);
    }

    /**
     * Execute the job.
     */
    public function handle(ImportHistoryService $historyService): void
    {
        // Get or create import history record
        $importHistory = $this->getOrCreateImportHistory($historyService);

        // Mark as processing
        $historyService->markAsProcessing($importHistory);

        try {
            // Verify the model class exists
            if (! class_exists($this->modelClass)) {
                throw new Exception("Model class {$this->modelClass} does not exist");
            }

            // Use DB facade directly instead of model instance for batch inserts
            $extension = strtolower(pathinfo($this->filePath, PATHINFO_EXTENSION));
            $filePath = storage_path('app/'.config('bulkimport.disk', 'public').'/'.$this->filePath);

            // Verify file exists
            if (! file_exists($filePath)) {
                throw new Exception("Import file not found at path: {$filePath}");
            }

            // Check if this is an Employee (User) import that needs special handling
            if ($this->modelClass === 'App\Models\User') {
                $stats = match ($extension) {
                    'xls', 'xlsx' => $this->importEmployeeExcel($filePath, $this->mapping),
                    'csv', 'txt' => $this->importEmployeeCsv($filePath, $this->mapping),
                    default => throw new Exception("Unsupported file format: {$extension}"),
                };
            } elseif ($this->modelClass === 'App\Models\Attendance\Attendance') {
                // Handle attendance imports using AttendanceImportService
                $stats = match ($extension) {
                    'xls', 'xlsx' => $this->importAttendanceExcel($filePath, $this->mapping),
                    'csv', 'txt' => $this->importAttendanceCsv($filePath, $this->mapping),
                    default => throw new Exception("Unsupported file format: {$extension}"),
                };
            } elseif ($this->modelClass === 'App\Models\Leave\LeaveRequest') {
                // Handle leave request imports using LeaveImportService
                $stats = match ($extension) {
                    'xls', 'xlsx' => $this->importLeaveRequestExcel($filePath, $this->mapping),
                    'csv', 'txt' => $this->importLeaveRequestCsv($filePath, $this->mapping),
                    default => throw new Exception("Unsupported file format: {$extension}"),
                };
            } else {
                $stats = match ($extension) {
                    'xls', 'xlsx' => $this->importExcel($filePath, $this->modelClass, $this->mapping),
                    'csv', 'txt' => $this->importCsv($filePath, $this->modelClass, $this->mapping),
                    default => throw new Exception("Unsupported file format: {$extension}"),
                };
            }

            // Mark as completed
            $historyService->markAsCompleted($importHistory, $stats);
        } catch (Throwable $e) {
            Log::error('Bulk import failed: '.$e->getMessage(), [
                'import_id' => $importHistory->id,
                'file' => $this->filePath,
                'model' => $this->modelClass,
                'exception' => $e,
            ]);

            // Mark as failed
            $historyService->markAsFailed($importHistory, $e);

            throw $e;
        }
    }

    /**
     * Get or create an import history record.
     */
    protected function getOrCreateImportHistory(ImportHistoryService $historyService): ImportHistory
    {
        if ($this->importHistoryId) {
            return ImportHistory::findOrFail($this->importHistoryId);
        }

        return $historyService->create(
            $this->filePath,
            $this->modelClass,
            $this->userId
        );
    }

    /**
     * Import data from a CSV file.
     *
     * @param  string  $path  File path
     * @param  string  $modelClass  The model class
     * @param  array  $mapping  Field mapping
     * @return array Import statistics
     */
    protected function importCsv(string $path, string $modelClass, array $mapping): array
    {
        $historyService = app(ImportHistoryService::class);
        $importHistory = ImportHistory::findOrFail($this->importHistoryId);

        $rows = array_map('str_getcsv', file($path));
        $totalRows = count($rows);
        $importedRows = 0;
        $skippedRows = 0;
        $errorRows = 0;
        $lastUpdateTime = now();
        $updateFrequency = 2;
        $chunkSize = 300;

        // Get the table name from the model
        $model = new $modelClass;
        $table = $model->getTable();

        // Update total rows in the database
        $importHistory->update(['total_rows' => $totalRows]);

        if ($this->hasHeader) {
            array_shift($rows);
            $totalRows--;
        }

        // Process rows in chunks
        $dataToInsert = [];

        foreach ($rows as $rowIndex => $row) {
            try {
                $data = [];
                foreach ($mapping as $index => $field) {
                    if ($field !== null && $field !== '' && isset($row[$index])) {
                        $data[$field] = $row[$index];
                    }
                }

                if (! empty($data)) {
                    $dataToInsert[] = $data;
                } else {
                    $skippedRows++;
                }
            } catch (Exception $e) {
                Log::error("Error importing row {$rowIndex}: ".$e->getMessage(), [
                    'row' => $row,
                    'mapping' => $mapping,
                ]);
                $errorRows++;
            }

            // When we reach chunk size or the last row, insert the batch
            if (count($dataToInsert) >= $chunkSize || $rowIndex === count($rows) - 1) {
                if (! empty($dataToInsert)) {
                    try {
                        DB::beginTransaction();

                        // Use DB facade directly with table name
                        $inserted = DB::table($table)->insertOrIgnore($dataToInsert);

                        DB::commit();

                        $importedRows += count($dataToInsert);
                        $dataToInsert = []; // Reset for next chunk
                    } catch (Exception $e) {
                        DB::rollBack();
                        Log::error('Error batch inserting rows: '.$e->getMessage(), [
                            'batch_size' => count($dataToInsert),
                            'exception' => $e,
                            'sample_data' => array_slice($dataToInsert, 0, 2),
                        ]);

                        // Try inserting one by one to salvage as many rows as possible
                        foreach ($dataToInsert as $singleRow) {
                            try {
                                if (DB::table($table)->insertOrIgnore([$singleRow])) {
                                    $importedRows++;
                                } else {
                                    $skippedRows++;
                                }
                            } catch (Exception $innerException) {
                                Log::error('Error inserting single row: '.$innerException->getMessage());
                                $errorRows++;
                            }
                        }

                        $dataToInsert = []; // Reset for next chunk
                    }
                }
            }

            // Broadcast progress periodically to avoid flooding
            $now = now();
            if ($now->diffInSeconds($lastUpdateTime) >= $updateFrequency) {
                $historyService->updateProgress($importHistory, $importedRows, $skippedRows, $errorRows);
                $lastUpdateTime = $now;
            }
        }

        // Final progress update
        $historyService->updateProgress($importHistory, $importedRows, $skippedRows, $errorRows);

        return [
            'total' => $totalRows,
            'imported' => $importedRows,
            'skipped' => $skippedRows,
            'errors' => $errorRows,
        ];
    }

    /**
     * Import data from an Excel file.
     *
     * @param  string  $path  File path
     * @param  string  $modelClass  The model class
     * @param  array  $mapping  Field mapping
     * @return array Import statistics
     */
    protected function importExcel(string $path, string $modelClass, array $mapping): array
    {
        $historyService = app(ImportHistoryService::class);
        $importHistory = ImportHistory::findOrFail($this->importHistoryId);

        $collection = Excel::toCollection(null, $path)->first();
        $totalRows = $collection->count();
        $importedRows = 0;
        $skippedRows = 0;
        $errorRows = 0;
        $lastUpdateTime = now();
        $updateFrequency = 2; // seconds
        $chunkSize = 300; // Process 300 rows at a time

        // Get the table name from the model
        $model = new $modelClass;
        $table = $model->getTable();

        // Update total rows in the database
        $importHistory->update(['total_rows' => $totalRows]);

        if ($this->hasHeader) {
            $collection = $collection->slice(1);
            $totalRows--;
        }

        // Process rows in chunks
        $dataToInsert = [];

        foreach ($collection as $rowIndex => $row) {
            try {
                $data = [];
                foreach ($mapping as $index => $field) {
                    if ($field !== null && $field !== '' && isset($row[$index])) {
                        $data[$field] = $row[$index];
                    }
                }

                if (! empty($data)) {
                    $dataToInsert[] = $data;
                } else {
                    $skippedRows++;
                }
            } catch (Exception $e) {
                Log::error("Error importing row {$rowIndex}: ".$e->getMessage(), [
                    'row' => $row,
                    'mapping' => $mapping,
                ]);
                $errorRows++;
            }

            // When we reach chunk size or the last row, insert the batch
            if (count($dataToInsert) >= $chunkSize || $rowIndex === $collection->count() - 1) {
                if (! empty($dataToInsert)) {
                    try {
                        DB::beginTransaction();

                        // Use DB facade directly with table name
                        $inserted = DB::table($table)->insertOrIgnore($dataToInsert);

                        DB::commit();

                        $importedRows += count($dataToInsert);
                        $dataToInsert = []; // Reset for next chunk
                    } catch (Exception $e) {
                        DB::rollBack();
                        Log::error('Error batch inserting rows: '.$e->getMessage(), [
                            'batch_size' => count($dataToInsert),
                            'exception' => $e,
                            'sample_data' => array_slice($dataToInsert, 0, 2),
                        ]);

                        // Try inserting one by one to salvage as many rows as possible
                        foreach ($dataToInsert as $singleRow) {
                            try {
                                if (DB::table($table)->insertOrIgnore([$singleRow])) {
                                    $importedRows++;
                                } else {
                                    $skippedRows++;
                                }
                            } catch (Exception $innerException) {
                                Log::error('Error inserting single row: '.$innerException->getMessage());
                                $errorRows++;
                            }
                        }

                        $dataToInsert = []; // Reset for next chunk
                    }
                }
            }

            // Broadcast progress periodically to avoid flooding
            $now = now();
            if ($now->diffInSeconds($lastUpdateTime) >= $updateFrequency) {
                $historyService->updateProgress($importHistory, $importedRows, $skippedRows, $errorRows);
                $lastUpdateTime = $now;
            }
        }

        // Final progress update
        $historyService->updateProgress($importHistory, $importedRows, $skippedRows, $errorRows);

        return [
            'total' => $totalRows,
            'imported' => $importedRows,
            'skipped' => $skippedRows,
            'errors' => $errorRows,
        ];
    }

    /**
     * Import employee data from CSV with multiple table dependencies
     *
     * @param  string  $path  File path
     * @param  array  $mapping  Field mapping
     * @return array Import statistics
     */
    protected function importEmployeeCsv(string $path, array $mapping): array
    {
        $historyService = app(ImportHistoryService::class);
        $employeeService = app(EmployeeImportService::class);
        $importHistory = ImportHistory::findOrFail($this->importHistoryId);

        $rows = array_map('str_getcsv', file($path));
        $totalRows = count($rows);

        // Update total rows in the database
        $importHistory->update(['total_rows' => $totalRows]);

        // Use the EmployeeImportService for processing
        $stats = $employeeService->importEmployees($rows, $mapping, $this->hasHeader);

        // Update progress
        $historyService->updateProgress($importHistory, $stats['imported'], $stats['skipped'], $stats['errors']);

        return $stats;
    }

    /**
     * Import employee data from Excel with multiple table dependencies
     *
     * @param  string  $path  File path
     * @param  array  $mapping  Field mapping
     * @return array Import statistics
     */
    protected function importEmployeeExcel(string $path, array $mapping): array
    {
        $historyService = app(ImportHistoryService::class);
        $employeeService = app(EmployeeImportService::class);
        $importHistory = ImportHistory::findOrFail($this->importHistoryId);

        $collection = Excel::toCollection(null, $path)->first();
        $totalRows = $collection->count();

        // Update total rows in the database
        $importHistory->update(['total_rows' => $totalRows]);

        // Convert collection to array for processing
        $rows = $collection->toArray();

        // Use the EmployeeImportService for processing
        $stats = $employeeService->importEmployees($rows, $mapping, $this->hasHeader);

        // Update progress
        $historyService->updateProgress($importHistory, $stats['imported'], $stats['skipped'], $stats['errors']);

        return $stats;
    }

    /**
     * Import attendance data from CSV
     *
     * @param  string  $path  File path
     * @param  array  $mapping  Field mapping
     * @return array Import statistics
     */
    protected function importAttendanceCsv(string $path, array $mapping): array
    {
        $historyService = app(ImportHistoryService::class);
        $attendanceService = app(AttendanceImportService::class);
        $importHistory = ImportHistory::findOrFail($this->importHistoryId);

        $rows = array_map('str_getcsv', file($path));
        $totalRows = count($rows);

        // Update total rows in the database
        $importHistory->update(['total_rows' => $totalRows]);

        // Remove header row if present
        if ($this->hasHeader) {
            array_shift($rows);
            $totalRows--;
        }

        // Use the AttendanceImportService for processing
        $processedData = $attendanceService->processAttendanceData($rows, $mapping);

        if ($processedData['error_count'] > 0) {
            Log::warning('Attendance import has validation errors', [
                'error_count' => $processedData['error_count'],
                'errors' => $processedData['errors'],
            ]);
        }

        // Import the processed data
        $importResult = $attendanceService->importAttendanceRecords($processedData['processed_data']);

        if ($importResult['success']) {
            $stats = [
                'total' => $totalRows,
                'imported' => $importResult['imported_count'],
                'skipped' => $processedData['error_count'],
                'errors' => count($importResult['errors']),
            ];
        } else {
            $stats = [
                'total' => $totalRows,
                'imported' => 0,
                'skipped' => 0,
                'errors' => $totalRows,
            ];
        }

        // Update progress
        $historyService->updateProgress($importHistory, $stats['imported'], $stats['skipped'], $stats['errors']);

        return $stats;
    }

    /**
     * Import attendance data from Excel
     *
     * @param  string  $path  File path
     * @param  array  $mapping  Field mapping
     * @return array Import statistics
     */
    protected function importAttendanceExcel(string $path, array $mapping): array
    {
        $historyService = app(ImportHistoryService::class);
        $attendanceService = app(AttendanceImportService::class);
        $importHistory = ImportHistory::findOrFail($this->importHistoryId);

        $collection = Excel::toCollection(null, $path)->first();
        $totalRows = $collection->count();

        // Update total rows in the database
        $importHistory->update(['total_rows' => $totalRows]);

        // Convert collection to array for processing
        $rows = $collection->toArray();

        // Remove header row if present
        if ($this->hasHeader) {
            array_shift($rows);
            $totalRows--;
        }

        // Use the AttendanceImportService for processing
        $processedData = $attendanceService->processAttendanceData($rows, $mapping);

        if ($processedData['error_count'] > 0) {
            Log::warning('Attendance import has validation errors', [
                'error_count' => $processedData['error_count'],
                'errors' => $processedData['errors'],
            ]);
        }

        // Import the processed data
        $importResult = $attendanceService->importAttendanceRecords($processedData['processed_data']);

        if ($importResult['success']) {
            $stats = [
                'total' => $totalRows,
                'imported' => $importResult['imported_count'],
                'skipped' => $processedData['error_count'],
                'errors' => count($importResult['errors']),
            ];
        } else {
            $stats = [
                'total' => $totalRows,
                'imported' => 0,
                'skipped' => 0,
                'errors' => $totalRows,
            ];
        }

        // Update progress
        $historyService->updateProgress($importHistory, $stats['imported'], $stats['skipped'], $stats['errors']);

        return $stats;
    }

    /**
     * Import leave request data from CSV
     *
     * @param  string  $path  File path
     * @param  array  $mapping  Field mapping
     * @return array Import statistics
     */
    protected function importLeaveRequestCsv(string $path, array $mapping): array
    {
        $historyService = app(ImportHistoryService::class);
        $leaveImportService = app(LeaveImportService::class);
        $importHistory = ImportHistory::findOrFail($this->importHistoryId);

        $rows = array_map('str_getcsv', file($path));
        $totalRows = count($rows);

        // Update total rows in the database
        $importHistory->update(['total_rows' => $totalRows]);

        // Use the LeaveImportService for processing
        $stats = $leaveImportService->importLeaveRequests($rows, $mapping, $this->hasHeader);

        // Update progress
        $historyService->updateProgress($importHistory, $stats['imported'], $stats['skipped'], $stats['errors']);

        return $stats;
    }

    /**
     * Import leave request data from Excel
     *
     * @param  string  $path  File path
     * @param  array  $mapping  Field mapping
     * @return array Import statistics
     */
    protected function importLeaveRequestExcel(string $path, array $mapping): array
    {
        $historyService = app(ImportHistoryService::class);
        $leaveImportService = app(LeaveImportService::class);
        $importHistory = ImportHistory::findOrFail($this->importHistoryId);

        $collection = Excel::toCollection(null, $path)->first();
        $totalRows = $collection->count();

        // Update total rows in the database
        $importHistory->update(['total_rows' => $totalRows]);

        // Convert collection to array for processing
        $rows = $collection->toArray();

        // Use the LeaveImportService for processing
        $stats = $leaveImportService->importLeaveRequests($rows, $mapping, $this->hasHeader);

        // Update progress
        $historyService->updateProgress($importHistory, $stats['imported'], $stats['skipped'], $stats['errors']);

        return $stats;
    }
}
