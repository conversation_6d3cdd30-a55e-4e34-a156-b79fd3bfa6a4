<?php

namespace Modules\AssetManagement\Database\Seeders;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Seeder;
use Modules\AssetManagement\Entities\AssetCategory;

class AssetCategoryTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Model::unguard();
        AssetCategory::truncate();

        $categories = [];
        $totalCategories = rand(60, 70); // generate 60–70 records

        for ($i = 1; $i <= $totalCategories; $i++) {
            $categories[] = [
                'title' => 'Category '.$i,
                'status' => 'active',
                'attachments' => json_encode([
                    'disk' => 'public',
                    'files' => ['modules/asset_management/category/default.png'],
                ]),
                'company_id' => 1,
                'branch_id' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        AssetCategory::insert($categories);

        $this->command->info('✅ AssetCategory records seeded: '.count($categories));
    }
}
