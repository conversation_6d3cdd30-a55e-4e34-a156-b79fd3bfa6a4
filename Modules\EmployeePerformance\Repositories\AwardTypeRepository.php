<?php

namespace Modules\EmployeePerformance\Repositories;

use App\Services\FileUploadService;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Modules\EmployeePerformance\Entities\AwardType;

class AwardTypeRepository
{
    public function __construct(protected AwardType $model) {}

    public function getPaginateData($request, $fields = ['*'])
    {
        return $this->model::select($fields)->latest('id')
            ->when($request->search, function ($query) use ($request) {
                $query->where(function ($q) use ($request) {
                    $q->where('name', 'like', '%'.$request->search.'%')
                        ->orWhere('message', 'like', '%'.$request->search.'%');
                });
            })
            ->when($request->status, function ($query) use ($request) {
                $query->where('status', $request->status);
            })
            ->paginate($request->limit ?? 10);
    }

    public function store(array $data)
    {
        DB::beginTransaction();
        try {
            $data['created_by'] = Auth::id();
            $data['company_id'] = Auth::user()->company_id;
            $data['branch_id'] = Auth::user()->branch_id;

            // Handle badge file upload
            if (isset($data['badge_file']) && $data['badge_file']->isValid()) {
                $path = FileUploadService::file($data['badge_file'], 'award_types');
                $data['badge'] = [
                    'disk' => config('filesystems.default'),
                    'files' => [$path],
                ];
            }
            unset($data['badge_file']);

            $created = $this->model->create($data);

            if ($created) {
                DB::commit();

                return $created;
            }
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function show($id)
    {
        return $this->model->findOrFail($id);
    }

    public function update($id, array $data)
    {
        DB::beginTransaction();
        try {
            $awardType = $this->model->findOrFail($id);
            $data['updated_by'] = Auth::id();

            // Handle badge file upload
            if (isset($data['badge_file']) && $data['badge_file']->isValid()) {
                // Delete old badge file if exists
                if ($awardType->badge && isset($awardType->badge['files'])) {
                    FileUploadService::delete($awardType->badge['files']);
                }

                $path = FileUploadService::file($data['badge_file'], 'award_types');
                $data['badge'] = [
                    'disk' => config('filesystems.default'),
                    'files' => [$path],
                ];
            }
            unset($data['badge_file']);

            $updated = $awardType->update($data);

            if ($updated) {
                DB::commit();

                return $awardType->fresh();
            }
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function delete($id)
    {
        DB::beginTransaction();
        try {
            $awardType = $this->model->findOrFail($id);

            // Check if award type has related awards
            if ($awardType->awards()->count() > 0) {
                throw new Exception(_trans('message.Cannot delete award type with existing awards'));
            }

            // Delete badge file if exists
            if ($awardType->badge && isset($awardType->badge['files'])) {
                FileUploadService::delete($awardType->badge['files']);
            }

            $deleted = $awardType->delete();

            if ($deleted) {
                DB::commit();

                return true;
            }
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function getActiveAwardTypes()
    {
        return $this->model->where('status', 'active')->get();
    }
}
