/*------------------------------------------------------------------------------------------------------------------
    Index Of CSS File
--------------------------------------------------------------------------------------------------------------------

    @Version            : 1.0.0
    @Application Name   : Onest HRM
    @Application Author : www.onesttech.com


    1. Common Default Style         23. Badges                            45. Shine                          67. Animate CSS                      
    2. Button Style                 24. Pagination                        46. Login Page CSS                 68. Switch                           
    3. Dropdown Style               25. Profile                           47. New CSS - 2025                 69. Line Clamp CSS                   
    4. Search Input Style           26. Custom Input Field                48. Profile                        70. <PERSON><PERSON> CSS                      
    5. Header CSS                   27. Input Check Radio                 49. Profile Complete UI            71. Action But<PERSON> Variants CSS       
    6. Topbar Dropdown Menu CSS     28. Email Template                    50. Tab Style 02                   72. Ghost Variant CSS                
    7. Profile Expand CSS           29. Select 2                          51. User Dashboard CSS             73. Badge Variant                    
    8. Header Marquee Bar CSS       30. Task View                         52. Error Page CSS (duplicate)     74. Credentials CSS                  
    9. Sidebar CSS                  31. Rating Widget                     53. New Table CSS                  75. Chat Box SCSS                    
    10. Theme Switch CSS            32. Select 2                          54. Status Badge CSS               76. AI Chat Boot                     
    11. Card                        33. Custom Image Uploader Widget      55. Report Page                    77. Working Schedule List CSS        
    12. Chart CSS                   34. Icon                              56. New CSS                        78. (Empty Entry)                    
    13. Tables CSS                  35. Check In                          57. Single Days Time Log           79. Assets Details CSS               
    14. Charts                      36. Button Hold                       58. Months Time Log                80. Complaint Form CSS               
    15. Progress                    37. Checkout Modal                    59. Permission Tab                 81. Phone Book CSS                   
    16. Accordion                   38. Sticky Note                       60. Progress CSS                   82. Complaint Form CSS  
    17. Signup, Signin              39. Select 2 Style - SCSS Version     61. Department by Present CSS      83. Complaint Form CSS   
    18. Card (duplicate)            40. Auth Page CSS                     62. Employee Time Line             84. Color picker                     
    19. Modal                       41. Error Page CSS                    63. Avatar Group Component         85. Custom Date Picker               
    20. Alert                       42. Award Modal CSS                   64. New CSS                        86. Mobile Menu Footer - SCSS Format 
    21. Color Template              43. File Uploader                     65. swal2 CSS                      87. Custom Dropdown                  
    22. Dropdown                    44. Award Modal CSS (duplicate)       66. 500 Error Page                                                      

--------------------------------------------------------------------------------------------------------------------
    End-of CSS File
--------------------------------------------------------------------------------------------------------------------*/

@import url("https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap");

:root {
  /* text color */
  --ot-primary-text: #161f2b;
  --ot-secondary-text: #3d454f;
  --ot-tertiary-text: #ff991a;
  --ot-subtitle-text: #545c66;

  --heading-fonts: "Open Sans", sans-serif;
  --primary-color: #2978e5;
  --secondary-color: #f4f1eb;
  --ot-primary-btn: #161f2b;
  --black-gradient: linear-gradient(to right, #000000, #444444);
  --white-color: #fff;
  --primary-bg-opacity: 1;
  --primary-bg: rgb(249 250 251 / var(--primary-bg-opacity, 1));
  --theme-bg: #fff6ed;

  /* border color */
  --ot-primary-border: #d6e2ef;
  --ot-secondary-border: #bdc7d2;

  /* background color */
  --ot-bg-primary: #f0f3f5;
  --ot-bg-secondary: #ffffff;
  --ot-bg-secondary-opacity: rgba(255, 255, 255, 0.35);
  --ot-bg-tertiary: #ffffff;
  --ot-bg-progress: #e6e6e6;

  /* badge background color */
  --ot-bg-badge-success: #29d697;
  --ot-bg-badge-danger: #ff6a54;
  --ot-bg-badge-warning: #fdc400;
  --ot-bg-badge-primary: #1890ff;

  /* badge text color */
  --ot-text-badge-success: #ffffff;
  --ot-text-badge-danger: #ffffff;
  --ot-text-badge-warning: #ffffff;
  --ot-text-badge-primary: #ffffff;

  /* badge light background color */
  --ot-bg-badge-light-success: #e9faf4;
  --ot-bg-badge-light-danger: #fff0ed;
  --ot-bg-badge-light-warning: #fef9e5;
  --ot-bg-badge-light-primary: #e6f2fd;

  /* badge light text color */
  --ot-text-badge-light-success: #29d697;
  --ot-text-badge-light-danger: #ff6a54;
  --ot-text-badge-light-warning: #fdc400;
  --ot-text-badge-light-primary: #1890ff;

  /* badge deep text color */
  --ot-text-badge-deep-success: #29d697;
  --ot-text-badge-deep-danger: #ff6a54;
  --ot-text-badge-deep-warning: #fdc400;
  --ot-text-badge-deep-primary: #1890ff;

  /* RGB COLOR  */
  --primary-color-rgb: 41, 120, 229;
  --secondary-color-rgb: 0, 0, 0;
  --danger-color-rgb: 255, 51, 51;
  --success-color-rgb: 16, 185, 129;
  --warning-color-rgb: 255, 153, 26;
  --info-color-rgb: 23, 162, 184;
  --light-color-rgb: 244, 241, 235;
  --tertiary-color-rgb: 84, 92, 102;

  --ghost-active: rgba(0, 0, 0, 0.1);
  --ghost-hover: rgba(0, 0, 0, 0.05);
}

/*------------------------------------------------------------------
  1. Common Default Style
--------------------------------------------------------------------*/

* {
  scrollbar-color: #d0d0d0 #fff;
  scrollbar-width: thin;
}

* {
  box-sizing: border-box;
  outline: none;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
}

body {
  font-family: var(--heading-fonts);
  color: var(--ot-primary-text);
  font-size: 14px;
  background: var(--primary-bg);
}

html,
body {
  height: 100%;
}

a {
  text-decoration: none;
}

ol,
ul {
  padding-left: 0;
}

.bg-primary {
  background: var(--primary-bg) !important;
}

.bg-secondary {
  background: var(--ot-tertiary-text) !important;
}

.bg-primary-soft {
  background: var(--ot-bg-primary) !important;
}

.bg-primary-light {
  background: #f5f5f76e;
}

.what-happens-bg {
  background-color: rgba(26, 115, 233, 0.1);
}

.processing-bg {
  background-color: rgba(22, 178, 116, 0.1);
}

.quick-tips-bg {
  background-color: rgba(255, 153, 26, 0.1);
}

.bg-black {
  background: var(--ot-primary-btn) !important;
}

.bg-black-gradient {
  background: var(--black-gradient) !important;
}

.p-24 {
  padding: 24px !important;
}

.p-20 {
  padding: 20px !important;
}

.p-16 {
  padding: 16px !important;
}

.space-y-12 {
  li:not(:last-child) {
    margin-bottom: 12px;
  }
}

.p-30 {
  padding: 30px !important;
}

.p-18 {
  padding: 18px !important;
}

.p-10 {
  padding: 10px !important;
}

.p-12 {
  padding: 12px !important;
}

.p-14 {
  padding: 14px !important;
}

.p-16 {
  padding: 16px !important;
}

.p-20 {
  padding: 20px !important;
}

.pa-0 {
  padding: 0 !important;
}

.pl-0 {
  padding-left: 0 !important;
}

.pt-0 {
  padding-top: 0 !important;
}

.pr-0 {
  padding-right: 0 !important;
}

.pb-0 {
  padding-bottom: 0 !important;
}

.pv-0 {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.ph-0 {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.pa-2 {
  padding: 2px !important;
}

.pl-2 {
  padding-left: 2px !important;
}

.pt-2 {
  padding-top: 2px !important;
}

.pr-2 {
  padding-right: 2px !important;
}

.pb-2 {
  padding-bottom: 2px !important;
}

.pv-2 {
  padding-top: 2px !important;
  padding-bottom: 2px !important;
}

.ph-2 {
  padding-left: 2px !important;
  padding-right: 2px !important;
}

.pa-4 {
  padding: 4px !important;
}

.pl-4 {
  padding-left: 4px !important;
}

.pt-4 {
  padding-top: 4px !important;
}

.opacity-9 {
  opacity: 0.9;
}

.opacity-8 {
  opacity: 0.8;
}

.pr-4 {
  padding-right: 4px !important;
}

.pb-4 {
  padding-bottom: 4px !important;
}

.pv-4 {
  padding-top: 4px !important;
  padding-bottom: 4px !important;
}

.ph-4 {
  padding-left: 4px !important;
  padding-right: 4px !important;
}

.pa-6 {
  padding: 6px !important;
}

.pl-6 {
  padding-left: 6px !important;
}

.pt-6 {
  padding-top: 6px !important;
}

.pr-6 {
  padding-right: 6px !important;
}

.pb-6 {
  padding-bottom: 6px !important;
}

.pv-6 {
  padding-top: 6px !important;
  padding-bottom: 6px !important;
}

.ph-6 {
  padding-left: 6px !important;
  padding-right: 6px !important;
}

.pa-8 {
  padding: 8px !important;
}

.pl-8 {
  padding-left: 8px !important;
}

.pt-8 {
  padding-top: 8px !important;
}

.pr-8 {
  padding-right: 8px !important;
}

.pb-8 {
  padding-bottom: 8px !important;
}

.pv-8 {
  padding-top: 8px !important;
  padding-bottom: 8px !important;
}

.ph-8 {
  padding-left: 8px !important;
  padding-right: 8px !important;
}

.pa-10 {
  padding: 10px !important;
}

.pl-10 {
  padding-left: 10px !important;
}

.pt-10 {
  padding-top: 10px !important;
}

.pr-10 {
  padding-right: 10px !important;
}

.pb-10 {
  padding-bottom: 10px !important;
}

.pv-10 {
  padding-top: 10px !important;
  padding-bottom: 10px !important;
}

.ph-10 {
  padding-left: 10px !important;
  padding-right: 10px !important;
}

.pa-12 {
  padding: 12px !important;
}

.pl-12 {
  padding-left: 12px !important;
}

.pt-12 {
  padding-top: 12px !important;
}

.pr-12 {
  padding-right: 12px !important;
}

.pb-12 {
  padding-bottom: 12px !important;
}

.pv-12 {
  padding-top: 12px !important;
  padding-bottom: 12px !important;
}

.ph-12 {
  padding-left: 12px !important;
  padding-right: 12px !important;
}

.pa-14 {
  padding: 14px !important;
}

.pl-14 {
  padding-left: 14px !important;
}

.pt-14 {
  padding-top: 14px !important;
}

.pr-14 {
  padding-right: 14px !important;
}

.pb-14 {
  padding-bottom: 14px !important;
}

.pv-14 {
  padding-top: 14px !important;
  padding-bottom: 14px !important;
}

.ph-14 {
  padding-left: 14px !important;
  padding-right: 14px !important;
}

.pa-16 {
  padding: 16px !important;
}

.pl-16 {
  padding-left: 16px !important;
}

.pt-16 {
  padding-top: 16px !important;
}

.pr-16 {
  padding-right: 16px !important;
}

.pb-16 {
  padding-bottom: 16px !important;
}

.pv-16 {
  padding-top: 16px !important;
  padding-bottom: 16px !important;
}

.ph-16 {
  padding-left: 16px !important;
  padding-right: 16px !important;
}

.pa-18 {
  padding: 18px !important;
}

.pl-18 {
  padding-left: 18px !important;
}

.pt-18 {
  padding-top: 18px !important;
}

.pr-18 {
  padding-right: 18px !important;
}

.pb-18 {
  padding-bottom: 18px !important;
}

.pv-18 {
  padding-top: 18px !important;
  padding-bottom: 18px !important;
}

.ph-18 {
  padding-left: 18px !important;
  padding-right: 18px !important;
}

.pa-20 {
  padding: 20px !important;
}

.pl-20 {
  padding-left: 20px !important;
}

.pt-20 {
  padding-top: 20px !important;
}

.pr-20 {
  padding-right: 20px !important;
}

.pb-20 {
  padding-bottom: 20px !important;
}

.pv-20 {
  padding-top: 20px !important;
  padding-bottom: 20px !important;
}

.ph-20 {
  padding-left: 20px !important;
  padding-right: 20px !important;
}

.radius-4 {
  border-radius: 4px !important;
}

.radius-6 {
  border-radius: 6px !important;
}

.radius-7 {
  border-radius: 7px !important;
}

.radius-8 {
  border-radius: 8px !important;
}

.radius-9 {
  border-radius: 9px !important;
}

.radius-10 {
  border-radius: 10px !important;
}

.radius-12 {
  border-radius: 12px !important;
}

.radius-14 {
  border-radius: 14px !important;
}

.radius-16 {
  border-radius: 16px !important;
}

.radius-18 {
  border-radius: 18px !important;
}

.radius-20 {
  border-radius: 20px !important;
}

.radius-22 {
  border-radius: 22px !important;
}

.radius-24 {
  border-radius: 24px !important;
}

.radius-26 {
  border-radius: 26px !important;
}

.radius-28 {
  border-radius: 28px !important;
}

.pa-22 {
  padding: 22px !important;
}

.pl-22 {
  padding-left: 22px !important;
}

.pt-22 {
  padding-top: 22px !important;
}

.pr-22 {
  padding-right: 22px !important;
}

.pb-22 {
  padding-bottom: 22px !important;
}

.pv-22 {
  padding-top: 22px !important;
  padding-bottom: 22px !important;
}

.ph-22 {
  padding-left: 22px !important;
  padding-right: 22px !important;
}

.pa-24 {
  padding: 24px !important;
}

.pl-24 {
  padding-left: 24px !important;
}

.pt-24 {
  padding-top: 24px !important;
}

.pr-24 {
  padding-right: 24px !important;
}

.pb-24 {
  padding-bottom: 24px !important;
}

.pv-24 {
  padding-top: 24px !important;
  padding-bottom: 24px !important;
}

.ph-24 {
  padding-left: 24px !important;
  padding-right: 24px !important;
}

.pa-26 {
  padding: 26px !important;
}

.pl-26 {
  padding-left: 26px !important;
}

.pt-26 {
  padding-top: 26px !important;
}

.pr-26 {
  padding-right: 26px !important;
}

.pb-26 {
  padding-bottom: 26px !important;
}

.pb-25 {
  padding-bottom: 25px !important;
}

.pv-26 {
  padding-top: 26px !important;
  padding-bottom: 26px !important;
}

.ph-26 {
  padding-left: 26px !important;
  padding-right: 26px !important;
}

.pa-28 {
  padding: 28px !important;
}

.pl-28 {
  padding-left: 28px !important;
}

.pt-28 {
  padding-top: 28px !important;
}

.pr-28 {
  padding-right: 28px !important;
}

.pb-28 {
  padding-bottom: 28px !important;
}

.pv-28 {
  padding-top: 28px !important;
  padding-bottom: 28px !important;
}

.ph-28 {
  padding-left: 28px !important;
  padding-right: 28px !important;
}

.pa-30 {
  padding: 30px !important;
}

.pl-30 {
  padding-left: 30px !important;
}

.pt-30 {
  padding-top: 30px !important;
}

.pr-30 {
  padding-right: 30px !important;
}

.pb-30 {
  padding-bottom: 30px !important;
}

.pv-30 {
  padding-top: 30px !important;
  padding-bottom: 30px !important;
}

.ph-30 {
  padding-left: 30px !important;
  padding-right: 30px !important;
}

.pa-32 {
  padding: 32px !important;
}

.pl-32 {
  padding-left: 32px !important;
}

.pt-32 {
  padding-top: 32px !important;
}

.pr-32 {
  padding-right: 32px !important;
}

.pb-32 {
  padding-bottom: 32px !important;
}

.pv-32 {
  padding-top: 32px !important;
  padding-bottom: 32px !important;
}

.ph-32 {
  padding-left: 32px !important;
  padding-right: 32px !important;
}

.pa-34 {
  padding: 34px !important;
}

.pl-34 {
  padding-left: 34px !important;
}

.pt-34 {
  padding-top: 34px !important;
}

.pr-34 {
  padding-right: 34px !important;
}

.pb-34 {
  padding-bottom: 34px !important;
}

.pv-34 {
  padding-top: 34px !important;
  padding-bottom: 34px !important;
}

.ph-34 {
  padding-left: 34px !important;
  padding-right: 34px !important;
}

.pa-36 {
  padding: 36px !important;
}

.pl-36 {
  padding-left: 36px !important;
}

.pt-36 {
  padding-top: 36px !important;
}

.pr-36 {
  padding-right: 36px !important;
}

.pb-36 {
  padding-bottom: 36px !important;
}

.pv-36 {
  padding-top: 36px !important;
  padding-bottom: 36px !important;
}

.ph-36 {
  padding-left: 36px !important;
  padding-right: 36px !important;
}

.pa-38 {
  padding: 38px !important;
}

.pl-38 {
  padding-left: 38px !important;
}

.pt-38 {
  padding-top: 38px !important;
}

.pr-38 {
  padding-right: 38px !important;
}

.pb-38 {
  padding-bottom: 38px !important;
}

.pv-38 {
  padding-top: 38px !important;
  padding-bottom: 38px !important;
}

.ph-38 {
  padding-left: 38px !important;
  padding-right: 38px !important;
}

.pa-40 {
  padding: 40px !important;
}

.pl-40 {
  padding-left: 40px !important;
}

.pt-40 {
  padding-top: 40px !important;
}

.pr-40 {
  padding-right: 40px !important;
}

.pb-40 {
  padding-bottom: 40px !important;
}

.pv-40 {
  padding-top: 40px !important;
  padding-bottom: 40px !important;
}

.ph-40 {
  padding-left: 40px !important;
  padding-right: 40px !important;
}

.pa-42 {
  padding: 42px !important;
}

.pl-42 {
  padding-left: 42px !important;
}

.pt-42 {
  padding-top: 42px !important;
}

.pr-42 {
  padding-right: 42px !important;
}

.pb-42 {
  padding-bottom: 42px !important;
}

.pv-42 {
  padding-top: 42px !important;
  padding-bottom: 42px !important;
}

.ph-42 {
  padding-left: 42px !important;
  padding-right: 42px !important;
}

.pa-44 {
  padding: 44px !important;
}

.pl-44 {
  padding-left: 44px !important;
}

.pt-44 {
  padding-top: 44px !important;
}

.pr-44 {
  padding-right: 44px !important;
}

.pb-44 {
  padding-bottom: 44px !important;
}

.pv-44 {
  padding-top: 44px !important;
  padding-bottom: 44px !important;
}

.ph-44 {
  padding-left: 44px !important;
  padding-right: 44px !important;
}

.pa-46 {
  padding: 46px !important;
}

.pl-46 {
  padding-left: 46px !important;
}

.pt-46 {
  padding-top: 46px !important;
}

.pr-46 {
  padding-right: 46px !important;
}

.pb-46 {
  padding-bottom: 46px !important;
}

.pv-46 {
  padding-top: 46px !important;
  padding-bottom: 46px !important;
}

.ph-46 {
  padding-left: 46px !important;
  padding-right: 46px !important;
}

.pa-48 {
  padding: 48px !important;
}

.pl-48 {
  padding-left: 48px !important;
}

.pt-48 {
  padding-top: 48px !important;
}

.pr-48 {
  padding-right: 48px !important;
}

.pb-48 {
  padding-bottom: 48px !important;
}

.pv-48 {
  padding-top: 48px !important;
  padding-bottom: 48px !important;
}

.ph-48 {
  padding-left: 48px !important;
  padding-right: 48px !important;
}

.pa-50 {
  padding: 50px !important;
}

.pl-50 {
  padding-left: 50px !important;
}

.pt-50 {
  padding-top: 50px !important;
}

.pr-50 {
  padding-right: 50px !important;
}

.pb-50 {
  padding-bottom: 50px !important;
}

.pv-50 {
  padding-top: 50px !important;
  padding-bottom: 50px !important;
}

.ph-50 {
  padding-left: 50px !important;
  padding-right: 50px !important;
}

.pa-52 {
  padding: 52px !important;
}

.pl-52 {
  padding-left: 52px !important;
}

.pt-52 {
  padding-top: 52px !important;
}

.pr-52 {
  padding-right: 52px !important;
}

.pb-52 {
  padding-bottom: 52px !important;
}

.pv-52 {
  padding-top: 52px !important;
  padding-bottom: 52px !important;
}

.ph-52 {
  padding-left: 52px !important;
  padding-right: 52px !important;
}

.pa-54 {
  padding: 54px !important;
}

.pl-54 {
  padding-left: 54px !important;
}

.pt-54 {
  padding-top: 54px !important;
}

.pr-54 {
  padding-right: 54px !important;
}

.pb-54 {
  padding-bottom: 54px !important;
}

.pv-54 {
  padding-top: 54px !important;
  padding-bottom: 54px !important;
}

.ph-54 {
  padding-left: 54px !important;
  padding-right: 54px !important;
}

.pa-56 {
  padding: 56px !important;
}

.pl-56 {
  padding-left: 56px !important;
}

.pt-56 {
  padding-top: 56px !important;
}

.pr-56 {
  padding-right: 56px !important;
}

.pb-56 {
  padding-bottom: 56px !important;
}

.pv-56 {
  padding-top: 56px !important;
  padding-bottom: 56px !important;
}

.ph-56 {
  padding-left: 56px !important;
  padding-right: 56px !important;
}

.pa-58 {
  padding: 58px !important;
}

.pl-58 {
  padding-left: 58px !important;
}

.pt-58 {
  padding-top: 58px !important;
}

.pr-58 {
  padding-right: 58px !important;
}

.pb-58 {
  padding-bottom: 58px !important;
}

.pv-58 {
  padding-top: 58px !important;
  padding-bottom: 58px !important;
}

.ph-58 {
  padding-left: 58px !important;
  padding-right: 58px !important;
}

.pa-60 {
  padding: 60px !important;
}

.pl-60 {
  padding-left: 60px !important;
}

.pt-60 {
  padding-top: 60px !important;
}

.pr-60 {
  padding-right: 60px !important;
}

.pb-60 {
  padding-bottom: 60px !important;
}

.pv-60 {
  padding-top: 60px !important;
  padding-bottom: 60px !important;
}

.ph-60 {
  padding-left: 60px !important;
  padding-right: 60px !important;
}

.pa-62 {
  padding: 62px !important;
}

.pl-62 {
  padding-left: 62px !important;
}

.pt-62 {
  padding-top: 62px !important;
}

.pr-62 {
  padding-right: 62px !important;
}

.pb-62 {
  padding-bottom: 62px !important;
}

.pv-62 {
  padding-top: 62px !important;
  padding-bottom: 62px !important;
}

.ph-62 {
  padding-left: 62px !important;
  padding-right: 62px !important;
}

.pa-64 {
  padding: 64px !important;
}

.pl-64 {
  padding-left: 64px !important;
}

.pt-64 {
  padding-top: 64px !important;
}

.pr-64 {
  padding-right: 64px !important;
}

.pb-64 {
  padding-bottom: 64px !important;
}

.pv-64 {
  padding-top: 64px !important;
  padding-bottom: 64px !important;
}

.ph-64 {
  padding-left: 64px !important;
  padding-right: 64px !important;
}

.pa-66 {
  padding: 66px !important;
}

.pl-66 {
  padding-left: 66px !important;
}

.pt-66 {
  padding-top: 66px !important;
}

.pr-66 {
  padding-right: 66px !important;
}

.pb-66 {
  padding-bottom: 66px !important;
}

.pv-66 {
  padding-top: 66px !important;
  padding-bottom: 66px !important;
}

.ph-66 {
  padding-left: 66px !important;
  padding-right: 66px !important;
}

.pa-68 {
  padding: 68px !important;
}

.pl-68 {
  padding-left: 68px !important;
}

.pt-68 {
  padding-top: 68px !important;
}

.pr-68 {
  padding-right: 68px !important;
}

.pb-68 {
  padding-bottom: 68px !important;
}

.pv-68 {
  padding-top: 68px !important;
  padding-bottom: 68px !important;
}

.ph-68 {
  padding-left: 68px !important;
  padding-right: 68px !important;
}

.pa-70 {
  padding: 70px !important;
}

.pl-70 {
  padding-left: 70px !important;
}

.pt-70 {
  padding-top: 70px !important;
}

.pr-70 {
  padding-right: 70px !important;
}

.pb-70 {
  padding-bottom: 70px !important;
}

.pv-70 {
  padding-top: 70px !important;
  padding-bottom: 70px !important;
}

.ph-70 {
  padding-left: 70px !important;
  padding-right: 70px !important;
}

.pa-72 {
  padding: 72px !important;
}

.pl-72 {
  padding-left: 72px !important;
}

.pt-72 {
  padding-top: 72px !important;
}

.pr-72 {
  padding-right: 72px !important;
}

.pb-72 {
  padding-bottom: 72px !important;
}

.pv-72 {
  padding-top: 72px !important;
  padding-bottom: 72px !important;
}

.ph-72 {
  padding-left: 72px !important;
  padding-right: 72px !important;
}

.pa-74 {
  padding: 74px !important;
}

.pl-74 {
  padding-left: 74px !important;
}

.pt-74 {
  padding-top: 74px !important;
}

.pr-74 {
  padding-right: 74px !important;
}

.pb-74 {
  padding-bottom: 74px !important;
}

.pv-74 {
  padding-top: 74px !important;
  padding-bottom: 74px !important;
}

.ph-74 {
  padding-left: 74px !important;
  padding-right: 74px !important;
}

.pa-76 {
  padding: 76px !important;
}

.pl-76 {
  padding-left: 76px !important;
}

.pt-76 {
  padding-top: 76px !important;
}

.pr-76 {
  padding-right: 76px !important;
}

.pb-76 {
  padding-bottom: 76px !important;
}

.pv-76 {
  padding-top: 76px !important;
  padding-bottom: 76px !important;
}

.ph-76 {
  padding-left: 76px !important;
  padding-right: 76px !important;
}

.pa-78 {
  padding: 78px !important;
}

.pl-78 {
  padding-left: 78px !important;
}

.pt-78 {
  padding-top: 78px !important;
}

.pr-78 {
  padding-right: 78px !important;
}

.pb-78 {
  padding-bottom: 78px !important;
}

.pv-78 {
  padding-top: 78px !important;
  padding-bottom: 78px !important;
}

.ph-78 {
  padding-left: 78px !important;
  padding-right: 78px !important;
}

.pa-80 {
  padding: 80px !important;
}

.pl-80 {
  padding-left: 80px !important;
}

.pt-80 {
  padding-top: 80px !important;
}

.pr-80 {
  padding-right: 80px !important;
}

.pb-80 {
  padding-bottom: 80px !important;
}

.pv-80 {
  padding-top: 80px !important;
  padding-bottom: 80px !important;
}

.ph-80 {
  padding-left: 80px !important;
  padding-right: 80px !important;
}

.pa-82 {
  padding: 82px !important;
}

.pl-82 {
  padding-left: 82px !important;
}

.pt-82 {
  padding-top: 82px !important;
}

.pr-82 {
  padding-right: 82px !important;
}

.pb-82 {
  padding-bottom: 82px !important;
}

.pv-82 {
  padding-top: 82px !important;
  padding-bottom: 82px !important;
}

.ph-82 {
  padding-left: 82px !important;
  padding-right: 82px !important;
}

.pa-84 {
  padding: 84px !important;
}

.pl-84 {
  padding-left: 84px !important;
}

.pt-84 {
  padding-top: 84px !important;
}

.pr-84 {
  padding-right: 84px !important;
}

.pb-84 {
  padding-bottom: 84px !important;
}

.pv-84 {
  padding-top: 84px !important;
  padding-bottom: 84px !important;
}

.ph-84 {
  padding-left: 84px !important;
  padding-right: 84px !important;
}

.pa-86 {
  padding: 86px !important;
}

.pl-86 {
  padding-left: 86px !important;
}

.pt-86 {
  padding-top: 86px !important;
}

.pr-86 {
  padding-right: 86px !important;
}

.pb-86 {
  padding-bottom: 86px !important;
}

.pv-86 {
  padding-top: 86px !important;
  padding-bottom: 86px !important;
}

.ph-86 {
  padding-left: 86px !important;
  padding-right: 86px !important;
}

.pa-88 {
  padding: 88px !important;
}

.pl-88 {
  padding-left: 88px !important;
}

.pt-88 {
  padding-top: 88px !important;
}

.pr-88 {
  padding-right: 88px !important;
}

.pb-88 {
  padding-bottom: 88px !important;
}

.pv-88 {
  padding-top: 88px !important;
  padding-bottom: 88px !important;
}

.ph-88 {
  padding-left: 88px !important;
  padding-right: 88px !important;
}

.pa-90 {
  padding: 90px !important;
}

.pl-90 {
  padding-left: 90px !important;
}

.pt-90 {
  padding-top: 90px !important;
}

.pr-90 {
  padding-right: 90px !important;
}

.pb-90 {
  padding-bottom: 90px !important;
}

.pv-90 {
  padding-top: 90px !important;
  padding-bottom: 90px !important;
}

.ph-90 {
  padding-left: 90px !important;
  padding-right: 90px !important;
}

.pa-92 {
  padding: 92px !important;
}

.pl-92 {
  padding-left: 92px !important;
}

.pt-92 {
  padding-top: 92px !important;
}

.pr-92 {
  padding-right: 92px !important;
}

.pb-92 {
  padding-bottom: 92px !important;
}

.pv-92 {
  padding-top: 92px !important;
  padding-bottom: 92px !important;
}

.ph-92 {
  padding-left: 92px !important;
  padding-right: 92px !important;
}

.pa-94 {
  padding: 94px !important;
}

.pl-94 {
  padding-left: 94px !important;
}

.pt-94 {
  padding-top: 94px !important;
}

.pr-94 {
  padding-right: 94px !important;
}

.pb-94 {
  padding-bottom: 94px !important;
}

.pv-94 {
  padding-top: 94px !important;
  padding-bottom: 94px !important;
}

.ph-94 {
  padding-left: 94px !important;
  padding-right: 94px !important;
}

.pa-96 {
  padding: 96px !important;
}

.pl-96 {
  padding-left: 96px !important;
}

.pt-96 {
  padding-top: 96px !important;
}

.pr-96 {
  padding-right: 96px !important;
}

.pb-96 {
  padding-bottom: 96px !important;
}

.pv-96 {
  padding-top: 96px !important;
  padding-bottom: 96px !important;
}

.ph-96 {
  padding-left: 96px !important;
  padding-right: 96px !important;
}

.pa-98 {
  padding: 98px !important;
}

.pl-98 {
  padding-left: 98px !important;
}

.pt-98 {
  padding-top: 98px !important;
}

.pr-98 {
  padding-right: 98px !important;
}

.pb-98 {
  padding-bottom: 98px !important;
}

.pv-98 {
  padding-top: 98px !important;
  padding-bottom: 98px !important;
}

.ph-98 {
  padding-left: 98px !important;
  padding-right: 98px !important;
}

.pa-100 {
  padding: 100px !important;
}

.pl-100 {
  padding-left: 100px !important;
}

.pt-100 {
  padding-top: 100px !important;
}

.pr-100 {
  padding-right: 100px !important;
}

.pb-100 {
  padding-bottom: 100px !important;
}

.pv-100 {
  padding-top: 100px !important;
  padding-bottom: 100px !important;
}

.ph-100 {
  padding-left: 100px !important;
  padding-right: 100px !important;
}

.h-80vh {
  height: 80vh !important;
}

.h-90vh {
  height: 90vh !important;
}

.h-88vh {
  height: 88vh !important;
}

.h-100vh {
  height: 100vh !important;
}

.h-80vh-minus-120 {
  height: calc(80vh - 120px) !important;
}

.h-80vh-minus-160 {
  height: calc(80vh - 160px) !important;
}

.h-90vh-minus-160 {
  height: calc(90vh - 160px) !important;
}

.h-90vh-minus-92 {
  height: calc(90vh - 92px) !important;
}

.h-70vh {
  height: 70vh !important;
}

.mh-70vh {
  min-height: 70vh !important;
}

.h-60vh {
  height: 60vh !important;
}

.ma-0 {
  margin: 0 !important;
}

.ml-0 {
  margin-left: 0 !important;
}

.mt-0 {
  margin-top: 0 !important;
}

.mr-0 {
  margin-right: 0 !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mv-0 {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.mh-0 {
  margin-left: 0 !important;
  margin-right: 0 !important;
}

.ma-2 {
  margin: 2px !important;
}

.ml-2 {
  margin-left: 2px !important;
}

.mt-2 {
  margin-top: 2px !important;
}

.mr-2 {
  margin-right: 2px !important;
}

.mb-2 {
  margin-bottom: 2px !important;
}

.mv-2 {
  margin-top: 2px !important;
  margin-bottom: 2px !important;
}

.mh-2 {
  margin-left: 2px !important;
  margin-right: 2px !important;
}

.ma-4 {
  margin: 4px !important;
}

.ml-4 {
  margin-left: 4px !important;
}

.mt-4 {
  margin-top: 4px !important;
}

.mr-4 {
  margin-right: 4px !important;
}

.mb-4 {
  margin-bottom: 4px !important;
}

.mv-4 {
  margin-top: 4px !important;
  margin-bottom: 4px !important;
}

.mh-4 {
  margin-left: 4px !important;
  margin-right: 4px !important;
}

.ma-6 {
  margin: 6px !important;
}

.mr-auto {
  margin-right: auto;
}

.ml-auto {
  margin-left: auto;
}

.ml-6 {
  margin-left: 6px !important;
}

.mt-6 {
  margin-top: 6px !important;
}

.mr-6 {
  margin-right: 6px !important;
}

.mb-5 {
  margin-bottom: 5px !important;
}

.mb-6 {
  margin-bottom: 6px !important;
}

.mv-6 {
  margin-top: 6px !important;
  margin-bottom: 6px !important;
}

.mh-6 {
  margin-left: 6px !important;
  margin-right: 6px !important;
}

.ma-8 {
  margin: 8px !important;
}

.ml-8 {
  margin-left: 8px !important;
}

.mt-8 {
  margin-top: 8px !important;
}

.mr-8 {
  margin-right: 8px !important;
}

.mb-8 {
  margin-bottom: 8px !important;
}

.mv-8 {
  margin-top: 8px !important;
  margin-bottom: 8px !important;
}

.mh-8 {
  margin-left: 8px !important;
  margin-right: 8px !important;
}

.ma-10 {
  margin: 10px !important;
}

.ml-10 {
  margin-left: 10px !important;
}

.mt-10 {
  margin-top: 10px !important;
}

.mr-10 {
  margin-right: 10px !important;
}

.mb-10 {
  margin-bottom: 10px !important;
}

.mv-10 {
  margin-top: 10px !important;
  margin-bottom: 10px !important;
}

.mh-10 {
  margin-left: 10px !important;
  margin-right: 10px !important;
}

.ma-12 {
  margin: 12px !important;
}

.ml-12 {
  margin-left: 12px !important;
}

.mt-12 {
  margin-top: 12px !important;
}

.mr-12 {
  margin-right: 12px !important;
}

.mb-12 {
  margin-bottom: 12px !important;
}

.mv-12 {
  margin-top: 12px !important;
  margin-bottom: 12px !important;
}

.mh-12 {
  margin-left: 12px !important;
  margin-right: 12px !important;
}

.ma-14 {
  margin: 14px !important;
}

.ml-14 {
  margin-left: 14px !important;
}

.mt-14 {
  margin-top: 14px !important;
}

.mr-14 {
  margin-right: 14px !important;
}

.mb-14 {
  margin-bottom: 14px !important;
}

.mv-14 {
  margin-top: 14px !important;
  margin-bottom: 14px !important;
}

.mh-14 {
  margin-left: 14px !important;
  margin-right: 14px !important;
}

.ma-16 {
  margin: 16px !important;
}

.ml-16 {
  margin-left: 16px !important;
}

.mt-16 {
  margin-top: 16px !important;
}

.mr-16 {
  margin-right: 16px !important;
}

.mb-16 {
  margin-bottom: 16px !important;
}

.mv-16 {
  margin-top: 16px !important;
  margin-bottom: 16px !important;
}

.mh-16 {
  margin-left: 16px !important;
  margin-right: 16px !important;
}

.ma-18 {
  margin: 18px !important;
}

.ml-18 {
  margin-left: 18px !important;
}

.mt-18 {
  margin-top: 18px !important;
}

.mr-18 {
  margin-right: 18px !important;
}

.mb-18 {
  margin-bottom: 18px !important;
}

.mv-18 {
  margin-top: 18px !important;
  margin-bottom: 18px !important;
}

.mh-18 {
  margin-left: 18px !important;
  margin-right: 18px !important;
}

.ma-20 {
  margin: 20px !important;
}

.ml-20 {
  margin-left: 20px !important;
}

.mt-20 {
  margin-top: 20px !important;
}

.mr-20 {
  margin-right: 20px !important;
}

.mb-20 {
  margin-bottom: 20px !important;
}

.mv-20 {
  margin-top: 20px !important;
  margin-bottom: 20px !important;
}

.mh-20 {
  margin-left: 20px !important;
  margin-right: 20px !important;
}

.ma-22 {
  margin: 22px !important;
}

.ml-22 {
  margin-left: 22px !important;
}

.mt-22 {
  margin-top: 22px !important;
}

.mr-22 {
  margin-right: 22px !important;
}

.mb-22 {
  margin-bottom: 22px !important;
}

.mv-22 {
  margin-top: 22px !important;
  margin-bottom: 22px !important;
}

.mh-22 {
  margin-left: 22px !important;
  margin-right: 22px !important;
}

.ma-24 {
  margin: 24px !important;
}

.ml-24 {
  margin-left: 24px !important;
}

.mt-24 {
  margin-top: 24px !important;
}

.mr-24 {
  margin-right: 24px !important;
}

.mb-24 {
  margin-bottom: 24px !important;
}

.mv-24 {
  margin-top: 24px !important;
  margin-bottom: 24px !important;
}

.mh-24 {
  margin-left: 24px !important;
  margin-right: 24px !important;
}

.ma-26 {
  margin: 26px !important;
}

.ml-26 {
  margin-left: 26px !important;
}

.mt-26 {
  margin-top: 26px !important;
}

.mr-26 {
  margin-right: 26px !important;
}

.mb-26 {
  margin-bottom: 26px !important;
}

.mv-26 {
  margin-top: 26px !important;
  margin-bottom: 26px !important;
}

.mh-26 {
  margin-left: 26px !important;
  margin-right: 26px !important;
}

.ma-28 {
  margin: 28px !important;
}

.ml-28 {
  margin-left: 28px !important;
}

.mt-28 {
  margin-top: 28px !important;
}

.mr-28 {
  margin-right: 28px !important;
}

.mb-28 {
  margin-bottom: 28px !important;
}

.mv-28 {
  margin-top: 28px !important;
  margin-bottom: 28px !important;
}

.mh-28 {
  margin-left: 28px !important;
  margin-right: 28px !important;
}

.ma-30 {
  margin: 30px !important;
}

.ml-30 {
  margin-left: 30px !important;
}

.mt-30 {
  margin-top: 30px !important;
}

.mr-30 {
  margin-right: 30px !important;
}

.mb-30 {
  margin-bottom: 30px !important;
}

.mv-30 {
  margin-top: 30px !important;
  margin-bottom: 30px !important;
}

.mh-30 {
  margin-left: 30px !important;
  margin-right: 30px !important;
}

.ma-32 {
  margin: 32px !important;
}

.ml-32 {
  margin-left: 32px !important;
}

.mt-32 {
  margin-top: 32px !important;
}

.mr-32 {
  margin-right: 32px !important;
}

.mb-32 {
  margin-bottom: 32px !important;
}

.mv-32 {
  margin-top: 32px !important;
  margin-bottom: 32px !important;
}

.mh-32 {
  margin-left: 32px !important;
  margin-right: 32px !important;
}

.ma-34 {
  margin: 34px !important;
}

.ml-34 {
  margin-left: 34px !important;
}

.mt-34 {
  margin-top: 34px !important;
}

.mr-34 {
  margin-right: 34px !important;
}

.mb-34 {
  margin-bottom: 34px !important;
}

.mv-34 {
  margin-top: 34px !important;
  margin-bottom: 34px !important;
}

.mh-34 {
  margin-left: 34px !important;
  margin-right: 34px !important;
}

.ma-36 {
  margin: 36px !important;
}

.ml-36 {
  margin-left: 36px !important;
}

.mt-36 {
  margin-top: 36px !important;
}

.mr-36 {
  margin-right: 36px !important;
}

.mb-36 {
  margin-bottom: 36px !important;
}

.mv-36 {
  margin-top: 36px !important;
  margin-bottom: 36px !important;
}

.mh-36 {
  margin-left: 36px !important;
  margin-right: 36px !important;
}

.ma-38 {
  margin: 38px !important;
}

.ml-38 {
  margin-left: 38px !important;
}

.mt-38 {
  margin-top: 38px !important;
}

.mr-38 {
  margin-right: 38px !important;
}

.mb-38 {
  margin-bottom: 38px !important;
}

.mv-38 {
  margin-top: 38px !important;
  margin-bottom: 38px !important;
}

.mh-38 {
  margin-left: 38px !important;
  margin-right: 38px !important;
}

.ma-40 {
  margin: 40px !important;
}

.ml-40 {
  margin-left: 40px !important;
}

.mt-40 {
  margin-top: 40px !important;
}

.mr-40 {
  margin-right: 40px !important;
}

.mb-40 {
  margin-bottom: 40px !important;
}

.mv-40 {
  margin-top: 40px !important;
  margin-bottom: 40px !important;
}

.mh-40 {
  margin-left: 40px !important;
  margin-right: 40px !important;
}

.ma-42 {
  margin: 42px !important;
}

.ml-42 {
  margin-left: 42px !important;
}

.mt-42 {
  margin-top: 42px !important;
}

.mr-42 {
  margin-right: 42px !important;
}

.mb-42 {
  margin-bottom: 42px !important;
}

.mv-42 {
  margin-top: 42px !important;
  margin-bottom: 42px !important;
}

.mh-42 {
  margin-left: 42px !important;
  margin-right: 42px !important;
}

.h-42 {
  height: 42px;
}

.ma-44 {
  margin: 44px !important;
}

.ml-44 {
  margin-left: 44px !important;
}

.mt-44 {
  margin-top: 44px !important;
}

.mr-44 {
  margin-right: 44px !important;
}

.mb-44 {
  margin-bottom: 44px !important;
}

.mv-44 {
  margin-top: 44px !important;
  margin-bottom: 44px !important;
}

.mh-44 {
  margin-left: 44px !important;
  margin-right: 44px !important;
}

.ma-46 {
  margin: 46px !important;
}

.ml-46 {
  margin-left: 46px !important;
}

.mt-46 {
  margin-top: 46px !important;
}

.mr-46 {
  margin-right: 46px !important;
}

.mb-46 {
  margin-bottom: 46px !important;
}

.mv-46 {
  margin-top: 46px !important;
  margin-bottom: 46px !important;
}

.mh-46 {
  margin-left: 46px !important;
  margin-right: 46px !important;
}

.ma-48 {
  margin: 48px !important;
}

.ml-48 {
  margin-left: 48px !important;
}

.mt-48 {
  margin-top: 48px !important;
}

.mr-48 {
  margin-right: 48px !important;
}

.mb-48 {
  margin-bottom: 48px !important;
}

.mv-48 {
  margin-top: 48px !important;
  margin-bottom: 48px !important;
}

.mh-48 {
  margin-left: 48px !important;
  margin-right: 48px !important;
}

.ma-50 {
  margin: 50px !important;
}

.ml-50 {
  margin-left: 50px !important;
}

.mt-50 {
  margin-top: 50px !important;
}

.mr-50 {
  margin-right: 50px !important;
}

.mb-50 {
  margin-bottom: 50px !important;
}

.mv-50 {
  margin-top: 50px !important;
  margin-bottom: 50px !important;
}

.mh-50 {
  margin-left: 50px !important;
  margin-right: 50px !important;
}

.ma-52 {
  margin: 52px !important;
}

.ml-52 {
  margin-left: 52px !important;
}

.mt-52 {
  margin-top: 52px !important;
}

.mr-52 {
  margin-right: 52px !important;
}

.mb-52 {
  margin-bottom: 52px !important;
}

.mv-52 {
  margin-top: 52px !important;
  margin-bottom: 52px !important;
}

.mh-52 {
  margin-left: 52px !important;
  margin-right: 52px !important;
}

.ma-54 {
  margin: 54px !important;
}

.ml-54 {
  margin-left: 54px !important;
}

.mt-54 {
  margin-top: 54px !important;
}

.mr-54 {
  margin-right: 54px !important;
}

.mb-54 {
  margin-bottom: 54px !important;
}

.mv-54 {
  margin-top: 54px !important;
  margin-bottom: 54px !important;
}

.mh-54 {
  margin-left: 54px !important;
  margin-right: 54px !important;
}

.ma-56 {
  margin: 56px !important;
}

.ml-56 {
  margin-left: 56px !important;
}

.mt-56 {
  margin-top: 56px !important;
}

.mr-56 {
  margin-right: 56px !important;
}

.mb-56 {
  margin-bottom: 56px !important;
}

.mv-56 {
  margin-top: 56px !important;
  margin-bottom: 56px !important;
}

.mh-56 {
  margin-left: 56px !important;
  margin-right: 56px !important;
}

.ma-58 {
  margin: 58px !important;
}

.ml-58 {
  margin-left: 58px !important;
}

.mt-58 {
  margin-top: 58px !important;
}

.mr-58 {
  margin-right: 58px !important;
}

.mb-58 {
  margin-bottom: 58px !important;
}

.mv-58 {
  margin-top: 58px !important;
  margin-bottom: 58px !important;
}

.mh-58 {
  margin-left: 58px !important;
  margin-right: 58px !important;
}

.ma-60 {
  margin: 60px !important;
}

.ml-60 {
  margin-left: 60px !important;
}

.mt-60 {
  margin-top: 60px !important;
}

.mr-60 {
  margin-right: 60px !important;
}

.mb-60 {
  margin-bottom: 60px !important;
}

.mv-60 {
  margin-top: 60px !important;
  margin-bottom: 60px !important;
}

.mh-60 {
  margin-left: 60px !important;
  margin-right: 60px !important;
}

.ma-62 {
  margin: 62px !important;
}

.ml-62 {
  margin-left: 62px !important;
}

.mt-62 {
  margin-top: 62px !important;
}

.mr-62 {
  margin-right: 62px !important;
}

.mb-62 {
  margin-bottom: 62px !important;
}

.mv-62 {
  margin-top: 62px !important;
  margin-bottom: 62px !important;
}

.mh-62 {
  margin-left: 62px !important;
  margin-right: 62px !important;
}

.ma-64 {
  margin: 64px !important;
}

.ml-64 {
  margin-left: 64px !important;
}

.mt-64 {
  margin-top: 64px !important;
}

.mr-64 {
  margin-right: 64px !important;
}

.mb-64 {
  margin-bottom: 64px !important;
}

.mv-64 {
  margin-top: 64px !important;
  margin-bottom: 64px !important;
}

.mh-64 {
  margin-left: 64px !important;
  margin-right: 64px !important;
}

.ma-66 {
  margin: 66px !important;
}

.ml-66 {
  margin-left: 66px !important;
}

.mt-66 {
  margin-top: 66px !important;
}

.mr-66 {
  margin-right: 66px !important;
}

.mb-66 {
  margin-bottom: 66px !important;
}

.mv-66 {
  margin-top: 66px !important;
  margin-bottom: 66px !important;
}

.mh-66 {
  margin-left: 66px !important;
  margin-right: 66px !important;
}

.ma-68 {
  margin: 68px !important;
}

.ml-68 {
  margin-left: 68px !important;
}

.mt-68 {
  margin-top: 68px !important;
}

.mr-68 {
  margin-right: 68px !important;
}

.mb-68 {
  margin-bottom: 68px !important;
}

.mv-68 {
  margin-top: 68px !important;
  margin-bottom: 68px !important;
}

.mh-68 {
  margin-left: 68px !important;
  margin-right: 68px !important;
}

.ma-70 {
  margin: 70px !important;
}

.ml-70 {
  margin-left: 70px !important;
}

.mt-70 {
  margin-top: 70px !important;
}

.mr-70 {
  margin-right: 70px !important;
}

.mb-70 {
  margin-bottom: 70px !important;
}

.mv-70 {
  margin-top: 70px !important;
  margin-bottom: 70px !important;
}

.mh-70 {
  margin-left: 70px !important;
  margin-right: 70px !important;
}

.ma-72 {
  margin: 72px !important;
}

.ml-72 {
  margin-left: 72px !important;
}

.mt-72 {
  margin-top: 72px !important;
}

.mr-72 {
  margin-right: 72px !important;
}

.mb-72 {
  margin-bottom: 72px !important;
}

.mv-72 {
  margin-top: 72px !important;
  margin-bottom: 72px !important;
}

.mh-72 {
  margin-left: 72px !important;
  margin-right: 72px !important;
}

.ma-74 {
  margin: 74px !important;
}

.ml-74 {
  margin-left: 74px !important;
}

.mt-74 {
  margin-top: 74px !important;
}

.mr-74 {
  margin-right: 74px !important;
}

.mb-74 {
  margin-bottom: 74px !important;
}

.mv-74 {
  margin-top: 74px !important;
  margin-bottom: 74px !important;
}

.mh-74 {
  margin-left: 74px !important;
  margin-right: 74px !important;
}

.ma-76 {
  margin: 76px !important;
}

.ml-76 {
  margin-left: 76px !important;
}

.mt-76 {
  margin-top: 76px !important;
}

.mr-76 {
  margin-right: 76px !important;
}

.mb-76 {
  margin-bottom: 76px !important;
}

.mv-76 {
  margin-top: 76px !important;
  margin-bottom: 76px !important;
}

.mh-76 {
  margin-left: 76px !important;
  margin-right: 76px !important;
}

.ma-78 {
  margin: 78px !important;
}

.ml-78 {
  margin-left: 78px !important;
}

.mt-78 {
  margin-top: 78px !important;
}

.mr-78 {
  margin-right: 78px !important;
}

.mb-78 {
  margin-bottom: 78px !important;
}

.mv-78 {
  margin-top: 78px !important;
  margin-bottom: 78px !important;
}

.mh-78 {
  margin-left: 78px !important;
  margin-right: 78px !important;
}

.ma-80 {
  margin: 80px !important;
}

.ml-80 {
  margin-left: 80px !important;
}

.mt-80 {
  margin-top: 80px !important;
}

.mr-80 {
  margin-right: 80px !important;
}

.mb-80 {
  margin-bottom: 80px !important;
}

.mv-80 {
  margin-top: 80px !important;
  margin-bottom: 80px !important;
}

.mh-80 {
  margin-left: 80px !important;
  margin-right: 80px !important;
}

.ma-82 {
  margin: 82px !important;
}

.ml-82 {
  margin-left: 82px !important;
}

.mt-82 {
  margin-top: 82px !important;
}

.mr-82 {
  margin-right: 82px !important;
}

.mb-82 {
  margin-bottom: 82px !important;
}

.mv-82 {
  margin-top: 82px !important;
  margin-bottom: 82px !important;
}

.mh-82 {
  margin-left: 82px !important;
  margin-right: 82px !important;
}

.ma-84 {
  margin: 84px !important;
}

.ml-84 {
  margin-left: 84px !important;
}

.mt-84 {
  margin-top: 84px !important;
}

.mr-84 {
  margin-right: 84px !important;
}

.mb-84 {
  margin-bottom: 84px !important;
}

.mv-84 {
  margin-top: 84px !important;
  margin-bottom: 84px !important;
}

.mh-84 {
  margin-left: 84px !important;
  margin-right: 84px !important;
}

.ma-86 {
  margin: 86px !important;
}

.ml-86 {
  margin-left: 86px !important;
}

.mt-86 {
  margin-top: 86px !important;
}

.mr-86 {
  margin-right: 86px !important;
}

.mb-86 {
  margin-bottom: 86px !important;
}

.mv-86 {
  margin-top: 86px !important;
  margin-bottom: 86px !important;
}

.mh-86 {
  margin-left: 86px !important;
  margin-right: 86px !important;
}

.ma-88 {
  margin: 88px !important;
}

.ml-88 {
  margin-left: 88px !important;
}

.mt-88 {
  margin-top: 88px !important;
}

.mr-88 {
  margin-right: 88px !important;
}

.mb-88 {
  margin-bottom: 88px !important;
}

.mv-88 {
  margin-top: 88px !important;
  margin-bottom: 88px !important;
}

.mh-88 {
  margin-left: 88px !important;
  margin-right: 88px !important;
}

.ma-90 {
  margin: 90px !important;
}

.ml-90 {
  margin-left: 90px !important;
}

.mt-90 {
  margin-top: 90px !important;
}

.mr-90 {
  margin-right: 90px !important;
}

.mb-90 {
  margin-bottom: 90px !important;
}

.mv-90 {
  margin-top: 90px !important;
  margin-bottom: 90px !important;
}

.mh-90 {
  margin-left: 90px !important;
  margin-right: 90px !important;
}

.ma-92 {
  margin: 92px !important;
}

.ml-92 {
  margin-left: 92px !important;
}

.mt-92 {
  margin-top: 92px !important;
}

.mr-92 {
  margin-right: 92px !important;
}

.mb-92 {
  margin-bottom: 92px !important;
}

.mv-92 {
  margin-top: 92px !important;
  margin-bottom: 92px !important;
}

.mh-92 {
  margin-left: 92px !important;
  margin-right: 92px !important;
}

.ma-94 {
  margin: 94px !important;
}

.ml-94 {
  margin-left: 94px !important;
}

.mt-94 {
  margin-top: 94px !important;
}

.mr-94 {
  margin-right: 94px !important;
}

.mb-94 {
  margin-bottom: 94px !important;
}

.mv-94 {
  margin-top: 94px !important;
  margin-bottom: 94px !important;
}

.mh-94 {
  margin-left: 94px !important;
  margin-right: 94px !important;
}

.ma-96 {
  margin: 96px !important;
}

.ml-96 {
  margin-left: 96px !important;
}

.mt-96 {
  margin-top: 96px !important;
}

.mr-96 {
  margin-right: 96px !important;
}

.mb-96 {
  margin-bottom: 96px !important;
}

.mv-96 {
  margin-top: 96px !important;
  margin-bottom: 96px !important;
}

.mh-96 {
  margin-left: 96px !important;
  margin-right: 96px !important;
}

.ma-98 {
  margin: 98px !important;
}

.ml-98 {
  margin-left: 98px !important;
}

.mt-98 {
  margin-top: 98px !important;
}

.mr-98 {
  margin-right: 98px !important;
}

.mb-98 {
  margin-bottom: 98px !important;
}

.mv-98 {
  margin-top: 98px !important;
  margin-bottom: 98px !important;
}

.mh-98 {
  margin-left: 98px !important;
  margin-right: 98px !important;
}

.ma-100 {
  margin: 100px !important;
}

.ml-100 {
  margin-left: 100px !important;
}

.mt-100 {
  margin-top: 100px !important;
}

.mr-100 {
  margin-right: 100px !important;
}

.top-60 {
  top: 60px;
}

.top-70 {
  top: 70px;
}

.top-76 {
  top: 76px;
}

.top-100 {
  top: 100px;
}

.mb-100 {
  margin-bottom: 100px !important;
}

.mv-100 {
  margin-top: 100px !important;
  margin-bottom: 100px !important;
}

.mh-100 {
  margin-left: 100px !important;
  margin-right: 100px !important;
}

.fs-18 {
  font-size: 18px;
}

.fs-12 {
  font-size: 12px;
}

.fs-14 {
  font-size: 14px;
}

.gap-6 {
  gap: 6px !important;
}

.gap-7 {
  gap: 7px !important;
}

.gap-8 {
  gap: 8px !important;
}

.gap-9 {
  gap: 9px !important;
}

.gap-10 {
  gap: 10px !important;
}

.gap-11 {
  gap: 11px !important;
}

.gap-12 {
  gap: 12px !important;
}

.gap-13 {
  gap: 13px !important;
}

.gap-14 {
  gap: 14px !important;
}

.gap-15 {
  gap: 15px !important;
}

.gap-16 {
  gap: 16px !important;
}

.gap-18 {
  gap: 18px !important;
}

.gap-19 {
  gap: 19px !important;
}

.gap-20 {
  gap: 20px !important;
}

.gap-21 {
  gap: 21px !important;
}

.gap-22 {
  gap: 22px !important;
}

.gap-23 {
  gap: 23px !important;
}

.gap-24 {
  gap: 24px !important;
}

.gap-25 {
  gap: 25px !important;
}

.gap-28 {
  gap: 28px !important;
}

.gap-30 {
  gap: 30px !important;
}

.gap-35 {
  gap: 35px !important;
}

.gap-40 {
  gap: 40px !important;
}

.gap-45 {
  gap: 45px !important;
}

.wh-20px {
  width: 20px;
  height: 20px;
}

.wh-40px {
  width: 40px;
  height: 40px;
}

.wh-50px {
  width: 50px;
  height: 50px;
}

.wh-60px {
  width: 60px;
  height: 60px;
}

.wh-70px {
  width: 70px;
  height: 70px;
}

.wh-90px {
  width: 90px;
  height: 90px;
}

.h-40px {
  height: 40px;
}

.h-50 {
  height: 50px;
}

.h-100 {
  height: 100px;
}

.h-10 {
  height: 10px;
}

.h-14 {
  height: 14px;
}

.h-13 {
  height: 13px;
}

.h-10 {
  height: 10px;
}

.h-200 {
  height: 200px;
}

.h-300 {
  height: 300px;
}

.h-400 {
  height: 400px;
}

.h-500 {
  height: 500px;
}

.h-30 {
  height: 30px;
}

.h-0 {
  height: 0px;
  overflow: hidden;
}

.h-14 {
  height: 14px;
}

.letter-space-1 {
  letter-spacing: 1px;
}

.stack-y-16>*:not(:first-child) {
  margin-top: 16px;
}

.select2-h-30 {
  .select2-container--default .select2-selection--single .select2-selection-rendered {
    line-height: 30px;
    font-weight: 600;
    font-size: 14px;
  }

  .select2-container .select2-selection--single {
    height: 30px;
  }

  .select2-container--default .select2-selection--single .select2-selection-arrow {
    height: 29px;
  }
}

.h-32 {
  height: 32px !important;
}

.text-10 {
  font-size: 10px !important;
}

.text-11 {
  font-size: 11px !important;
}

.text-12 {
  font-size: 12px !important;
}

.g-x-24 {
  --bs-gutter-x: 24px !important;
}

.g-y-10 {
  --bs-gutter-y: 10px;
}

.g-y-15 {
  --bs-gutter-y: 15px;
}

.g-y-24 {
  --bs-gutter-y: 24px;
}

.g-x-12 {
  --bs-gutter-x: 12px;
}

.g-y-12 {
  --bs-gutter-y: 12px;
}

.g-y-20 {
  --bs-gutter-y: 20px;
}

.g-x-20 {
  --bs-gutter-x: 20px;
}

.g-y-16 {
  --bs-gutter-y: 16px;
}

.g-x-16 {
  --bs-gutter-x: 16px;
}

.g-y-8 {
  --bs-gutter-y: 8px;
}

.g-x-8 {
  --bs-gutter-x: 8px;
}

.g-x-20 {
  --bs-gutter-x: 20px;
}

.g-x-30 {
  --bs-gutter-x: 30px;
}

.g-x-40 {
  --bs-gutter-x: 40px;
}

.g-x-45 {
  --bs-gutter-x: 45px;
}

.g-x-50 {
  --bs-gutter-x: 50px;
}

.g-x-60 {
  --bs-gutter-x: 60px;
}

.g-x-100 {
  --bs-gutter-x: 100px;
}

.g-x-80 {
  --bs-gutter-x: 80px;
}

.g-x-35 {
  --bs-gutter-x: 35px;
}

.g-y-8 {
  --bs-gutter-y: 8px;
}

.g-y-20 {
  --bs-gutter-y: 20px;
}

.g-y-30 {
  --bs-gutter-y: 30px;
}

.g-y-40 {
  --bs-gutter-y: 40px;
}

.g-y-45 {
  --bs-gutter-y: 45px;
}

.g-y-50 {
  --bs-gutter-y: 50px;
}

.g-y-35 {
  --bs-gutter-y: 35px;
}

.text-13 {
  font-size: 13px !important;
}

.text-14 {
  font-size: 14px !important;
}

.text-15 {
  font-size: 15px !important;
}

.text-16 {
  font-size: 16px !important;
}

.text-17 {
  font-size: 17px !important;
}

.text-18 {
  font-size: 18px !important;
}

.text-19 {
  font-size: 19px !important;
}

.text-20 {
  font-size: 20px !important;
}

.text-21 {
  font-size: 21px !important;
}

.text-22 {
  font-size: 22px !important;
}

.text-24 {
  font-size: 24px !important;
}

.text-26 {
  font-size: 26px !important;
}

.dot-style {
  width: 8px;
  height: 8px;
  background-color: #d9d9d9;
  border-radius: 50%;
  display: inline-block;
}

.bg-green {
  background: #16b274;
}

.bg-red {
  background: #cd4040;
}

.bg-blue {
  background: #1a73e9;
}

.bg-yellow {
  background: #ff991a;
}

.bg-dark {
  background: #161f2b;
}

.bg-pink {
  background: #f95ffe;
}

.bg-success {
  background: #00c577 !important;
}

.bg-skye {
  background: #43cefc;
}

.bg-light {
  background: #f6f8fa;
}

.bg-light-2 {
  background: #f6f8fa;
}

.text-title {
  color: var(--ot-primary-text) !important;
}

.text-primary {
  color: var(--primary-color) !important;
}

.text-subtitle {
  color: var(--ot-subtitle-text) !important;
}

.text-tertiary {
  color: var(--ot-tertiary-text) !important;
}

.line-height-0 {
  line-height: 0 !important;
}

.line-height-1 {
  line-height: 1 !important;
}

.dropdown-text-light {
  color: #6f767e;
}

.dropdown-text-dark {
  color: #1a1d1f;
}

.dropdown-text-red {
  color: #ff0022;
}

.dropdown-text-disable {
  color: #b2bec3;
}

.text-green {
  color: #16b274;
}

.dropdown-text-blue {
  color: #5669ff;
}

.dropdown-content {
  margin-top: 24px;
  margin-left: 32px;
}

.dropdown-section {
  padding: 80px;
  background: #fcfcfc;
  margin-top: 8px;
}

.menu-divider {
  border-bottom: 1px solid var(--ot-primary-border);
  margin-bottom: 12px !important;
  padding-bottom: 11px;
}

.dropdown-section h1 {
  font-weight: 700;
  font-size: 32px;
  text-transform: uppercase;
  color: #120d26;
}

.dropdown-section p {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #4b4b4b;
}

.button-title {
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  color: #120d26;
}

.min-w-auto {
  min-width: auto !important;
}

.max-w-50 {
  max-width: 50px;
}

.max-w-80 {
  max-width: 80px;
}

.max-w-100 {
  max-width: 100px;
}

.max-w-120 {
  max-width: 120px;
}

.max-w-110 {
  max-width: 110px;
}

.max-w-140 {
  max-width: 140px !important;
}

.max-w-160 {
  max-width: 160px !important;
}

.max-w-180 {
  max-width: 180px !important;
}

.max-w-200 {
  max-width: 200px !important;
}

.max-w-220 {
  max-width: 220px !important;
}

.max-w-240 {
  max-width: 240px !important;
}

.max-w-230 {
  max-width: 230px !important;
}

.top-20 {
  top: 20px !important;
}

.top-12 {
  top: 12px !important;
}

.max-w-260 {
  max-width: 260px !important;
}

.max-w-280 {
  max-width: 280px !important;
}

.max-w-270 {
  max-width: 270px !important;
}

.max-w-300 {
  max-width: 300px !important;
}

.max-w-310 {
  max-width: 310px !important;
}

.min-w-310 {
  min-width: 310px !important;
}

.max-w-500 {
  max-width: 500px !important;
}

.max-w-400 {
  max-width: 400px !important;
}

.max-w-600 {
  max-width: 600px !important;
}

.min-w-96 {
  min-width: 96px;
}

.min-w-50 {
  min-width: 50px;
}

.min-w-80 {
  min-width: 80px;
}

.min-w-100 {
  min-width: 100px;
}

.min-w-150 {
  min-width: 150px;
}

.min-w-120 {
  min-width: 120px;
}

.min-w-125 {
  min-width: 140px;
}

.min-w-300 {
  min-width: 300px;
}

.min-w-400 {
  min-width: 400px;
}

.min-w-200 {
  min-width: 200px;
}

.max-width-265 {
  max-width: 265px !important;
}

.max-height-400 {
  max-height: 400px;
}

.max-height-335 {
  max-height: 335px;
}

.max-height-200 {
  max-height: 200px;
}

.max-height-300 {
  max-height: 300px;
}

.max-height-80vh {
  max-height: 80vh;
}

.over-flow-auto {
  overflow: auto;
}

.over-flow-y-auto {
  overflow-y: auto;
}

.over-flow-x-auto {
  overflow-x: auto;
}

.over-flow-hidden {
  overflow: hidden;
}

.over-flow-y-hidden {
  overflow-y: hidden;
}

.over-flow-x-hidden {
  overflow-x: hidden;
}

.bottom-0 {
  bottom: 0;
}

.bottom-10 {
  bottom: 10px;
}

.bottom-15 {
  bottom: 15px;
}

.bottom-20 {
  bottom: 20px;
}

.bottom-25 {
  bottom: 25px;
}

.right-0 {
  right: 0;
}

.right-10 {
  right: 10px;
}

.right-15 {
  right: 15px;
}

.right-20 {
  right: 20px;
}

.right-25 {
  right: 25px;
}

.left-0 {
  left: 0;
}

.left-10 {
  left: 10px;
}

.left-15 {
  left: 15px;
}

.left-20 {
  left: 20px;
}

.left-25 {
  left: 25px;
}

.max-height-100 {
  max-height: 100px;
}

.max-height-150 {
  max-height: 150px;
}

.max-height-250 {
  max-height: 250px;
}

.max-height-350 {
  max-height: 350px;
}

.max-height-500 {
  max-height: 500px;
}

.max-height-600 {
  max-height: 600px;
}

hr {
  margin: 11px 0;
  color: inherit;
  border: 0;
  border-top: 1px var(--ot-primary-border) solid;
  opacity: 1;
}

.column-count-2 {
  column-count: 2;

  @media (max-width: 768px) {
    column-count: 1;
  }
}

.column-count-3 {
  column-count: 3;

  @media (max-width: 768px) {
    column-count: 1;
  }
}

.column-count-4 {
  column-count: 4;

  @media (max-width: 768px) {
    column-count: 1;
  }
}

.column-count-5 {
  column-count: 5;

  @media (max-width: 768px) {
    column-count: 1;
  }
}

.pro-badge {
  background: #fff5e8;
  color: #ff991a;
  padding: 3px 5px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
  display: inline-block;
  overflow: hidden;
}

.rotate-180 {
  transform: rotate(180deg);
}

.rotate-90 {
  transform: rotate(90deg);
}

.text-break {
  word-wrap: break-word !important;
  word-break: break-word !important;
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -6px;
  background: var(--ot-tertiary-text);
  width: 18px;
  height: 17px;
  border-radius: 50%;
  line-height: 16px;
  font-size: 10px;
  font-weight: 700;
}

.row {
  --bs-gutter-x: 1rem;
}

.w-lg-100 {
  @media (max-width: 1200px) {
    width: 100% !important;
  }
}

.w-md-100 {
  @media (max-width: 991px) {
    width: 100% !important;
  }
}

.w-xl-100 {
  @media (max-width: 1400px) {
    width: 100% !important;
  }
}

.gap-lg-20 {
  @media (max-width: 1200px) {
    gap: 20px !important;
  }
}

.gap-md-20 {
  @media (max-width: 991px) {
    gap: 20px !important;
  }
}

/*------------------------------------------------------------------
  2. Button Style
--------------------------------------------------------------------*/
.btn-primary-dashed {
  padding: 12px 22px;
  font-size: 14px;
  font-weight: 600;
  text-transform: capitalize;
  border-radius: 4px;
  text-align: center;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  background: none;
  color: var(--ot-secondary-text);
  border: 1px dashed var(--ot-primary-border);
  transition: 0.3s;
  background: var(--primary-bg);
  display: flex;
  align-items: center;
  height: 40px;
  justify-content: center;
  gap: 8px;

  @media (max-width: 768px) {
    padding: 4px 10px;
    height: 33px;
  }
}

.btn-primary-dashed:hover,
.btn-primary-dashed:focus {
  border: 1px solid var(--ot-primary-border);
  background: var(--ot-primary-border);
}

.btn-primary-outline {
  padding: 12px 22px;
  font-size: 14px;
  font-weight: 600;
  text-transform: capitalize;
  border-radius: 4px;
  text-align: center;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  background: none;
  color: var(--ot-secondary-text);
  border: 1px solid var(--ot-primary-border);
  transition: 0.3s;
  background: var(--primary-bg);
  display: flex;
  align-items: center;
  height: 40px;
  justify-content: center;
  gap: 8px;

  @media (max-width: 768px) {
    padding: 4px 10px;
    height: 33px;
  }
}

.btn-primary-outline:hover,
.btn-primary-outline:focus {
  color: #fff !important;
  border: 1px solid transparent;
  background: var(--ot-primary-btn);
}

.btn-primary-fill {
  padding: 9px 19px;
  font-size: 14px;
  font-weight: 600;
  text-transform: capitalize;
  border-radius: 4px;
  text-align: center;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  background: var(--ot-primary-btn);
  color: #fff;
  border: 1px solid transparent;
  transition: 0.3s;
  display: flex;
  align-items: center;
  height: 40px;
  justify-content: center;
  gap: 8px;

  @media (max-width: 768px) {
    padding: 4px 10px;
    height: 33px;
  }
}

.btn-primary-fill i {
  font-size: 18px;
}

.disable-style.radio-active {
  background: #cd4040 !important;
}

.btn-danger-outline {
  padding: 12px 22px;
  font-size: 14px;
  font-weight: 600;
  text-transform: capitalize;
  border-radius: 4px;
  text-align: center;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  background: none;
  color: #fd2201;
  border: 1px solid #fd2201;
  transition: 0.3s;
  background: none;
  display: flex;
  align-items: center;
  height: 40px;
  justify-content: center;

  @media (max-width: 768px) {
    padding: 4px 10px;
    height: 33px;
  }
}

.cache-btn {
  padding: 7px 12px;
  font-size: 14px;
  font-weight: 500;
  text-transform: capitalize;
  border-radius: 4px;
  text-align: center;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  background: none;
  color: var(--primary-color);
  transition: 0.3s;
  background: var(--primary-bg);
  display: flex;
  align-items: center;
  display: flex;
  align-items: center;
  height: 40px;
  justify-content: center;
  gap: 6px;

  @media (max-width: 768px) {
    padding: 4px 10px;
    height: 33px;
  }
}

.btn-danger-outline:hover {
  color: #fff;
  border: 1px solid transparent;
  background: #fd2201;
}

.btn-danger-fill {
  padding: 12px 22px;
  font-size: 14px;
  font-weight: 600;
  text-transform: capitalize;
  border-radius: 4px;
  text-align: center;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  background: none;
  color: #fff;
  border: 1px solid #fd2201;
  transition: 0.3s;
  background: #fd2201;
  display: flex;
  align-items: center;
  display: flex;
  align-items: center;
  height: 40px;
  justify-content: center;
  gap: 8px;

  @media (max-width: 768px) {
    padding: 4px 10px;
    height: 33px;
  }
}

.create-btn {
  border: 0;
  outline: 0 !important;
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--primary-color);
  font-size: 14px;
  font-weight: 600;
  background: none;
  text-transform: capitalize;
  margin-bottom: 7px;
  line-height: 1;
}

.ot-dropdown-btn {
  background: var(--ot-primary-btn);
  border-radius: 4px;
  padding: 11px 14px;
  border: 0;
  outline: 0 !important;
  display: flex;
  align-items: center;
  gap: 13px;
}

.ot-dropdown-btn::after {
  content: none;
}

.btn-cancel-soft {
  padding: 9px 19px;
  font-size: 14px;
  font-weight: 600;
  text-transform: capitalize;
  border-radius: 4px;
  text-align: center;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  background: #ffe9e9;
  color: #d54f4f;
  border: 1px solid #fd6c6c;
  transition: 0.3s;
  display: flex;
  align-items: center;
  height: 40px;
  justify-content: center;

  @media (max-width: 768px) {
    padding: 4px 10px;
    height: 33px;
  }

  &.selected {
    background: #d54f4f;
    color: #fff;
    border: 1px solid transparent;
  }
}

.btn-approve-soft {
  padding: 9px 19px;
  font-size: 14px;
  font-weight: 600;
  text-transform: capitalize;
  border-radius: 4px;
  text-align: center;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  background: #e9fbe9;
  color: #4f8d4f;
  border: 1px solid #59db59;
  transition: 0.3s;
  display: flex;
  align-items: center;
  height: 40px;
  justify-content: center;
  gap: 8px;

  @media (max-width: 768px) {
    padding: 4px 10px;
    height: 33px;
  }

  &.selected {
    background: #4f8d4f;
    color: #fff;
    border: 1px solid transparent;
  }
}

.btn-regular-soft {
  padding: 9px 19px;
  font-size: 14px;
  font-weight: 600;
  text-transform: capitalize;
  border-radius: 4px;
  text-align: center;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  background: #cdcfff;
  color: #3947eb;
  border: 1px solid #3947eb;
  transition: 0.3s;
  display: flex;
  align-items: center;
  height: 40px;
  justify-content: center;
  gap: 8px;

  @media (max-width: 768px) {
    padding: 4px 10px;
    height: 33px;
  }

  &.selected {
    background: #3947eb;
    color: #fff;
    border: 1px solid transparent;
  }
}

.ot-btn-primary,
.ot-btn-info,
.ot-btn-warning,
.ot-btn-danger,
.ot-btn-success {
  color: #ffffff !important;
  border: 1px solid var(--ot-primary-border);
  background: var(--ot-primary-btn);
  border-radius: 4px;
}

.ot-btn-success {
  color: #ffffff !important;
  border: 1px solid #00ffa2;
  background: linear-gradient(90deg, #29d697 0%, #00ffa2 100%);
}

.ot-btn-danger {
  color: #ffffff !important;
  border: 1px solid #fd2201;
  background: linear-gradient(90deg, #ff6a54 0%, #fd2201 100%);
}

.ot-btn-warning {
  color: #ffffff !important;
  border: 1px solid #fec403;
  background: linear-gradient(90deg, #fdc400 0%, #fec403 100%);
}

.ot-btn-info {
  color: #ffffff !important;
  border: 1px solid #00ddff;
  background: linear-gradient(90deg, #138496 0%, #00ddff 100%);
}

/*------------------------------------------------------------------
  3. Dropdown Style
--------------------------------------------------------------------*/
.dropdown-with-down-arrow,
.dropdown2-with-down-arrow {
  margin-top: 12px;
  background-color: #fcfcfc;
  font-weight: 500;
  font-size: 16px;
  padding: 8px 16px;
  letter-spacing: 0.02em;
  color: #1a1d1f;
  border: 2px solid #f0eeee;
  border-radius: 4px;
}

.dropdown-with-down-arrow i,
.dropdown2-with-down-arrow i {
  margin-left: 12px;
  font-weight: bold;
}

.dropdown-with-down-arrow:hover,
.dropdown2-with-down-arrow:hover {
  border: 2px solid #5669ff;
  color: #5669ff;
}

.dropdown-with-down-arrow:hover i,
.dropdown2-with-down-arrow:hover i {
  color: #5669ff;
}

.dropdown-with-down-arrow:focus,
.dropdown2-with-down-arrow:focus {
  border: 2px solid #4d5ee5;
  color: #066ecf;
}

.dropdown-with-down-arrow:focus i,
.dropdown2-with-down-arrow:focus i {
  color: #066ecf;
}

.dropdwon-btn-section {
  margin-top: 40px;
}

.dropdown-with-dots {
  margin-top: 12px;
}

button {
  border: none;
  outline: none;
}

.dropdown-menu {
  margin-top: 10px !important;
}

#dropdown {
  background-color: #fcfcfc;
  border: 2px solid #f0eeee;
  border-radius: 5px 0 0 5px;
  font-weight: 500;
  font-size: 16px;
  padding: 8px 16px;
  letter-spacing: 0.02em;
  color: #1a1d1f;
}

#dropdown:hover {
  border: 2px solid #5669ff;
  color: #5669ff;
}

#dropdown:focus {
  border: 2px solid #4d5ee5;
  color: #066ecf;
}

#three-dots {
  background-color: #fcfcfc;
  border: 2px solid #f0eeee;
  border-radius: 0 5px 5px 0;
  padding: 8px;
  letter-spacing: 0.02em;
  color: #1a1d1f;
}

#three-dots:hover {
  border: 2px solid #5669ff;
  color: #5669ff;
}

#three-dots:focus {
  border: 2px solid #4d5ee5;
  color: #066ecf;
}

.dropdown-items,
.third-item,
.second-item {
  position: relative;
  width: 191px;
  background: #ffffff;
  border: 1px solid #f0eeee;
  border-radius: 4px;
}

.dropdown-items ul,
.third-item ul,
.second-item ul {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 16px;
  padding-right: 0;
  gap: 16px;
}

.dropdown-items ul .text-secondary>i,
.third-item ul .text-secondary>i,
.second-item ul .text-secondary>i {
  padding-right: 16px;
}

.dropdown-items ul li,
.third-item ul li,
.second-item ul li {
  font-weight: 400;
  font-size: 14px;
  padding: 8px;
  padding-right: 24px;
}

.dropdown-items ul .dropdown-text-dark>i,
.third-item ul .dropdown-text-dark>i,
.second-item ul .dropdown-text-dark>i {
  padding-left: 16px;
}

.second-item {
  display: block;
  width: 180px;
  position: absolute;
  opacity: 0;
  visibility: hidden;
  left: 100%;
  margin-top: -40px;
  transition: all ease-in-out 0.4s;
}

.third-item {
  display: none;
  width: 180px;
  position: absolute;
  left: 112%;
  margin-top: -66px;
}

ul li.dropdown-text-dark:hover .second-item {
  visibility: visible;
  opacity: 1;
  transition-delay: 0s;
  margin-left: 10px;
}

.dropdown-search-checkbox {
  margin-top: 12px;
  background-color: #fcfcfc;
  border: 2px solid #f0eeee;
  border-radius: 50px;
  font-weight: 500;
  font-size: 16px;
  letter-spacing: 0.02em;
  padding: 8px 16px;
  color: #5669ff;
}

#autosidebar {
  max-height: 100vh;
  /* overflow-y: auto; */
}

/*------------------------------------------------------------------
  4. Search Input Style
--------------------------------------------------------------------*/
.search-container {
  margin-top: 12px;
  width: 286px;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.search-input-checkbox input {
  width: 20px !important;
  height: 20px;
  border: 1px solid #eaeaea;
  border-radius: 4px;
}

.search-input-checkbox label {
  color: #6f767e;
  margin-left: 12px;
}

.search-input-checkbox ul li {
  display: flex;
}

.search-input {
  position: relative;
  text-align: center;
  padding: 15px 16px 15px 16px;
  border-bottom: 2px solid #f0eeee;
}

.search-input input {
  padding: 12px;
  width: 100%;
  border: 2px solid #f0eeee;
  border-radius: 50px;
  background: #fafafa;
  outline: none;
}

.search-input::-moz-placeholder {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.search-input::placeholder {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.search-input i {
  position: absolute;
  top: 29px;
  right: 27px;
  padding: 5px;
  background-color: #fafafa;
  color: #6f767e;
}

.search-items {
  padding: 17px 25px;
}

.search-items ul {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 16px;

  font-weight: 400;
  font-size: 14px;
}

.search-items ul li {
  color: #6f767e;
}

.search-input ul {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 8px 16px;
  gap: 16px;
}

.search-input ul li {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #6f767e;
}

.search-input ul li input {
  width: auto;
}

.search-input ul li ::-moz-placeholder {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.search-input ul li ::placeholder {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.search-container .btn-items {
  padding: 5px 17px 25px;
  text-align: end;
}

.search-container .btn-items .btn {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}

.search-container .btn-items .btn.clear {
  color: #6f767e;
}

.input-default input,
.input-date input {
  padding: 16px;
  width: 336px;
  height: 40px;
  border: 2px solid #f0eeee;
  border-radius: 4px;
  outline: none;
}

.input-default ::-moz-placeholder,
.input-date ::-moz-placeholder {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-default ::placeholder,
.input-date ::placeholder {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

/*  */
.input-field-focus {
  width: 360px;

  input {
    width: 100%;
    height: 40px;
    padding: 16px;
    border: 2px solid #f0eeee;
    border-radius: 4px;
    outline: none;

    &:focus-visible {
      box-shadow: 0 0 10px rgba(10, 175, 255, 0.35);
      border: 2px solid #0010f7;
      outline: none;
    }

    &::placeholder {
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      color: #b2bec3;
    }

    &::-moz-placeholder {
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      color: #b2bec3;
    }
  }
}

.input-pre-post {
  position: relative;
  margin-top: 16px;
  width: 296px;
  height: 40px;
}

.input-pre-post input {
  padding: 16px 45px;
  width: 296px;
  height: 40px;
  border: 2px solid #f0eeee;
  border-radius: 4px;
  outline: none;
}

.input-pre-post::-moz-placeholder {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-pre-post::placeholder {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-pre-post i {
  position: absolute;
  top: 16px;
  color: #6f767e;
}

.input-pre-post img {
  position: absolute;
  top: 2px;
  right: 1px;
  padding: 15px;
  width: 47px;
  background-color: #f7f7f7;
  border-left: 2px solid #f0eeee;
  border-radius: 0 5px 5px 0;
}

.input-https-post {
  position: relative;
  margin-top: 16px;
  width: 296px;
  height: 40px;
}

.input-https-post input {
  padding: 16px 43px 16px 110px;
  width: 296px;
  height: 40px;
  border: 2px solid #f0eeee;
  border-radius: 4px;
  outline: none;
}

.input-https-post::-moz-placeholder {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-https-post::placeholder {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-https-post i {
  position: absolute;
  top: 16px;
  color: #6f767e;
}

.input-https-post img {
  position: absolute;
  top: 2px;
  right: 1px;
  padding: 15px;
  width: 47px;
  background-color: #f7f7f7;
  border-left: 2px solid #f0eeee;
  border-radius: 0 5px 5px 0;
}

.input-https-post .https {
  position: absolute;
  color: #1a1d1f;
  top: 2px;
  left: 1.5px;
  background-color: #f7f7f7;
  padding: 12px;
  border-right: 2px solid #f0eeee;
  border-radius: 5px 0 0 5px;
}

.input-https-post2 {
  position: relative;
  margin-top: 16px;
  width: 296px;
  height: 40px;
}

.input-https-post2 input {
  padding: 16px 43px 16px 86px;
  width: 296px;
  height: 40px;
  border: 2px solid #f0eeee;
  border-radius: 4px;
  outline: none;
}

.input-https-post2::-moz-placeholder {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-https-post2::placeholder {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-https-post2 i {
  position: absolute;
  top: 16px;
  color: #6f767e;
}

.input-https-post2 .https {
  position: absolute;
  color: #1a1d1f;
  top: 2px;
  left: 1.5px;
  background-color: #f7f7f7;
  padding: 12px;
  border-right: 2px solid #f0eeee;
  border-radius: 5px 0 0 5px;
}

textarea::-moz-placeholder {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

textarea::placeholder {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.text-area .text-count {
  font-size: 10px;
  font-weight: 500;
  text-align: end;
  color: #b2bec3;
}

.Input-search-tab {
  position: relative;
  margin-top: 16px;
  width: 273px;
  height: 40px;
}

.Input-search-tab input {
  padding: 16px 43px 16px 16px;
  width: 100%;
  height: 100%;
  border: 2px solid #f0eeee;
  border-radius: 4px;
  outline: none;
}

.Input-search-tab ::-moz-placeholder {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.Input-search-tab::placeholder {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.Input-search-tab i {
  position: absolute;
  top: 0;
  color: #6f767e;
  border-left: 2px solid #f0eeee;
  padding: 16px;
}

.Input-search-tab .search {
  position: absolute;
  color: #ffffff;
  top: 0;
  right: 0;
  background-color: #5669ff;
  padding: 13px;
  border-radius: 0 5px 5px 0;
}

.Input-search-tab.search-color>input {
  width: 289px;
}

.Input-search-tab.search-color>i {
  right: -15px;
  color: #fafafa;
  background-color: #5669ff;
  border-radius: 0 5px 5px 0;
}

.Input-search-tab .search-tab1 {
  width: 237px;
}

.Input-search-tab.microphone>input {
  padding: 16px 110px 16px 16px;
}

.input-date {
  margin-top: 16px;
}

.input-date input {
  width: 286px;
}

.Range-date {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  width: 286px;
  height: 40px;
  flex-direction: row;
  border: 2px solid #f0eeee;
  border-radius: 4px;
  margin-top: 16px;
  padding: 10px 16px;
  gap: 5px;
}

.Range-date input {
  border: none;
  outline: none;
}

.time-field {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  flex-direction: row;
  width: 386px;
  gap: 5px;
  margin-top: 16px;
}

.time-field .input-time {
  border: 2px solid #f0eeee;
  border-radius: 4px;
  width: 107px;
  height: 50px;
}

.time-field .input-time select {
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  color: #1a1d1f;
  border: none;
  outline: none;
  padding: 11px 16px;
}

.time-field .select-time input {
  border: 2px solid #f0eeee;
  border-radius: 4px;
  width: 286px;
  height: 40px;
  outline: none;
  padding: 16px;
}

.input-group-start-end-time {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  justify-content: flex-end;
  flex-direction: row;
  gap: 10px;
}

.input-group-start-end-time .input-start-end-time {
  border: 2px solid #f0eeee;
  border-radius: 4px;
  width: 169px;
  height: 44px;
  padding: 12px 16px;
  color: #6f767e;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.input-group-start-end-time .input-start-end-time span {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}

option:hover {
  color: #5669ff;
}

.input-time-select {
  display: flex;
  justify-content: end;
  align-items: center;
  margin-top: 16px;
}

.input-time-select .input-start-end-time-select {
  border: 2px solid #f0eeee;
  border-radius: 4px;
  width: 178px;
  height: 60px;
  padding: 12px 16px;
  color: #6f767e;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.input-time-select .input-start-end-time-select p {
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  color: #0010f7;
}

.input-time-select .input-start-end-time-select h6 {
  font-style: normal;
  font-weight: 700;
  font-size: 16px;
  color: #1a1d1f;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  box-sizing: border-box;
  scroll-behavior: smooth;
}

/* Heading Text */
h6,
h5,
h4,
h3,
h2,
h1 {
  color: var(--ot-primary-text);
}

p,
a,
label {
  color: var(--ot-subtitle-text);
}

::-webkit-scrollbar {
  width: 8px;
  border-radius: 4px;
}

/* Track */
::-webkit-scrollbar-track {
  background: transparent;
}

.modal-dialog {
  @media (max-width: 576px) {
    max-width: 90%;
    margin: 0 auto;
  }
}

/*------------------------------------------------------------------
  5. Header CSS
--------------------------------------------------------------------*/
.header {
  position: fixed;
  top: 35px;
  left: 286px;
  right: 0;
  z-index: 20;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 11px 20px;
  background: #fff;
  transition: left 0.4s, -webkit-left 0.4s;
  height: 70px;
  border-bottom: 1px solid #b0ceff54;

  @media (max-width: 992px) {
    width: 100%;
    padding: 10px;
    left: 0;
    transition: transform 0.3s;
  }

  &.on-scroll {
    background: var(--ot-bg-secondary);
  }

  button {
    outline: none;
  }

  .dropdown {
    .dropdown-menu {
      transition: all 0.3s;
      max-height: 0;
      display: block;
      overflow: hidden;
      opacity: 0;
    }
  }

  .close-toggle {
    display: none;
    transform: rotate(180deg);

    @media (max-width: 992px) {
      display: block !important;
    }
  }

  .header-search {
    position: relative;
    width: 310px;

    @media (max-width: 992px) {
      display: none !important;
    }

    .search-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }

    .search-field {
      width: 100%;
      padding: 12px 10px 12px 36px;
      border-radius: 4px;
      border: none;
      background-color: transparent;

      &:focus-visible {
        outline-color: invert;
      }
    }
  }

  .header-controls {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 18px;

    .header-control-item {
      .item-content {
        .profile-navigate {
          display: flex;
          align-items: center;
          gap: 12px;
          width: max-content;

          &.show {
            display: flex !important;
          }

          .profile-photo {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            background: var(--primary-bg);
            overflow: hidden;

            img {
              width: 100%;
              height: 100%;
              border-radius: 50%;
              object-fit: cover;
            }
          }

          .profile-info {
            display: flex;
            flex-direction: column;
            align-items: baseline;

            h6 {
              font-weight: 600;
              font-size: 16px;
              line-height: 20px;
              margin: 0;
            }

            p {
              font-weight: 500;
              font-size: 12px;
              line-height: 15px;
              margin: 0;
            }
          }
        }

        button.icon {
          width: 24px;
          height: 24px;
        }
      }
    }
  }
}

.half-expand {
  .header {
    z-index: 22;
    left: 100px;
    transition: left 0.4s, -webkit-left 0.4s;
  }
}

/*------------------------------------------------------------------
  6. Topbar Dropdown Menu CSS
--------------------------------------------------------------------*/
.topbar-dropdown-menu {
  max-width: 425px;

  h1 {
    font-size: 20px;
    line-height: 32px;
    padding-left: 20px;
    font-weight: 600;
    color: var(--ot-primary-text);
  }

  .topbar-dropdown-content {
    .topbar-dropdown-item {
      padding: 20px;
      border-radius: 4px;
      position: relative;
      white-space: normal;
      gap: 1rem;

      &:hover {
        background-color: var(--ot-bg-primary);
      }

      &:active {
        color: #1e2125;
        background-color: #e9ecef;
      }

      &::before {
        content: "";
        position: absolute;
        bottom: 0;
        height: 1px;
        background: #efefef;
        left: 12px;
        right: 12px;
      }

      &:last-child::before {
        content: none !important;
      }

      .item-avater {
        min-width: 50px;
        min-height: 50px;
        max-width: 50px;
        max-height: 50px;
        border-radius: 50%;
        position: relative;

        img {
          width: 100%;
          height: 100%;
        }

        .item-badge {
          position: absolute;
          right: 0;
          bottom: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #6f767e;
          width: 10px;
          height: 10px;
          max-width: 10px;
          max-height: 10px;
          border-radius: 50%;

          &.active {
            background-color: #24c087;
          }

          &.item-icon-badge {
            width: 18px;
            height: 18px;
            min-width: 18px;
            min-height: 18px;
            padding: 10px;
            background-color: #5669ff;
          }
        }

        .online-status-badge {
          width: 12px;
          height: 12px;
          border: 1px solid #fff;
        }
      }

      .item-content {
        h6 {
          font-size: 14px;
          line-height: 18px;
          color: var(--ot-primary-text);
          font-weight: 500;

          span {
            color: var(--ot-primary-text);
          }

          &.message {
            display: flex;
            flex-direction: column;
            gap: 10px;
            font-weight: 700;
            line-height: 15px;

            span {
              font-size: 12px;
              font-weight: 400;
            }
          }
        }
      }

      .item-status {
        color: var(--ot-primary-text);
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 5px;

        .status-dot {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          min-width: 6px;
          min-height: 6px;
          background-color: #6f767e;

          &.active {
            background: #5669ff;
          }
        }
      }
    }
  }

  .topbar-dropdown-footer {
    background: var(--ot-primary-btn);
    padding: 12px;
    border-radius: 4px;
    margin-top: 20px;
    text-align: center;

    a {
      color: #fff;
    }

    &:hover {
      background: var(--ot-primary-btn);
    }
  }
}

/*------------------------------------------------------------------
  7. Profile Expand CSS
--------------------------------------------------------------------*/
.profile-expand-dropdown {
  min-width: 300px !important;
  max-width: 320px !important;

  .profile-expand-container {
    .profile-expand-list {
      gap: 20px;
      padding: 28px;

      .profile-photo {
        width: 80px;
        height: 80px;
        object-fit: cover;
        margin-bottom: 10px;
        border-radius: 50%;

        display: flex;
        justify-content: center;
        margin-left: auto;
        margin-right: auto;
        background: var(--primary-bg);

        img {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          object-fit: cover;
          margin-bottom: 10px;
        }
      }

      .profile-info {
        font-size: 20px;
        margin-bottom: 0;
        font-weight: 600;
        color: var(--ot-primary-text);
      }

      .profile-expand-item {
        position: relative;
        text-decoration: none;

        span {
          font-weight: 500;
          font-size: 15px;
          line-height: 29px;
          color: var(--ot-primary-text);
        }

        &:hover {
          span {
            color: var(--ot-primary-text);
          }
        }
      }
    }
  }
}

.divider {
  position: relative;

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    top: 50%;
    left: 0;
    right: 0;
    height: 20px;
    width: 1px;
    background: var(--ot-secondary-border);
    text-align: center;
    transform: translatey(-50%);
  }
}

.profile-buttons {
  background: var(--primary-bg);
  border-radius: 4px;
  padding: 5px 20px;
}

/*------------------------------------------------------------------
 8. Header Marquee Bar CSS
--------------------------------------------------------------------*/
.marquee-bar-hide .header-bottom-bar {
  display: none;
}

.marquee-bar-hide .sidebar {
  top: 0 !important;
}

.marquee-bar-hide .header {
  top: 0 !important;
}

.marquee-bar-show #layout-wrapper .page-content {
  padding-top: 45px;
}

/* margin-right: 20px; */

/*------------------------------------------------------------------
  9. Sidebar CSS
--------------------------------------------------------------------*/
.sidebar {
  position: fixed;
  top: 35px;
  left: 0;
  bottom: 0;
  z-index: 300;
  display: flex;
  flex-direction: column;
  width: 286px;
  background-color: #fff;
  transition: width 0.4s;
  border-right: 1px solid #b0ceff54;
  box-shadow: rgba(50, 50, 93, 0) 0 6px 12px -2px,
    rgba(0, 0, 0, 0.04) 0 3px 7px -3px;

  &.branch-enable {
    .sidebar-dropdown-menu {
      border-top: 1px solid var(--ot-primary-border);
      padding-top: 13px;
    }

    .sidebar-menu {
      height: calc(100vh - 220px) !important;
      padding: 9px 15px 15px 10px !important;
    }
  }

  .sidebar-header {
    position: relative;
    padding: 9px 14px;
    border-bottom: 1px solid #b0ceff54;
    display: flex;
    align-items: center;
    background: var(--ot-bg-secondary);
    z-index: 3000000;
    height: 70px;
  }

  .sidebar-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;

    .full-logo {
      opacity: 1;
      visibility: visible;
      width: 170px;
      height: 50px;
      object-fit: contain;
      transition: opacity 0.4s;
    }

    .half-logo {
      opacity: 0;
      visibility: hidden;
      height: 0;
      width: 55px;
      transition: opacity 0.4s;
      margin: 0 auto;
      position: relative;
      top: -10px;
    }
  }

  .sidebar-toggle {
    background: transparent;
    border: 0;
    outline: 0;
    cursor: pointer;
    padding: 0;
    margin: 0;
  }

  .sidebar-close {
    display: none;
    background: 0;
    font-size: 28px;
    padding: 0;
    margin-right: 10px;

    &:hover {
      color: red;
    }

    @media (max-width: 992px) {
      display: block;
    }
  }

  .sidebar-menu {
    height: calc(100vh - 148px);
    overflow-y: scroll;
    padding: 15px 15px 15px 10px;

    .sidebar-menu-section {
      .sidebar-menu-section-heading {
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;
        color: var(--primary-color);
        line-height: 28px;
        margin-left: 17px;
      }

      .parent-menu-list {
        padding-left: 0;
        list-style: none;
      }
    }

    .sidebar-menu-item {
      a {
        font-size: 14px;
        font-weight: 600;
        display: flex;
        align-items: center;
        padding: 11px 0 11px 20px;
        margin-bottom: 4px;
        gap: 10px;
        border-radius: 4px;
        color: var(--ot-primary-text);
        text-decoration: none;
        cursor: pointer;

        /* &::before {
                    content: "";
                    width: 8px;
                    height: 2px;
                    background: var(--ot-primary-text);
                } */

        /* &.parent-item-content::before {
                    content: none;
                } */

        &.active {
          background: var(--ot-bg-primary);
        }

        &.has-arrow.active::after {
          transform: rotate(-135deg) translate(0, -50%);
        }
      }

      >ul {
        padding-left: 0;
        list-style: none;

        li {
          a {
            padding-left: 47px;
            padding-top: 16px;
            padding-bottom: 16px;
          }

          ul {
            li {
              a {
                padding-left: 64px;
              }
            }
          }
        }
      }
    }
  }
}

.sidebar {
  @media (max-width: 992px) {
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(6px);
    z-index: 99;
    left: -100%;
    transition: -webkit-left 0.3s;
    transition: left 0.3s;
    transition: left 0.3s, -webkit-left 0.3s;
  }

  .sidebar-menu {
    .sidebar-menu-section {
      .sidebar-menu-item {
        >ul {
          >li {
            >a {
              padding-left: 48px;
              padding-top: 7px;
              padding-bottom: 7px;
              font-weight: 400;
              font-size: 14px;
              text-transform: capitalize;
            }
          }
        }

        .sidebar-menu-item {
          background: transparent !important;
          list-style-type: none;

          &.mm-active {
            .has-arrow::before {
              background: #fff;
            }
          }

          .has-arrow.active {
            &::before {
              background: var(--primary-color);
              background: transparent;
            }

            &::after {
              transform: rotate(-135deg) translate(0, -50%);
            }
          }
        }

        &.mm-active {
          >a {
            background: var(--ot-bg-primary);

            &.has-arrow::after {
              transform: rotate(225deg) translate(0, -50%);
            }
          }
        }

        .active {
          >a {
            background: transparent !important;
            color: var(--primary-color);

            /* &::before {
                            background: var(--primary-color) !important;
                        } */
          }
        }
      }
    }

    .half-expand & {
      margin-top: 20px;
    }
  }
}

.sidebar-menu-item.mm-active {
  >a::before {
    background: var(--ot-primary-btn);
  }

  ul {
    background: transparent;
    border-radius: 8px;
  }
}

.sidebar-expand {
  .sidebar {
    left: 0;
    transition: left 0.3s;
  }
}

.child-menu-list {

  .sidebar-menu-item:first-child,
  .nav-item:first-child {
    padding-top: 11px;
  }

  .sidebar-menu-item:last-child,
  .nav-item:last-child {
    padding-bottom: 8px;
  }
}

.sidebar-menu-item {
  &.mm-active {
    &>.parent-item-content::before {
      content: "";
      display: inline-block;
      width: 4px;
      height: 30px;
      background: var(--primary-color);
      position: absolute;
      left: -15px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 99;
      border-radius: 0 4px 4px 0;
    }

    &>.sidebar-menu-item.mm-active {
      & * .parent-item-content::before {
        display: none;
        content: unset;
        background: transparent;
      }
    }
  }
}

.sidebar {
  & .sidebar-menu {
    & .sidebar-menu-section {
      & .sidebar-menu-item {
        .sidebar-menu-item {
          .parent-item-content {
            margin-left: 28px;
            background-color: var(--primary-bg);
          }
        }
      }
    }
  }
}

.sidebar {
  .sidebar-menu {
    .sidebar-menu-section {
      .sidebar-menu-item {
        a.has-arrow {
          &::after {
            transform: rotate(135deg) translate(0, -50%);
          }
        }
      }
    }
  }
}

.sidebar {
  .sidebar-menu {
    .sidebar-menu-section {
      .sidebar-dropdown-menu {
        li.sidebar-menu-item {
          .parent-item-content {
            transition: all 0.3s ease;

            &:hover {
              background: var(--ot-bg-primary);
            }

            &.active {
              position: relative;

              &::before {
                content: "";
                display: inline-block;
                width: 4px;
                height: 30px;
                background: var(--primary-color);
                position: absolute;
                left: -15px;
                top: 50%;
                transform: translateY(-50%);
                z-index: 99;
                border-radius: 0 4px 4px 0;
              }
            }
          }

          >ul.child-menu-list {
            position: relative;

            &::before {
              content: "";
              position: absolute;
              left: 28px;
              top: 0;
              width: 100%;
              background: rgba(var(--secondary-color-rgb), 0.2);
              width: 1px;
              height: calc(100% - 32px);
            }

            li.sidebar-menu-item {

              &.active a,
              &:hover a {
                /* background: rgba(var(--primary-color-rgb), .01) !important; */
                background: transparent !important;

                &::before {
                  background: var(--primary-color) !important;
                }
              }

              &.active a span {
                /* background: #f0f3f5;
                                    padding: 5px 14px;
                                    border-radius: 30px; */
                font-weight: 500;
              }
            }

            a {
              position: relative;

              &::before {
                /* background: rgba(var(--secondary-color-rgb), 0.2); */
                background: transparent;
                width: 100%;
                width: 100%;
                height: 5px;
                content: "";
                position: absolute;
                left: 26px;
                top: 10px;
                width: 5px;
                border-radius: 50%;
                /* transform: translateY(-50%); */
                z-index: 2;
              }

              &::after {
                content: "";
                display: inline-block;
                width: 18px;
                height: 11px;
                background-image: url("data:image/svg+xml,%3Csvg width='23' height='11' viewBox='0 0 23 11' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M23 10H11.4612C7.47148 10 3.86349 7.6284 2.28124 3.9658L1 1' stroke='%2300000033'/%3E%3C/svg%3E");
                background-repeat: no-repeat;
                background-size: contain;
                position: absolute;
                left: 27px;
                top: 10px;
              }
            }
          }
        }
      }
    }
  }
}

.half-expand {
  .sidebar {
    width: 100px;

    .sidebar-header {
      .sidebar-logo {
        .full-logo {
          opacity: 0;
          visibility: hidden;
          height: 0;
        }

        .half-logo {
          opacity: 1;
          visibility: visible;
          height: auto;
        }
      }

      .half-expand-toggle {
        position: absolute;
        left: 100%;
        margin-left: 10px;

        img {
          transform: rotate(180deg);
        }
      }

      .sidebar-close:hover {
        color: #edfaff;
      }
    }

    .sidebar-menu {
      overflow: visible;
      position: absolute;
      left: 50%;
      top: 8%;
      z-index: 2000;
      height: calc(100vh - 100px);
      transform: translateX(-50%);
    }
  }

  .sidebar-menu-section-heading {
    margin-left: 0;
    justify-content: center;

    .on-half-expanded {
      display: none;
    }
  }

  .parent-menu-list {
    padding-left: 0;

    .sidebar-menu-item {
      position: relative;

      .parent-item-content {
        padding: 15px 0;
        justify-content: center;

        span {
          display: none;
        }

        &::after {
          content: none;
        }
      }

      a {
        border: none;
      }
    }

    >li>ul {
      position: absolute;
      top: 0;
      left: 85px;
      width: 236px;
      background: #fcfcfc !important;
      border: 1px solid #eaeaea;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
      border-radius: 10px;
    }
  }

  .main-content {
    margin-left: 100px;
    transition: margin-left 0.3s;
  }
}

.half-expand {
  .sidebar {
    .sidebar-menu {
      width: 100% !important;
    }

    ul.parent-menu-list {
      >li {
        >ul {
          left: 78px !important;
        }
      }
    }
  }

  .sidebar-dropdown-menu {
    li {
      position: relative;

      &:hover {

        >ul,
        >ul.collapse {
          display: block !important;
          height: auto !important;
          z-index: 1000;
        }
      }
    }

    >li {
      ul {
        position: absolute;
        left: 101%;
        top: 0;
        min-width: 200px;
        display: none;
        background: #fff !important;
        border-radius: 8px;
        border: 1px solid var(--ot-primary-border);
      }
    }
  }
}

.half-expand .sidebar .menu-divider {
  display: none !important;
}

.half-expand .footer-content {
  display: none !important;
}

.half-expand .sidebar {
  .sidebar-header {
    .sidebar-logo {
      .sidebar-logo {
        .full-logo {
          display: none;
        }

        .half-logo {
          display: block;
          opacity: 1;
          visibility: visible;
          height: auto;
          width: 100%;
          transition: opacity 0.4s;
          margin: 0 auto;
          position: relative;
          top: 0;
        }
      }
    }
  }

  .sidebar-menu {
    padding: 15px !important;
    margin: 0 !important;

    .sidebar-menu-section {
      .sidebar-dropdown-menu {
        li.sidebar-menu-item {
          .parent-item-content {
            padding: 11px !important;
            transition: all 0.3s ease;
          }

          >ul.child-menu-list {
            position: absolute;
            left: 68px !important;

            &::before {
              content: "";
              position: absolute;
              left: 27px;
              top: 0;
              width: 100%;
              background: rgba(var(--secondary-color-rgb), 0.2);
              width: 1px;
              height: calc(100% - 52px);
              top: 20px;
            }

            li.sidebar-menu-item {
              &>ul {
                left: 98px !important;
              }
            }
          }
        }
      }
    }
  }
}

.half-expand {
  .sidebar {
    & .sidebar-menu {
      & .sidebar-menu-section {
        & .sidebar-dropdown-menu {
          & li.sidebar-menu-item {
            &>ul.child-menu-list {
              left: auto;
              right: 68px;

              &::before {
                display: none;
              }

              & a {
                padding-left: 24px;
                margin-bottom: 0;

                &::before {
                  display: none;
                }

                &::after {
                  display: none;
                }
              }
            }
          }
        }
      }
    }
  }
}

.form-control:focus {
  background: none !important;
  color: var(--ot-subtitle-text) !important;
  box-shadow: none;
  border-color: var(--ot-primary-btn) !important;
  outline: none;
}

textarea.form-control:focus {
  box-shadow: none !important;
  background: none !important;
  color: var(--ot-subtitle-text) !important;
  border: 1px solid var(--ot-primary-btn);
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.main-content {
  margin-left: 286px;
  padding: 88px 20px 20px;

  transition: -webkit-left 0.3s;
  transition: margin-left 0.3s;
  transition: margin-left 0.3s, -webkit-margin-left 0.3s;

  @media (max-width: 992px) {
    margin-left: 0;
  }

  @media (max-width: 576px) {
    padding: 80px 10px 10px;
  }
}

/*------------------------------------------------------------------
  11. Card
--------------------------------------------------------------------*/

.ot-card {
  background-color: var(--ot-bg-secondary);
  filter: drop-shadow(0px 2px 8px rgba(0, 0, 0, 0.02));
  border-radius: 8px;
  padding: 24px;
  border: none;

  .card-header,
  .card-body,
  .card-footer {
    padding: 0;
    margin: 0;
    background: transparent;
    border: none;
  }

  .card-body {
    padding-top: 20px;
  }
}

/*------------------------------------------------------------------
    12. Chart CSS
--------------------------------------------------------------------*/

.chart-custom-content {
  p {
    font-weight: 400;
    font-size: 12px;
    line-height: 21px;
  }

  h2 {
    font-weight: 600;
    font-size: 20px;
    line-height: 24px;
    color: #1a1d1f;
  }

  .percentage-spike {
    img {
      width: 12px;
      height: 12px;
    }

    .primary-blue {
      color: #5669ff;
    }

    .secondary-blue {
      color: #55b1f3;
    }

    .primary-red {
      color: #ff4f75;
    }

    .primary-yellow {
      color: #ff991a;
    }

    &:nth-child(1) {
      background-color: #feb391;
    }
  }
}

.chart-header {
  p {
    font-weight: 500;
    font-size: 12px;
    line-height: 15px;
    color: var(--ot-primary-text);
  }
}

/*------------------------------------------------------------------
  13. Tables css
--------------------------------------------------------------------*/

.table-toolbar {
  font-size: 14px;
  font-weight: 500;
  line-height: 18px;
  color: #000000;

  .form-select {
    font-size: 14px;
    font-weight: 500;
    line-height: 18px;
    color: var(--ot-primary-text);
    width: auto;
    padding: 10px 30px 10px 14px;
    border-radius: 4px;
    background-repeat: no-repeat;
    background-position: right 14px center;
    background-size: 12px;
    outline: 0;
    box-shadow: none;
    background-color: #fff;
    height: 40px;
  }

  .btn-add {
    text-decoration: none;
    padding: 10px 17px;
    border: 1px solid #21c6fb;
    background: var(--ot-primary-btn);
    border-radius: 4px;
    display: block;
    text-align: center;

    font-size: 14px;
    font-weight: 600;
    line-height: 18px;
    color: #ffffff;
  }
}

.table-toolbar .btn-daterange,
.table-toolbar .dropdown-export .btn-export,
.table-toolbar .dropdown-designation .btn-designation {
  padding: 12px 22px;
  font-size: 14px;
  font-weight: 500;
  text-transform: capitalize;
  border-radius: 4px;
  text-align: center;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  background: none;
  color: var(--ot-secondary-text);
  border: 1px solid var(--ot-primary-border);
  transition: 0.3s;
  background: var(--primary-bg);
  display: flex;
  align-items: center;
  height: 40px;
  justify-content: center;
  gap: 9px;
}

.table-toolbar .btn-daterange .icon,
.table-toolbar .dropdown-export .btn-export .icon,
.table-toolbar .dropdown-designation .btn-designation .icon {
  color: var(--ot-primary-text);
  font-size: 14px;
}

.table-toolbar .btn-daterange:hover,
.table-toolbar .dropdown-export .btn-export:hover,
.table-toolbar .dropdown-designation .btn-designation:hover {
  box-shadow: none;
  background: var(--ot-primary-btn);
  color: #ffffff;
}

.table-toolbar .btn-daterange:hover .icon,
.table-toolbar .dropdown-export .btn-export:hover .icon,
.table-toolbar .dropdown-designation .btn-designation:hover .icon {
  color: #ffffff;
}

.table-toolbar .btn-daterange:focus,
.table-toolbar .dropdown-export .btn-export:focus,
.table-toolbar .dropdown-designation .btn-designation:focus {
  box-shadow: none;
  background: var(--ot-primary-btn);
  color: #ffffff;
}

.table-toolbar .btn-daterange:focus .icon,
.table-toolbar .dropdown-export .btn-export:focus .icon,
.table-toolbar .dropdown-designation .btn-designation:focus .icon {
  color: #ffffff;
}

.table-toolbar .search-box {
  position: relative;
  border: 1px solid var(--ot-primary-border);
  border-radius: 4px;
  height: 40px;
}

.table-toolbar .search-box .form-control,
.table-toolbar .search-box .profile-content .profile-body-form .form-box .form-select,
.profile-content .profile-body-form .form-box .table-toolbar .search-box .form-select {
  padding: 8px 45px 8px 16px;
  border: 1px solid transparent;
  box-shadow: none;
  background-color: #fff;
  color: var(--ot-primary-text);
}

.table-toolbar .search-box .form-control::-moz-placeholder,
.table-toolbar .search-box .profile-content .profile-body-form .form-box .form-select::-moz-placeholder,
.profile-content .profile-body-form .form-box .table-toolbar .search-box .form-select::-moz-placeholder {
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  color: #b2bec3;
}

.table-toolbar .search-box .form-control::placeholder,
.table-toolbar .search-box .profile-content .profile-body-form .form-box .form-select::placeholder,
.profile-content .profile-body-form .form-box .table-toolbar .search-box .form-select::placeholder {
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  color: #b2bec3;
}

.search-box .icon {
  position: absolute;
  top: 50%;
  right: 12px;
  transform: translateY(-50%);
  opacity: 0.7;
}

.table-toolbar .dropdown-designation .dropdown-menu {
  background: var(--ot-bg-secondary);
  border: 1px solid var(--ot-primary-border);
  position: relative;
  color: var(--ot-primary-text) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  margin-top: 10px !important;
  padding: 0;
}

.table-toolbar .dropdown-designation .search-content {
  padding: 20px;
}

.table-toolbar .dropdown-designation .search-content .search-box {
  position: relative;
  border: 1px solid #eaeaea;
  border-radius: 50px;
}

.table-toolbar .dropdown-designation .search-content .search-box .form-control,
.table-toolbar .dropdown-designation .search-content .search-box .profile-content .profile-body-form .form-box .form-select,
.profile-content .profile-body-form .form-box .table-toolbar .dropdown-designation .search-content .search-box .form-select {
  width: 232px;
  padding: 8px 45px 8px 16px;
  border: none;
  border-radius: 50px;
  box-shadow: none;
  background-color: #ffffff;
  color: #1a1d1f;
}

.table-toolbar .dropdown-designation .search-content .search-box .form-control::-moz-placeholder,
.table-toolbar .dropdown-designation .search-content .search-box .profile-content .profile-body-form .form-box .form-select::-moz-placeholder,
.profile-content .profile-body-form .form-box .table-toolbar .dropdown-designation .search-content .search-box .form-select::-moz-placeholder {
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  color: #b2bec3;
}

.table-toolbar .dropdown-designation .search-content .search-box .form-control::placeholder,
.table-toolbar .dropdown-designation .search-content .search-box .profile-content .profile-body-form .form-box .form-select::placeholder,
.profile-content .profile-body-form .form-box .table-toolbar .dropdown-designation .search-content .search-box .form-select::placeholder {
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  color: #b2bec3;
}

.table-toolbar .dropdown-designation .search-content .search-box .icon {
  position: absolute;
  top: 50%;
  right: 12px;
  transform: translateY(-50%);
  color: #6f767e;
}

.table-toolbar {
  .dropdown-designation {
    .list {
      padding: 26px;
      margin: 0;

      .list-item {
        list-style: none;
        margin-bottom: 24px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .dropdown-item {
      padding: 0;
      display: block;

      font-size: 14px;
      font-weight: 400;
      line-height: 16px;
      color: var(--ot-primary-text);

      &:hover,
      &:focus,
      &:active {
        background-color: var(--ot-bg-secondary);
        color: var(--ot-primary-text);
      }

      .icon {
        color: var(--ot-primary-text);
        font-size: 18px;
      }
    }
  }

  .dropdown-action {
    .btn-dropdown {
      background: none;
      color: var(--ot-primary-text);
      font-size: 22px;
    }

    .dropdown-menu {
      position: relative;
      background: var(--ot-bg-secondary);
      border: 1px solid var(--ot-primary-border);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      border-radius: 4px;
      padding: 16px;
      margin-top: 10px !important;

      &::before {
        content: "";
        position: absolute;
        top: -5px;
        right: 8px;
        background: var(--ot-bg-secondary);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        transform: rotate(45deg);
        padding: 10px;
        border: 1px solid var(--ot-primary-border);
      }

      &::after {
        content: "";
        position: absolute;
        top: 0;
        right: 5px;
        background: var(--ot-bg-secondary);
        padding: 10px 15px;
      }

      li {
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .dropdown-item {
      padding: 0;
      font-size: 14px;
      border-radius: 4px;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 10px;

      &:hover,
      &:focus,
      &:active {
        background: var(--ot-bg-secondary);
        color: var(--ot-primary-text);
      }

      .icon {
        color: var(--ot-primary-text);
        font-size: 16px;
      }
    }

    li:not(:last-child) .dropdown-item {
      border-bottom: 1px dashed var(--ot-primary-border);
      margin-bottom: 6px;
      padding-bottom: 8px;
    }
  }

  .dropdown-export {
    .dropdown-menu {
      position: relative;
      background-color: var(--ot-bg-secondary);
      border: 1px solid var(--ot-primary-border);
      color: var(--ot-primary-text);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      border-radius: 4px;
      padding: 24px;
      margin-top: 10px !important;

      li {
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .dropdown-item {
      padding: 0;
      display: block;

      font-size: 14px;
      font-weight: 500;
      line-height: 28px;
      color: var(--ot-primary-text);

      &:hover,
      &:focus,
      &:active {
        background-color: var(--ot-bg-secondary);
        color: var(--ot-primary-text);
      }

      .icon {
        color: var(--ot-primary-text);
        font-size: 16px;
      }
    }

    li:not(:last-child) .dropdown-item {
      border-bottom: 1px dashed var(--ot-primary-border);
      margin-bottom: 6px;
      padding-bottom: 8px;
      line-height: 1;
    }
  }
}

.table {
  border-color: var(--ot-primary-border);
  vertical-align: middle;
  margin: 0;

  .sorting-asc,
  .sorting-desc {
    position: relative;

    &::before {
      position: absolute;
      right: 16px;
      font-size: 12px;
      font-weight: 900;
      top: 42%;
      cursor: pointer;
      transform: translateY(-42%);
    }

    &::after {
      position: absolute;
      right: 16px;
      font-size: 12px;
      font-weight: 900;
      top: 62%;
      cursor: pointer;
      transform: translateY(-62%);
    }
  }

  .sorting-asc {
    &::before {
      color: #1a1d1f;
    }

    &::after {
      color: #bebfc0;
    }
  }

  .sorting-desc {
    &::before {
      color: #bebfc0;
    }

    &::after {
      color: #1a1d1f;
    }
  }

  /* .check-box {
        .form-check-input {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            border: 1px solid #eaeaea;
            background-color: #ffffff;

            &:focus {
                box-shadow: none;
                
            }

            &:checked {
                background-repeat: no-repeat;
                background-position: center;
            }
        }
    } */

  .thead {
    border-bottom-color: transparent;
    --bs-table-bg: var(--primary-bg);
    border-top-color: transparent;
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
    z-index: 9;

    tr {
      th {
        font-size: 14px;
        font-weight: 600;
        line-height: 21px;
        color: var(--ot-primary-text);
        padding: 9px 16px;
        vertical-align: middle;
        white-space: nowrap;
        border-color: transparent;
      }
    }
  }

  .tbody {
    tr {
      &:nth-of-type(odd) {
        background: var(--primary-bg);
      }

      td {
        font-style: normal;
        font-size: 12px;
        font-weight: 500;
        line-height: 21px;
        color: var(--ot-primary-text);
        padding: 6px 16px;
        vertical-align: middle;
        white-space: nowrap;
      }
    }
  }

  .dropdown-action {
    .btn-dropdown {
      width: 28px;
      height: 28px;
      font-size: 15px;
      background: none;
      font-weight: 300;
      color: var(--ot-subtitle-text);

      &:focus {
        background: none;
        box-shadow: unset;
        color: inherit;
      }
    }

    .dropdown-menu {
      background-color: var(--ot-bg-secondary) !important;
      color: var(--ot-primary-text) !important;
      border-radius: 4px;
      padding: 18px;
      margin-top: 10px !important;
      box-shadow: rgba(149, 157, 165, 0.2) 0 8px 24px;

      li {
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .dropdown-item {
      padding: 0;
      display: block;

      font-size: 14px;
      font-weight: 500;
      line-height: 28px;
      color: var(--ot-primary-text);
      display: flex;
      align-items: center;
      gap: 7px;
      line-height: 1;
      margin-bottom: 13px;

      &:hover,
      &:focus,
      &:active {
        background-color: transparent;
        color: var(--ot-primary-text);
      }

      .icon {
        color: var(--primary-color);
        font-size: 16px;
      }
    }
  }
}

.check-box {
  cursor: pointer;

  .form-check-input {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid #eaeaea;
    background-color: #ffffff;
    cursor: pointer;

    &:focus {
      box-shadow: none;
    }

    &:checked {
      background-repeat: no-repeat;
      background-position: center;
    }
  }
}

/*noinspection CssUnknownTarget*/
.check-box .form-check-input:checked {
  background: url("/assets/images/ok.svg"), var(--ot-primary-btn);
  background-repeat: no-repeat;
  background-position: center;
  border: none;
}

.table-bordered> :not(caption)>*>* {
  border-width: 0;
}

/*------------------------------------------------------------------
  14. Charts
--------------------------------------------------------------------*/

.statistic-card .card-heading .card-title {
  font-weight: 600;
  font-size: 20px;
  line-height: 32px;
  color: #1a1d1f;
  margin: 0;
  padding: 0;
}

/* Tables */
.badge-basic-success-text {
  font-weight: 600;
  font-size: 12px;
  border-radius: 50px;
  padding: 4px 10px;
  background: var(--ot-bg-badge-light-success);
  color: var(--ot-text-badge-deep-success);
}

.badge-basic-danger-text {
  font-weight: 600;
  font-size: 12px;
  border-radius: 50px;
  padding: 4px 10px;
  background: var(--ot-bg-badge-light-danger);
  color: var(--ot-text-badge-deep-danger);
}

.badge-basic-warning-text {
  font-weight: 600;
  font-size: 12px;
  border-radius: 50px;
  padding: 4px 10px;
  background: var(--ot-bg-badge-light-warning);
  color: var(--ot-text-badge-deep-warning);
}

.badge-basic-info-text {
  font-weight: 600;
  font-size: 12px;
  border-radius: 50px;
  padding: 4px 10px;
  background: #e5feff;
  color: #00dfe5;
}

.badge-basic-primary-text {
  font-weight: 600;
  font-size: 12px;
  border-radius: 50px;
  padding: 4px 10px;
  background: #eef0ff;
  color: #5669ff;
}

.page-heading h1 {
  font-size: 30px;
  line-height: 38px;
  color: #1a1d1f;
}

.component-page-heading h1 {
  font-weight: 600;
  font-size: 20px;
  line-height: 32px;
}

.basic-table,
.table-color-col,
.ot-table-bg {
  width: 100%;
}

.basic-table thead th,
.table-color-col thead th,
.ot-table-bg thead th {
  height: 56px;
  border-bottom: 1px solid #e9e9ef;
  padding-left: 16px;
  color: #1a1d1f;
}

.basic-table thead th:first-child,
.table-color-col thead th:first-child,
.ot-table-bg thead th:first-child {
  border-radius: 10px 0 0 0;
}

.basic-table thead th:last-child,
.table-color-col thead th:last-child,
.ot-table-bg thead th:last-child {
  border-radius: 0 10px 0 0;
}

.basic-table tbody td,
.table-color-col tbody td,
.ot-table-bg tbody td {
  font-weight: 400;
  padding-left: 16px;
  height: 56px;
  color: #6f767e;
}

.ot-table-bg th,
.ot-table-bg tr:nth-child(even) {
  background-color: #f7f7f7;
}

.table-color-col th:nth-child(odd),
.table-color-col td:nth-child(odd) {
  background-color: #f7f7f7;
}

.table-color-col td {
  border-bottom: 1px solid #f5f5f5;
}

.table-color-col tr:last-child td {
  border: none;
}

/*------------------------------------------------------------------
  15. Progress
--------------------------------------------------------------------*/

.progress-size,
.progress-darkviolet,
.progress-skyblue,
.progress-violet,
.progress-green,
.progress-darkblue {
  width: 100%;
  height: 8px;
}

.progress-darkblue {
  background-color: #dde1ff;
}

.progress-darkblue .progress-bar-darkblue {
  border-radius: 4px;
  background-color: #0010f7;
}

.progress-green {
  background-color: #e9faf4;
}

.progress-green .progress-bar-green {
  border-radius: 4px;
  background-color: #29d697;
}

.progress-violet {
  background-color: #ebe7ff;
}

.progress-violet .progress-bar-violet {
  border-radius: 4px;
  background-color: #c8bbfb;
}

.progress-skyblue {
  background-color: #d5e8fe;
}

.progress-skyblue .progress-bar-skyblue {
  border-radius: 4px;
  background-color: #4797ff;
}

.progress-darkviolet {
  background-color: #e8defe;
}

.progress-darkviolet .progress-bar-darkviolet {
  border-radius: 4px;
  background-color: #9d6fff;
}

.ot-badge.primary {
  background-color: #e9faf4;
  color: #7ab668;
  font-weight: 600;
  font-size: 12px;
  line-height: 15px;
  padding: 4px 10px;
  border-radius: 50px;
}

.btn-select-content {
  width: 305px;
  height: 50px;
}

.btn-select-content .btn-select {
  width: 100%;
  height: 50px;
  border: 1px solid #eaeaea;
  border-radius: 4px;

  font-weight: 400;
  font-size: 14px;
  background-color: white;
  color: #b2bec3;
}

.btn-select-content .btn-select:hover {
  box-shadow: 0 0 10px rgba(10, 175, 255, 0.35);
  border: 1px solid #0010f7;
  outline: none;
}

.btn-select-content ul {
  width: 305px;
  padding: 20px;
}

.btn-select-content .sub-menu {
  position: relative !important;
  width: 100% !important;
  transform: translate(0px, 0px) !important;
}

.input-Range-date {
  height: 50px;
  flex-direction: row;
  border: 1px solid #eaeaea;
  border-radius: 4px;
  padding: 0 10px;
  gap: 25px;
}

.input-Range-date input {
  border: none;
  outline: none;
}

.input-Range-date:hover {
  box-shadow: 0 0 10px rgba(10, 175, 255, 0.35);
  border: 1px solid #0010f7;
  outline: none;
}

.btn-primary-custom {
  font-weight: 600;
  height: 50px;
  background: var(--ot-primary-btn);
  border: none;
}

.input-search-with-icon {
  position: relative;
  width: 320px;
  height: 40px;
}

.input-search-with-icon input {
  width: 100%;
  height: 100%;
  padding: 10px 45px 10px 16px;
  border: 1px solid #eaeaea;
  border-radius: 4px;
  outline: none;
}

.input-search-with-icon input:focus-visible {
  box-shadow: 0 0 10px rgba(10, 175, 255, 0.35);
  border: 1px solid #0010f7;
  outline: none;
}

.input-search-with-icon ::-moz-placeholder {
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-search-with-icon ::placeholder {
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-search-with-icon i {
  position: absolute;
  top: 12px;
  color: #6f767e;
}

.button-group {
  border-color: #eaeaea !important;
}

.button-group:hover {
  background-color: rgba(161, 169, 255, 0.2039215686) !important;
  border-color: #5669ff !important;
}

.basic-table.pagination-content .pagination,
.pagination-content.ot-table-bg .pagination,
.pagination-content.table-color-col .pagination {
  width: 368px;
}

.basic-table.pagination-content .pagination li,
.pagination-content.ot-table-bg .pagination li,
.pagination-content.table-color-col .pagination li {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3px;
  width: 32px;
  border: 1px solid #eaeaea;
  border-radius: 4px;
}

.basic-table.pagination-content .pagination li a,
.pagination-content.ot-table-bg .pagination li a,
.pagination-content.table-color-col .pagination li a {
  color: #1a1d1f;
}

.basic-table.pagination-content .pagination li a i,
.pagination-content.ot-table-bg .pagination li a i,
.pagination-content.table-color-col .pagination li a i {
  color: #6f767e;
}

.basic-table.pagination-content .pagination li:hover a,
.pagination-content.ot-table-bg .pagination li:hover a,
.pagination-content.table-color-col .pagination li:hover a {
  color: #5669ff;
}

.basic-table.pagination-content .pagination li:hover,
.pagination-content.ot-table-bg .pagination li:hover,
.pagination-content.table-color-col .pagination li:hover {
  border-color: #5669ff;
}

.selected {
  background: var(--ot-primary-btn);
}

.selected>a {
  color: white !important;
}

.table-top-componet .right-component a {
  float: right;

  @media (max-width: 767.99px) {
    float: left;
    margin-top: 20px;
  }
}

/*------------------------------------------------------------------
  16. Accordion
--------------------------------------------------------------------*/

.custom-accordion {
  margin: 0;
  padding: 0;
  color: #2d3436;
  font-size: 14px;
  line-height: 1.5715;
  list-style: none;
  background-color: #fff;
  border: 1px solid #b2bec3 !important;
  border-radius: 4px;

  .accordion-button {
    border-bottom: 1px solid #b2bec3 !important;

    &:not(.collapsed) {
      color: #5669ff;
      background-color: transparent !important;
    }

    &:focus {
      border-color: transparent !important;
      box-shadow: none !important;
    }
  }

  .accordion-tag {
    background: #eef0ff;
    border: 1px solid #5669ff;
    color: #4d5ee5;
    padding: 0 16px;
    border-radius: 10px;
  }
}

/*------------------------------------------------------------------
 17. Signup, Signin 
--------------------------------------------------------------------*/
.col-custom-height {
  min-height: 100vh;
}

.left-box .left-box-image img {
  max-width: 100%;
  max-height: 100%;
}

.left-box .title {
  font-weight: 700;
  font-size: 30px;
  background: var(--ot-primary-btn);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.left-box .text {
  font-weight: 400;
  font-size: 14px;
  font-size: 16px;
}

.form-wrapper {
  @media (max-width: 767.99px) {
    padding: 20px 50px 50px !important;
  }

  @media (max-width: 576px) {
    padding: 30px 40px 40px !important;
  }

  @media (max-width: 480px) {
    padding: 20px 20px 20px !important;
  }

  .form-content {
    width: 360px;
    gap: 20px;

    @media (max-width: 767.99px) {
      width: 340px;
      padding: 20px;
    }

    @media (max-width: 576px) {
      width: 320px;
      gap: 20px;
      gap: 10px;
    }

    .form-heading-text {
      .title {
        font-weight: 700;
        font-size: 38px;
        line-height: 54px;
        color: #1a1d1f;

        @media (max-width: 767.99px) {
          font-size: 30px;
        }

        @media (max-width: 576px) {
          font-size: 28px;
        }
      }

      .text {
        font-weight: 400;
        font-size: 14px;
        line-height: 21px;

        @media (max-width: 576px) {
          font-size: 13px;
        }
      }
    }

    .form {
      gap: 20px;

      @media (max-width: 767.99px) {
        width: 100%;
        gap: 10px;
      }

      label {
        font-weight: 500;
        font-size: 14px;
        color: #1a1d1f;

        @media (max-width: 576px) {
          font-size: 13px;
        }
      }
    }

    .text-font {
      font-weight: 400;
      font-size: 14px;
    }

    .link-text {
      background: var(--ot-primary-btn);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .text-privacy-policy {
      font-weight: 400;
      font-size: 12px;
    }
  }
}

.submit-button {
  width: 100%;
  background: var(--ot-primary-btn);
  border-radius: 4px;
  color: white;

  font-weight: 400;
  font-size: 14px;
  font-weight: 600;
}

.social-media-content {
  width: 100%;
  gap: 20px;
}

.social-media-content .social-media-items {
  width: 100%;
  height: 45px;
  padding: 16px;
  border: 2px solid #f0eeee;
  border-radius: 4px;
  outline: none;
}

.social-media-content .social-media-items:hover {
  box-shadow: 0 0 10px rgba(10, 175, 255, 0.35);
  border: 2px solid #0010f7;
}

.or-line {
  width: 45%;
  height: 1px;
  background-color: #eaeaea;
  margin-top: 8px;
}

.or-text {
  font-size: 10px;
  font-weight: 600;
}

/* Error Page */
.error-wrapper {
  width: 100%;
  height: 100vh;

  .error-content {
    width: 486px;

    @media (max-width: 767.99px) {
      width: 410px;
      padding: 20px;
    }

    @media (max-width: 576px) {
      width: 320px;
      padding: 20px;
    }

    h1 {
      font-weight: 600;
      font-size: 42px;

      @media (max-width: 767.99px) {
        font-size: 30px;
      }

      @media (max-width: 576px) {
        font-size: 28px;
      }
    }

    img {
      width: 100%;
    }
  }
}

.btn-back-to-homepage {
  width: 215px;
}

/*------------------------------------------------------------------
 18. Card
--------------------------------------------------------------------*/

.customized .card-body {
  border-bottom: 1px solid #f0eeee;
  border-left: 1px solid #f0eeee;
  border-right: 1px solid #f0eeee;
  border-radius: 0 0 5px 5px;
}

.customized .card:hover {
  box-shadow: 0 5px 10px 0 lightgrey;
}

.no-border-card .list-group {
  gap: 10px;
}

.simple-card .list-group {
  gap: 10px;
}

/*------------------------------------------------------------------
 19. Modal
--------------------------------------------------------------------*/
.btn-close {
  position: relative;
  top: -27px;
  right: -29px;
  background: #fff;
  border-radius: 50%;
  line-height: 1;
  opacity: 1;
  font-size: 15px;
  padding: 0;
  box-shadow: none;
  display: flex;
  justify-content: center;
  outline: none !important;
  --bs-btn-close-focus-shadow: none;

  i {
    color: var(--ot-primary-text);
    font-size: 18px;
    line-height: 0.9;
  }

  &:hover {
    opacity: 1;
  }

  &.btn-canvas {
    position: unset;
    top: unset;
    right: unset;
    background: transparent var(--bs-btn-close-bg) center / 1em auto no-repeat;
  }
}

.modal {
  transition: backdrop-filter ease-in 0.5s;
  background-color: RGBA(255, 255, 255, 0.3);
  backdrop-filter: blur(2px);
}

.modal-header .modal-title {
  font-size: 16px;
  font-weight: 600;
}

.modal-header-style {
  padding: 14px 22px;
  background-color: #000000;
}

.modal-content {
  border: none;
}

.information-modal .fa {
  font-size: 28px;
}

.information-modal .modal-footer {
  border-top: none;
}

.confirmation-modal .fa {
  font-size: 28px;
}

.confirmation-modal .modal-footer {
  border-top: none;
}

/*------------------------------------------------------------------
    20. Alert
--------------------------------------------------------------------*/
.alert-section {
  background: #fff;
  padding: 80px;
  background: #fcfcfc;
  margin-top: 8px;
}

.alert-section h1 {
  font-weight: 700;
  font-size: 32px;
  text-transform: uppercase;
  color: #120d26;
}

.alert-section p {
  font-weight: 400;
  font-size: 14px;
  color: #4b4b4b;
}

.close-icon {
  float: right;
}

.basic-success-alert {
  padding: 15px;
  background-color: #e9faf4;
  color: #29d697;

  font-weight: 400;
  font-size: 14px;
}

.basic-success-alert .success-icon {
  color: #29d697;
}

.basic-secondary-alert {
  padding: 15px;
  background-color: #eef0ff;
  color: #4d5ee5;

  font-weight: 400;
  font-size: 14px;
}

.basic-secondary-alert .secondary-icon {
  color: #4d5ee5;
}

.basic-warning-alert {
  padding: 15px;
  background-color: #fef9e5;
  color: #fdc400;

  font-weight: 400;
  font-size: 14px;
}

.basic-warning-alert .warning-icon {
  color: #fdc400;
}

.basic-danger-alert {
  padding: 15px;
  background-color: #fff0ed;
  color: #ff6a54;

  font-weight: 400;
  font-size: 14px;
}

.basic-danger-alert .danger-icon {
  color: #ff6a54;
}

.alert-success-describe h4,
.alert-success-describe p {
  color: #29d697;
}

.alert-secondary-describe h4,
.alert-secondary-describe p {
  color: #4d5ee5;
}

.alert-warning-describe h4,
.alert-warning-describe p {
  color: #fdc400;
}

.alert-danger-describe h4,
.alert-danger-describe p {
  color: #ff6a54;
}

/*------------------------------------------------------------------
    21. Color Template
--------------------------------------------------------------------*/
.color-title {
  background-color: transparent;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  filter: drop-shadow(0px 2px 8px rgba(0, 0, 0, 0.05));

  h4 {
    font-size: 24px;
  }
}

/* .color-background {
    width: 100%;
    height: 145px;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;

    &.bg-primary-dark {
        background: var(--blue-dark);
    }

    &.bg-primary-main {
        background: var(--blue-primary);
    }

    &.bg-primary-tin1 {
        background: var(--blue-tint-1);
    }

    &.bg-primary-tin2 {
        background: var(--blue-tint-2);
    }

    &.bg-secondary-dark {
        background: var(--cyan-dark);
    }

    &.bg-secondary-main {
        background: var(--cyan-main);
    }

    &.bg-secondary-tin1 {
        background: var(--cyan-tint-1);
    }

    &.bg-secondary-tin2 {
        background: var(--cyan-tint-2);
    }

    &.bg-success-dark {
        background: var(--success-dark);
    }

    &.bg-success-main {
        background: var(--success-main);
    }

    &.bg-success-tin1 {
        background: var(--success-tint-1);
    }

    &.bg-success-tin2 {
        background: var(--success-tint-2);
    }

    &.bg-danger-dark {
        background: var(--danger-dark);
    }

    &.bg-danger-main {
        background: var(--danger-main);
    }

    &.bg-danger-tin1 {
        background: var(--danger-tint-1);
    }

    &.bg-danger-tin2 {
        background: var(--danger-tint-2);
    }

    &.bg-info-dark {
        background: var(--info-dark);
    }

    &.bg-info-main {
        background: var(--info-main);
    }

    &.bg-info-tin1 {
        background: var(--info-tint-1);
    }

    &.bg-info-tin2 {
        background: var(--info-tint-2);
    }

    &.bg-warn-dark {
        background: var(--warn-dark);
    }

    &.bg-warn-main {
        background: var(--warn-main);
    }

    &.bg-warn-tin1 {
        background: var(--warn-tint-1);
    }

    &.bg-warn-tin2 {
        background: var(--warn-tint-2);
    }
} */

/* .color-name-bg {
    background: #f5f5f5;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;

    &.color-primary-main {
        color: var(--blue-primary);
    }
} */

.accent {
  width: 100px;
  height: 100px;
  border-radius: 4px;
  filter: drop-shadow(0px 2px 8px rgba(0, 0, 0, 0.05));
  position: relative;

  &.accent-light-green {
    background: var(--accent-light-green);
  }

  &.accent-light-purple {
    background: var(--accent-light-purple);
  }

  &.accent-light-blue {
    background: var(--accent-light-blue);
  }

  &.accent-light-orange {
    background: var(--accent-light-orange);
  }

  &.accent-green {
    background: var(--accent-green);
  }

  &.accent-blue {
    background: var(--accent-blue);
  }

  &.accent-purple {
    background: var(--accent-purple);
  }

  span {
    position: absolute;
    bottom: 10px;
    padding-left: 5px;
  }
}

.typo {
  width: 100px;
  height: 100px;
  border-radius: 4px;
  filter: drop-shadow(0px 2px 8px rgba(0, 0, 0, 0.05));
  position: relative;
}

.typo.typo-black-dark {
  background: var(--typo-black-dark);
}

.typo.typo-black-main {
  background: var(--typo-black-main);
}

.typo.typo-black-bold {
  background: var(--typo-black-bold);
}

.typo.typo-black-light {
  background: var(--typo-black-light);
}

.typo.typo-black-lighter {
  background: var(--typo-black-lighter);
}

.typo span {
  position: absolute;
  bottom: 10px;
  padding-left: 5px;
}

.bgwhite {
  width: 100px;
  height: 100px;
  border-radius: 4px;
  filter: drop-shadow(0px 2px 8px rgba(0, 0, 0, 0.05));
  position: relative;
}

.bgwhite.bgwhite-dark {
  background: var(--bgwhite-dark);
}

.bgwhite.bgwhite-main {
  background: var(--bgwhite-main);
}

.bgwhite.bgwhite-tin1 {
  background: var(--bgwhite-tin1);
}

.bgwhite.bgwhite-tin2 {
  background: var(--bgwhite-tin2);
}

.bgwhite.typo-black-lighter {
  background: var(--typo-black-lighter);
}

.bgwhite span {
  position: absolute;
  bottom: 10px;
  padding-left: 5px;
}

/*------------------------------------------------------------------
    22. Dropdown
--------------------------------------------------------------------*/
.dropdown-text-light {
  color: #6f767e;
}

.dropdown-text-dark {
  color: #1a1d1f;
}

.dropdown-text-red {
  color: #ff0022;
}

.dropdown-text-disable {
  color: #b2bec3;
}

.dropdown-text-blue {
  color: #5669ff;
}

.dropdown-content {
  margin-top: 24px;
  margin-left: 32px;
}

.dropdown-section {
  padding: 80px;
  background: #fcfcfc;
  margin-top: 8px;
}

.dropdown-section h1 {
  font-weight: 700;
  font-size: 32px;
  text-transform: uppercase;
  color: #120d26;
}

.dropdown-section p {
  font-weight: 400;
  font-size: 14px;
  color: #4b4b4b;
}

.button-title {
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  color: #120d26;
}

.dropdown-with-down-arrow,
.dropdown2-with-down-arrow {
  margin-top: 12px;
  background-color: #fcfcfc;
  font-weight: 500;
  font-size: 16px;
  padding: 8px 16px;
  letter-spacing: 0.02em;
  color: #1a1d1f;
  border: 2px solid #f0eeee;
  border-radius: 4px;
}

.dropdown-with-down-arrow i,
.dropdown2-with-down-arrow i {
  margin-left: 12px;
  font-weight: bold;
}

.dropdown-with-down-arrow:hover,
.dropdown2-with-down-arrow:hover {
  border: 2px solid #5669ff;
  color: #5669ff;
}

.dropdown-with-down-arrow:hover i,
.dropdown2-with-down-arrow:hover i {
  color: #5669ff;
}

.dropdown-with-down-arrow:focus,
.dropdown2-with-down-arrow:focus {
  border: 2px solid #4d5ee5;
  color: #066ecf;
}

.dropdown-with-down-arrow:focus i,
.dropdown2-with-down-arrow:focus i {
  color: #066ecf;
}

.dropdwon-btn-section {
  margin-top: 40px;
}

.dropdown-with-dots {
  margin-top: 12px;
}

button {
  border: none;
  outline: none;
}

.dropdown-menu {
  margin-top: 10px !important;
}

#dropdown {
  background-color: #fcfcfc;
  border: 2px solid #f0eeee;
  border-radius: 5px 0 0 5px;
  font-weight: 500;
  font-size: 16px;
  padding: 8px 16px;
  letter-spacing: 0.02em;
  color: #1a1d1f;
}

#dropdown:hover {
  border: 2px solid #5669ff;
  color: #5669ff;
}

#dropdown:focus {
  border: 2px solid #4d5ee5;
  color: #066ecf;
}

#three-dots {
  background-color: #fcfcfc;
  border: 2px solid #f0eeee;
  border-radius: 0 5px 5px 0;
  padding: 8px;
  letter-spacing: 0.02em;
  color: #1a1d1f;
}

#three-dots:hover {
  border: 2px solid #5669ff;
  color: #5669ff;
}

#three-dots:focus {
  border: 2px solid #4d5ee5;
  color: #066ecf;
}

.dropdown-items,
.second-item,
.third-item {
  position: relative;
  width: 191px;
  background: #ffffff;
  border: 1px solid #f0eeee;
  border-radius: 4px;
}

.dropdown-items ul,
.second-item ul,
.third-item ul {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 16px;
  padding-right: 0;
  gap: 16px;
}

.dropdown-items ul .text-secondary>i,
.second-item ul .text-secondary>i,
.third-item ul .text-secondary>i {
  padding-right: 16px;
}

.dropdown-items ul li,
.second-item ul li,
.third-item ul li {
  font-weight: 400;
  font-size: 14px;
  padding: 8px;
  padding-right: 24px;
}

.dropdown-items ul .dropdown-text-dark>i,
.second-item ul .dropdown-text-dark>i,
.third-item ul .dropdown-text-dark>i {
  padding-left: 16px;
}

.second-item {
  display: block;
  width: 180px;
  position: absolute;
  opacity: 0;
  visibility: hidden;
  left: 100%;
  margin-top: -40px;
  transition: all ease-in-out 0.4s;
}

.third-item {
  display: none;
  width: 180px;
  position: absolute;
  left: 112%;
  margin-top: -66px;
}

ul li.dropdown-text-dark:hover .second-item {
  visibility: visible;
  opacity: 1;
  transition-delay: 0s;
  margin-left: 10px;
}

.dropdown-search-checkbox {
  margin-top: 12px;
  background-color: #fcfcfc;
  border: 2px solid #f0eeee;
  border-radius: 50px;
  font-weight: 500;
  font-size: 16px;
  letter-spacing: 0.02em;
  padding: 8px 16px;
  color: #5669ff;
}

.search-container {
  margin-top: 12px;
  width: 286px;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.search-input-checkbox input {
  width: 20px !important;
  height: 20px;
  border: 1px solid #eaeaea;
  border-radius: 4px;
}

.search-input-checkbox label {
  color: #6f767e;
  margin-left: 12px;
}

.search-input-checkbox ul li {
  display: flex;
}

.search-input {
  position: relative;
  text-align: center;
  padding: 15px 16px 15px 16px;
  border-bottom: 2px solid #f0eeee;
}

.search-input input {
  padding: 12px;
  width: 100%;
  border: 2px solid #f0eeee;
  border-radius: 50px;
  background: #fafafa;
  outline: none;
}

.search-input ::-moz-placeholder {
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.search-input ::placeholder {
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.search-input i {
  position: absolute;
  top: 29px;
  right: 27px;
  padding: 5px;
  background-color: #fafafa;
  color: #6f767e;
}

.search-items {
  padding: 17px 25px;
}

.search-items ul {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 16px;

  font-weight: 400;
  font-size: 14px;
}

.search-items ul li {
  color: #6f767e;
}

.search-input ul {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 8px 16px;
  gap: 16px;
}

.search-input ul li {
  font-weight: 400;
  font-size: 14px;
  color: #6f767e;
}

.search-input ul li input {
  width: auto;
}

.search-input ul li ::-moz-placeholder {
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.search-input ul li ::placeholder {
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.search-container .btn-items {
  padding: 5px 17px 25px;
  text-align: end;
}

.search-container .btn-items .btn {
  font-weight: 400;
  font-size: 14px;
}

.search-container .btn-items .btn.clear {
  color: #6f767e;
}

.input-default input,
.input-date input {
  padding: 16px;
  width: 336px;
  height: 40px;
  border: 2px solid #f0eeee;
  border-radius: 4px;
  outline: none;
}

.input-default ::-moz-placeholder,
.input-date ::-moz-placeholder {
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-default ::placeholder,
.input-date ::placeholder {
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-pre-post {
  position: relative;
  margin-top: 16px;
  width: 296px;
  height: 40px;
}

.input-pre-post input {
  padding: 16px 45px;
  width: 296px;
  height: 40px;
  border: 2px solid #f0eeee;
  border-radius: 4px;
  outline: none;
}

.input-pre-post ::-moz-placeholder {
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-pre-post ::placeholder {
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-pre-post i {
  position: absolute;
  top: 16px;
  color: #6f767e;
}

.input-pre-post img {
  position: absolute;
  top: 2px;
  right: 1px;
  padding: 15px;
  width: 47px;
  background-color: #f7f7f7;
  border-left: 2px solid #f0eeee;
  border-radius: 0 5px 5px 0;
}

.input-https-post {
  position: relative;
  margin-top: 16px;
  width: 296px;
  height: 40px;
}

.input-https-post input {
  padding: 16px 43px 16px 110px;
  width: 296px;
  height: 40px;
  border: 2px solid #f0eeee;
  border-radius: 4px;
  outline: none;
}

.input-https-post ::-moz-placeholder {
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-https-post ::placeholder {
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-https-post i {
  position: absolute;
  top: 16px;
  color: #6f767e;
}

.input-https-post img {
  position: absolute;
  top: 2px;
  right: 1px;
  padding: 15px;
  width: 47px;
  background-color: #f7f7f7;
  border-left: 2px solid #f0eeee;
  border-radius: 0 5px 5px 0;
}

.input-https-post .https {
  position: absolute;
  color: #1a1d1f;
  top: 2px;
  left: 1.5px;
  background-color: #f7f7f7;
  padding: 12px;
  border-right: 2px solid #f0eeee;
  border-radius: 5px 0 0 5px;
}

.input-https-post2 {
  position: relative;
  margin-top: 16px;
  width: 296px;
  height: 40px;
}

.input-https-post2 input {
  padding: 16px 43px 16px 86px;
  width: 296px;
  height: 40px;
  border: 2px solid #f0eeee;
  border-radius: 4px;
  outline: none;
}

.input-https-post2 ::-moz-placeholder {
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-https-post2 ::placeholder {
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-https-post2 i {
  position: absolute;
  top: 16px;
  color: #6f767e;
}

.input-https-post2 .https {
  position: absolute;
  color: #1a1d1f;
  top: 2px;
  left: 1.5px;
  background-color: #f7f7f7;
  padding: 12px;
  border-right: 2px solid #f0eeee;
  border-radius: 5px 0 0 5px;
}

.text-area .text-count {
  font-size: 10px;
  font-weight: 500;
  text-align: end;
  color: #b2bec3;
}

.Input-search-tab {
  position: relative;
  margin-top: 16px;
  width: 273px;
  height: 40px;
}

.Input-search-tab input {
  padding: 16px 43px 16px 16px;
  width: 100%;
  height: 100%;
  border: 2px solid #f0eeee;
  border-radius: 4px;
  outline: none;
}

.Input-search-tab ::-moz-placeholder {
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.Input-search-tab ::placeholder {
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.Input-search-tab i {
  position: absolute;
  top: 0;
  color: #6f767e;
  border-left: 2px solid #f0eeee;
  padding: 16px;
}

.Input-search-tab .search {
  position: absolute;
  color: #ffffff;
  top: 0;
  right: 0;
  background-color: #5669ff;
  padding: 13px;
  border-radius: 0 5px 5px 0;
}

.Input-search-tab.search-color>input {
  width: 289px;
}

.Input-search-tab.search-color>i {
  right: -15px;
  color: #fafafa;
  background-color: #5669ff;
  border-radius: 0 5px 5px 0;
}

.Input-search-tab .search-tab1 {
  width: 237px;
}

.Input-search-tab.microphone>input {
  padding: 16px 110px 16px 16px;
}

.input-date {
  margin-top: 16px;
}

.input-date input {
  width: 286px;
}

.Range-date {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  width: 286px;
  height: 40px;
  flex-direction: row;
  border: 2px solid #f0eeee;
  border-radius: 4px;
  margin-top: 16px;
  padding: 10px 16px;
  gap: 5px;
}

.Range-date input {
  border: none;
  outline: none;
}

.time-field {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  flex-direction: row;
  width: 386px;
  gap: 5px;
  margin-top: 16px;
}

.time-field .input-time {
  border: 2px solid #f0eeee;
  border-radius: 4px;
  width: 107px;
  height: 50px;
}

.time-field .input-time select {
  font-weight: 500;
  font-size: 16px;
  color: #1a1d1f;
  border: none;
  outline: none;
  padding: 11px 16px;
}

.time-field .select-time input {
  border: 2px solid #f0eeee;
  border-radius: 4px;
  width: 286px;
  height: 40px;
  outline: none;
  padding: 16px;
}

.input-group-start-end-time {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  justify-content: flex-end;
  flex-direction: row;
  gap: 10px;
}

.input-group-start-end-time .input-start-end-time {
  border: 2px solid #f0eeee;
  border-radius: 4px;
  width: 169px;
  height: 44px;
  padding: 12px 16px;
  color: #6f767e;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.input-group-start-end-time .input-start-end-time span {
  font-weight: 400;
  font-size: 14px;
}

option:hover {
  color: #5669ff;
}

.input-time-select {
  display: flex;
  justify-content: end;
  align-items: center;
  margin-top: 16px;

  .input-start-end-time-select {
    border: 2px solid #f0eeee;
    border-radius: 4px;
    width: 178px;
    height: 60px;
    padding: 12px 16px;
    color: #6f767e;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;

    p {
      font-weight: 500;
      font-size: 12px;
      color: #0010f7;
    }

    h6 {
      font-weight: 700;
      font-size: 16px;
      color: #1a1d1f;
    }
  }
}

/*------------------------------------------------------------------
    23. Badges
--------------------------------------------------------------------*/
.badge-success,
.badge-light-success,
.badge-primary,
.badge-light-primary,
.badge-warning,
.badge-light-warning,
.badge-danger,
.badge-light-danger {
  padding: 3px 6px;
  background: var(--ot-bg-badge-success);
  border-radius: 4px;

  font-size: 12px;
  font-weight: 700;
  line-height: 15px;
  color: var(--ot-text-badge-success);
}

.badge-success.ot-badge-circle,
.ot-badge-circle.badge-light-success,
.ot-badge-circle.badge-primary,
.ot-badge-circle.badge-light-primary,
.ot-badge-circle.badge-warning,
.ot-badge-circle.badge-light-warning,
.ot-badge-circle.badge-danger,
.ot-badge-circle.badge-light-danger {
  width: 30px;
  height: 30px;
  border-radius: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.badge-danger,
.badge-light-danger {
  background: var(--ot-bg-badge-danger);
  color: var(--ot-text-badge-danger);
}

.badge-warning,
.badge-light-warning {
  background: var(--ot-bg-badge-warning);
  color: var(--ot-text-badge-warning);
}

.badge-primary,
.badge-light-primary {
  background: var(--ot-bg-badge-primary);
  color: var(--ot-text-badge-primary);
}

.badge-light-success {
  background: var(--ot-bg-badge-light-success);
  color: var(--ot-text-badge-light-success);
}

.badge-light-danger {
  background: var(--ot-bg-badge-light-danger);
  color: var(--ot-text-badge-light-danger);
}

.badge-light-warning {
  background: var(--ot-bg-badge-light-warning);
  color: var(--ot-text-badge-light-warning);
}

.badge-light-primary {
  background: var(--ot-bg-badge-light-primary);
  color: var(--ot-text-badge-light-primary);
}

/*noinspection CssUnknownTarget*/
.ot-carousel .carousel-control-prev-icon {
  background-image: url(/assets/images/icons/slider-left-arrow.svg?d5d5a4bced12dafe92e512ca2dbd0bbd);
}

/*noinspection CssUnknownTarget*/
.ot-carousel .carousel-control-next-icon {
  background-image: url(/assets/images/icons/slider-right-arrow.svg?ab7c6bd75f3b76fc6eae1c8f39bb1334);
}

.ot-carousel .carousel-indicators button {
  background-color: var(--primary-color);
}

/* custom tooltip */
.custom-tooltip {
  --bs-tooltip-bg: var(--primary-color);
}

/*------------------------------------------------------------------
    24. Pagination
--------------------------------------------------------------------*/
.ot-pagination {
  margin-top: 40px;

  .pagination {
    margin: 0;

    .page-item {
      margin-right: 10px;

      &:last-child {
        margin-right: 0;
      }

      &:first-child .page-link {
        font-size: 26px;
        line-height: 11px;
        font-weight: 400;
        padding: 7px 8px;
      }

      &:last-child .page-link {
        font-size: 26px;
        line-height: 11px;
        font-weight: 400;
        padding: 7px 8px;
      }

      .page-link {
        font-size: 14px;
        line-height: 16px;
        color: var(--ot-primary-text);
        border: 1px solid var(--ot-primary-border);
        border-radius: 4px;
        background-color: #fff;
        min-width: 30px;
        height: 30px;
        font-weight: 600;

        i {
          font-size: 18px;
          line-height: 1;
        }

        &:hover {
          background-color: transparent;
          color: var(--primary-color);
        }

        &:focus {
          box-shadow: none;
          background-color: transparent;
        }
      }

      &.active {
        .page-link {
          background: var(--ot-primary-btn);
          color: #fff;
          border: 1px solid var(--ot-primary-btn);
        }
      }
    }
  }

  .small {
    font-size: 16px;
    text-transform: capitalize;
    font-weight: 500;

    &:last-child span {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 0;
      color: var(--primary-color);
    }
  }
}

@media (max-width: 992px) {
  .ot-pagination nav {
    overflow-x: scroll;
  }
}

/*------------------------------------------------------------------
   26. Custom Input field
--------------------------------------------------------------------*/
.ot-input {
  background-color: var(--ot-bg-secondary) !important;
  border: 1px solid var(--ot-primary-border) !important;
  outline: none !important;
  color: var(--ot-primary-text) !important;
  font-size: 14px;
  padding-left: 16px;

  &:focus-visible {
    box-shadow: 0 0 10px rgba(10, 175, 255, 0.35);
    border: 1px solid #5669ff !important;
    outline: none;
  }

  &::placeholder,
  &::-moz-placeholder {
    font-weight: 400;
    font-size: 14px;
    color: #b2bec3;
  }
}

/*------------------------------------------------------------------
  27. Input Check Radio
--------------------------------------------------------------------*/
.input-check-radio {
  padding: 8px 0;

  .form-check {
    display: flex;
    align-items: self-end;

    .form-check-input {
      width: 20px;
      height: 20px;
      border-radius: 4px;
      border: 1px solid #eaeaea;
      background-color: #ffffff;
      margin-right: 4px;

      &:focus {
        box-shadow: none;
      }

      &:checked {
        background-repeat: no-repeat;
        background-position: center;
        border: none;
      }
    }

    .form-check-label {
      margin-left: 8px;
    }
  }
}

.form-check {
  display: flex;
  align-items: center;
  gap: 7px;
}

/*------------------------------------------------------------------
    28. Email Template
--------------------------------------------------------------------*/
.email-template {
  text-align: center;
  padding: 56px 60px;
  background: white;
  border-radius: 4px;
  max-width: 600px;

  @media (max-width: 576px) {
    padding: 26px 30px;
  }

  .template-heading {
    h1 {
      font-weight: 600;
      font-size: 24px;
      line-height: 34px;
      margin-top: 20px;

      @media (max-width: 576px) {
        font-size: 20px;
        padding: 0 10px;
      }
    }

    p {
      font-size: 16px;
      line-height: 24px;
      color: #6f767e;
      margin-top: 20px;

      @media (max-width: 576px) {
        font-size: 16px;
        padding: 0 8px;
      }
    }

    .color-black {
      color: #1a1d1f;
    }
  }

  .template-body {
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    color: #6f767e;
    padding: 14px;

    @media (max-width: 576px) {
      font-size: 14px;
      line-height: 24px;
    }

    .content-part {
      text-align: left;
      margin-bottom: 28px;

      p {
        @media (max-width: 576px) {
          padding: 0;
        }

        a {
          color: var(--primary-color);
        }
      }

      h5 {
        color: #1a1d1f;
        margin-top: 28px;
        padding: 0;

        @media (max-width: 576px) {
          padding: 0;
        }
      }
    }

    .content-details {
      p {
        padding: 0 14px;
        margin-bottom: 28px;

        .link {
          color: var(--primary-color);
        }
      }
    }

    .ot-primary-text {
      font-weight: 600;
      font-size: 16px;
      line-height: 24px;
      color: var(--primary-color);
      margin-top: 26px;
    }

    h4 {
      font-weight: 600;
      font-size: 16px;
      color: #29d697;
    }

    h5 {
      padding: 0 14px;

      @media (max-width: 576px) {
        padding: 0;
      }
    }
  }

  .template-button-group {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-left: 14px;
    gap: 10px;

    @media (max-width: 576px) {
      flex-direction: column;
      padding: 0;
    }

    .template-btn {
      padding: 9px 2px;
      border-radius: 4px;
      background: var(--ot-primary-btn);

      @media (max-width: 576px) {
        width: 100%;
      }

      span {
        padding: 10px 16px;
        font-weight: 600;
        color: white;
        background: var(--ot-primary-btn);

        &:hover {
          outline: none;
          border: none;
          color: var(--primary-color);
          border-radius: 4px;
          background: white;
        }
      }
    }
  }

  .template-btn-container {
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .template-btn {
      padding: 9px 2px;
      border-radius: 4px;
      background: var(--ot-primary-btn);

      span {
        padding: 10px 16px;
        font-weight: 600;
        color: white;
        background: var(--ot-primary-btn);

        &:hover {
          outline: none;
          border: none;
          color: var(--primary-color);
          border-radius: 4px;
          background: white;
        }
      }
    }
  }

  .template-footer {
    font-weight: 500;
    font-size: 12px;
    line-height: 15px;
    color: #6f767e;
    border-top: 1px solid #dfe6e9;
    margin-top: 26px;

    @media (max-width: 576px) {
      line-height: 8px;
    }

    p {
      >a {
        color: var(--primary-color);
      }
    }

    .social-media-button {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 26px;
      gap: 8px;

      a {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 8.5px;
        border-radius: 50%;
        background: var(--ot-primary-btn);

        &:hover {
          background: var(--ot-primary-btn);
        }
      }
    }

    .template-footer-image {
      margin-top: 28px;
      margin-bottom: 8px;
    }
  }
}

.map-label {
  background: #fff !important;
  padding: 10px;
  color: #222;
  border-radius: 10px;
  box-shadow: 5px 4px 5px 0 rgba(0, 0, 0, 0.39);
  -webkit-box-shadow: 5px 4px 5px 0 rgba(0, 0, 0, 0.39);
  -moz-box-shadow: 5px 4px 5px 0 rgba(0, 0, 0, 0.39);
  z-index: 999;
}

.user-img {
  height: 40px;
  width: 42px;
  border-radius: 6px;
  overflow: hidden;
}

.img-cover {
  width: 100%;
  height: 100%;
  overflow: hidden;
  -o-object-fit: cover;
  object-fit: cover;
  -o-object-position: 50% 50%;
  object-position: 50% 50%;
}

.table-toolbar {
  /*noinspection CssUnknownTarget*/

  .form-select {
    background-image: url("/assets/images/icons/down-arrow.svg");
  }
}

.profile-table-content {
  .table-toolbar {
    /*noinspection CssUnknownTarget*/

    .form-select {
      background-image: url("/assets/images/icons/down-arrow.svg");
    }
  }

  .table {
    .check-box {
      /*noinspection CssUnknownTarget*/

      .form-check-input:checked {
        background: url("/assets/images/ok.svg"), var(--ot-primary-btn);
        background-repeat: no-repeat;
        background-position: center;
      }
    }
  }
}

.input-check-radio {
  .form-check {
    /*noinspection CssUnknownTarget*/

    .form-check-input:checked {
      background: url("/assets/images/ok.svg"), var(--ot-primary-btn);
      background-repeat: no-repeat;
      background-position: center;
    }
  }
}

/*------------------------------------------------------------------
  29. Select 2
--------------------------------------------------------------------*/
.modal .ot-form-control {
  color: #797979;
}

.dry-color {
  color: #6e6b7b;
}

.modal .select2-container {
  display: block;
}

.primary-color {
  color: #625f5f;
  font-size: 14px;
}

.new-profile-content {
  @media (max-width: 992px) {
    display: none;
  }

  .profile-menu {
    .nav-pills {
      .nav-item {
        /* width: 48%; */

        &:nth-child(odd) {
          padding-right: 13px;
          margin-right: 5px;
        }
      }
    }

    .nav {
      &.nav-pills {
        li {
          a {
            color: var(--ot-primary-text);
            padding: 8px 12px;
            border-radius: 4px;
            font-weight: 600;
            font-size: 14px;
            background: var(--primary-bg);
            margin-bottom: 5px;

            &.active {
              color: #fff;
              background: var(--ot-primary-btn);

              svg {
                color: #fff;
              }
            }
          }
        }
      }
    }
  }
}

#toast-container>.toast-success {
  background-color: #a5dc86 !important;
  -webkit-box-shadow: 0 0 12px #a5dc86;
  box-shadow: 0 0 12px #a5dc86;
}

#toast-container>.toast-error {
  background-color: #f27474 !important;
  -webkit-box-shadow: 0 0 12px #f27474;
  box-shadow: 0 0 12px #f27474;
}

#toast-container>.toast-warning {
  background-color: #e7e622 !important;
  -webkit-box-shadow: 0 0 12px #e7e622;
  box-shadow: 0 0 12px #e7e622;
}

#toast-container>.toast-info {
  background-color: #3fc3ee !important;
  -webkit-box-shadow: 0 0 12px #3fc3ee;
  box-shadow: 0 0 12px #3fc3ee;
}

#toast-container>div {
  width: 351px !important;
  height: 80px;
  font-size: 1em;
  opacity: 1 !important;
}

input.select2-search-field:focus-visible {
  outline: none;
}

input[type="file"] {
  padding-left: 10px !important;
}

input[type="file"]::file-selector-button {
  background: #f7fafc;
  color: var(--ot-primary-text);
  transition: 1s;
}

input[type="file"]::-ms-browse:hover {
  background: #f7fafc;
  color: var(--ot-primary-text);
}

input[type="file"]::-webkit-file-upload-button:hover {
  background: #f7fafc !important;
  color: var(--ot-primary-text);
}

input[type="file"]::file-selector-button:hover {
  background: #f7fafc !important;
  color: var(--ot-primary-text);
}

input[type="file"]::-webkit-file-upload-button {
  background: #f7fafc;
  color: var(--ot-primary-text);
}

.staff-profile-image-small {
  height: 32px;
  width: 32px;
  border-radius: 50%;
}

.modal-content {
  background-color: var(--ot-bg-secondary) !important;
  color: var(--ot-primary-text) !important;
}

.file-up-btn {
  margin: 4px;
  padding: 7px 12px;
  height: 34px;
}

.select2-dropdown,
input.select2-search-field:focus-visible,
.select2-container--default .select2-search--dropdown .select2-search-field:focus-visible,
.select2-container--default .select2-selection--multiple:focus-visible,
input.daterange-table-filter,
.select2-search-field:focus-visible {
  background-color: var(--ot-bg-secondary) !important;
  border: 1px solid var(--ot-primary-border) !important;
  color: var(--ot-primary-text) !important;
}

.select2-container--default .select2-search--dropdown .select2-search-field {
  border: 1px solid var(--ot-primary-border) !important;
  color: var(--ot-primary-text) !important;
  background-color: var(--ot-bg-secondary) !important;
}

.select2-container--default .select2-search--dropdown .select2-search-field:focus-visible {
  box-shadow: none;
  border: 1px solid var(--ot-primary-text) !important;
}

.select2-container--default .select2-search--dropdown .select2-search-field:focus-visible {
  color: var(--ot-primary-text) !important;
}

span.username,
span.description {
  color: var(--ot-primary-text);
}

/*------------------------------------------------------------------
 30. Task View
--------------------------------------------------------------------*/

.top-lead-menu {
  margin-left: -15px;
  margin-right: -15px;
  margin-top: -15px;
}

.horizontal-scrollable-tabs {
  min-height: 50px;
}

.horizontal-scrollable-tabs .scroller {
  background: 0 0;
  font-weight: 600;
  cursor: pointer;
  color: #50637c;
  border-bottom: 1px solid #f0f0f0;
  border-top: 1px solid #f0f0f0;
  padding: 9px 10px;
}

.horizontal-scrollable-tabs .arrow-left {
  float: left;
}

.horizontal-scrollable-tabs .arrow-right {
  float: right;
}

.horizontal-scrollable-tabs .horizontal-tabs .nav-tabs-horizontal {
  overflow-x: auto;
  overflow-y: hidden;
  display: -webkit-box;
  display: -moz-box;
}

.top-lead-menu .nav-tabs {
  border-top: 0;
}

.info-box-card {
  background: #f7fafc;
  filter: drop-shadow(0px 2px 8px rgba(0, 0, 0, 0.07));
}

.nav-tabs {
  padding-bottom: 0;
  margin-bottom: 0;
  background: 0 0;
  border-radius: 1px;
  padding-left: 0;
  padding-right: 0;
  border-bottom: none;
}

.nav-tabs>li.active>a,
.nav-tabs>li.active>a:focus,
.nav-tabs>li.active>a:hover,
.navbar-pills.nav-tabs>li>a:focus,
.navbar-pills.nav-tabs>li>a:hover {
  border-bottom: 1px solid #5669ff;
}

.nav-tabs>li.active>a,
.nav-tabs>li.active>a:focus,
.nav-tabs>li.active>a:hover,
.nav-tabs>li>a:focus,
.nav-tabs>li>a:hover {
  border: 0;
  border-radius: 0;
  border-bottom: 1px solid #5669ff;
  background: 0 0;
  color: #5669ff;
  margin-bottom: 1px;
}

.project-tabs>li>a {
  color: #6f767e;
}

.nav>li>a {
  position: relative;
  display: block;
  padding: 10px 0;
  font-size: 14px;
  font-weight: 500;

  margin: 0 13px;
}

.mb-20 {
  margin-bottom: 20px;
}

.badge-main {
  background-color: #7f58fe;
  padding: 4px 10px;
  font-size: 12px;
  font-weight: 600;
  line-height: 15px;
}

.colored-toast.swal2-icon-success {
  background-color: #a5dc86 !important;
}

.colored-toast.swal2-icon-error {
  background-color: #f27474 !important;
}

.colored-toast.swal2-icon-warning {
  background-color: #e7e622 !important;
}

.colored-toast.swal2-icon-info {
  background-color: #3fc3ee !important;
}

.colored-toast.swal2-icon-question {
  background-color: #87adbd !important;
}

.colored-toast .swal2-title {
  color: white;
}

.colored-toast .swal2-close {
  color: white;
}

.colored-toast .swal2-html-container {
  color: white;
}

.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #ccc;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  width: 45px;
}

input:checked+.slider {
  background: var(--ot-primary-btn);
}

.slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 4px;
  bottom: 3px;
  background-color: white;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

input:focus+.slider {
  box-shadow: 0 0 1px #4466f2;
}

input:checked+.slider:before {
  -ms-transform: translateX(18px);
  transform: translateX(18px);
}

.slider.round {
  border-radius: 30px;
  height: 25px;
}

.slider.round:before {
  border-radius: 50%;
}

.btn-odd-row {
  background-color: #f7fafc;
  padding-top: 10px;
}

.btn-even-row {
  padding-top: 10px;
}

.mt-30 {
  margin-top: 30px;
}

.staff-profile-image-small {
  height: 32px;
  width: 32px;
  border-radius: 50%;
}

.font-size-13 {
  font-size: 13px;
}

.font-size-14 {
  font-size: 14px;
}

.font-size-16 {
  font-size: 16px !important;
}

.c-none {
  display: none;
}

table.dataTable tbody th,
table.dataTable tbody td {
  font-size: 13px !important;
  line-height: 1;
}

.sticky-note {
  margin-bottom: 30px;
  background: #ff8;
  padding: 20px;
  padding-top: 10px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.25);
  -webkit-transition: transform 150ms cubic-bezier(0.165, 0.84, 0.44, 1);
  transition: transform 150ms cubic-bezier(0.165, 0.84, 0.44, 1);
  z-index: 1;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  text-align: justify;
}

.sticky-note:hover {
  cursor: move;
  transform: rotate(0deg);
}

.sticky-note>* {
  z-index: 1;
}

.sticky-note::after {
  content: "";
  position: absolute;
  z-index: 0;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  opacity: 0;
  -webkit-transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
  transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.sticky-note:hover::after {
  opacity: 1;
}

.cursor-pointer {
  cursor: pointer !important;
}

.page-btn {
  background: #ddd;
  color: #2c303a;
  height: 35px;
  border-radius: 2px;
  padding: 0 10px;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  transition: 0.3s ease;
  margin: 0 2px;
  border: 0;
  outline: none !important;
}

.page-btn:first-of-type {
  margin-left: 0;
}

.page-btn:last-of-type {
  margin-right: 0;
}

.page-btn:not([class$="-page"]) {
  width: 35px;
}

.pagination-buttons {
  display: flex;
  align-items: center;
}

.page-btn[class*="-page"] {
  background: #ccc;
  font-size: 0.6em;
  font-weight: 700;
}

.page-btn.active {
  background: linear-gradient(90deg, #7f58fe 0%, #7f58fe 100%);
  color: #fff;
}

.page-btn:hover {
  background: linear-gradient(90deg, #7f58fe 0%, #7f58fe 100%);
  color: #fff;
}

.page-btn[disabled],
.page-btn[disabled]:hover {
  opacity: 0.3;
  color: #000;
  background: #ccc;
  cursor: not-allowed;
}

.custom-action-right {
  float: right;
  right: 0 !important;
  left: auto !important;
}

/*------------------------------------------------------------------
  31. Rating Widget
--------------------------------------------------------------------*/

.rating {
  border: none;
  float: left;

  >input {
    display: none;
  }

  >label {
    color: #ddd;
    float: right;

    &:before {
      margin: 5px;
      font-size: 1.25em;
      font-family: "Font Awesome 5 Free";
      display: inline-block;
      content: "\f005";
    }
  }

  >.half {
    &:before {
      content: "\f089";
      position: absolute;
    }
  }

  >input:checked~label,
  &:not(:checked)>label:hover,
  &:not(:checked)>label:hover~label,
  i.fa-star,
  i.fa-star-half-alt {
    color: #ffd700;
  }

  >input:checked+label:hover,
  >input:checked~label:hover,
  >label:hover~input:checked~label,
  >input:checked~label:hover~label {
    color: #ffed85;
  }
}

.ot-form-control {
  width: 100%;
  height: 40px;
  border: 1px solid #f0eeee;
  border-radius: 4px;
  outline: none;
  color: var(--ot-primary-text);
  font-weight: 400;
  font-size: 14px;
  line-height: 35px;
}

.ot-form-control:focus-visible,
.ot-form-control:focus,
.form-select:focus-visible {
  box-shadow: 0 0 10px rgb(10 175 255 / 35%) !important;
  border: 1px solid var(--ot-primary-btn) !important;
  outline: none;
}

.ot-form-control-date {
  padding: 8px 8px 8px 16px;
  color: #797979;
}

/*------------------------------------------------------------------
  32. Slect 2
--------------------------------------------------------------------*/

.select2-container .select2-selection--single .select2-selection-rendered {
  padding-left: 16px;
  color: #797979;
  line-height: 40px;
  padding-right: 40px;
}

.select2-container .select2-selection--multiple {
  min-height: 40px;
  border: 1px solid var(--ot-primary-border);
  background-color: var(--ot-bg-secondary) !important;
}

.select2-container--default.select2-container--focus .select2-selection--multiple {
  border: 1px solid var(--ot-primary-border);
}

.select2-container .select2-search--inline .select2-search-field {
  margin-top: 15px;
  padding-left: 16px;
  border: none !important;
}

.select2-container--default .select2-selection--multiple .select2-selection--choice {
  margin-top: 0;
  background-color: var(--ot-bg-progress);
  color: var(--ot-primary-text);
}

.select2-container--default .select2-selection--multiple .select2-selection-rendered {
  margin-top: 5px;
}

.select2-container--open {
  z-index: 9999999;
}

/* end */
input[type="range"] {
  height: 40px;
}

/*------------------------------------------------------------------
  33. Custom Image Uploader Widget
--------------------------------------------------------------------*/
.custom-image-upload-wrapper,
.custom-image-upload-wrapper .image-area {
  width: 145px;
  height: 145px;
  border-radius: 0.25rem;
  border: 1px solid #ddd;
}

.custom-image-upload-wrapper {
  position: relative;
  background-color: var(--base-color);
}

.custom-image-upload-wrapper .image-area img {
  width: auto !important;
  max-height: 100%;
  border-radius: 0.25rem;
}

.custom-image-upload-wrapper .input-area {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  opacity: 0;
  padding: 5px 0;
  background-color: rgba(0, 0, 0, 0.4);
  border-bottom-left-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
  transition: all 0.25s ease-in-out;
}

.custom-image-upload-wrapper .input-area #upload-label {
  width: 100%;
  font-size: 90%;
  cursor: pointer;
  margin-bottom: 0;
  text-align: center;
  color: #fff !important;
}

.custom-image-upload-wrapper .input-area:hover {
  opacity: 1;
}

.form-label {
  color: var(--ot-primary-text);
  font-weight: 500;
  font-size: 14px;
  line-height: 16px;
  margin-bottom: 8px;
}

.card-body {
  padding: 20px;
}

.c-white-box {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 10px 15px rgb(126 194 219 / 30%);
}

.tooltip {
  z-index: 21;
}

textarea.form-control {
  width: 100%;
  border: 1px solid #f0eeee;
  border-radius: 4px;
  outline: none;
  color: var(--ot-primary-text);
  font-weight: 400;
  font-size: 14px;
  line-height: 28px;
}

.select2-container {
  margin-bottom: 0 !important;
}

.custom-chart {
  position: relative;
  height: 400px;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

.custom-chart .card-body {
  padding: 0;
  padding-top: 12px;
}

.custom-chart table thead th {
  font-weight: 300 !important;
  font-size: 14px;
  line-height: 16px;
}

.custom-chart table tbody td {
  font-weight: 300 !important;
  font-size: 12px;

  padding: 16px;
}

.dashboard-card-icon img {
  max-width: 24px;
  max-height: 24px;
}

.dashboard-card-icon i {
  font-size: 24px;
}

/*------------------------------------------------------------------
   34. icon
--------------------------------------------------------------------*/
.circle-violet {
  background: rgba(194, 183, 252, 0.1);
}

.circle-violet i {
  color: #6c63ffc9;
}

.circle-brown {
  background: rgba(254, 179, 145, 0.1);
}

.circle-brown i {
  color: #f4a460c4;
}

.circle-success {
  background: rgba(123, 183, 105, 0.1);
}

.circle-success i {
  color: #7ab668;
}

.circle-primary {
  background: rgba(0, 160, 255, 0.1);
}

.circle-primary i {
  color: #00a0ff;
}

.circle-warning {
  background: rgba(255, 193, 7, 0.1);
}

.circle-warning i {
  color: #ffc107;
}

.circle-lightseagreen {
  background: rgba(0, 255, 255, 0.1);
}

.circle-lightseagreen i {
  color: #20b2aa;
}

.circle-blue {
  background: rgba(0, 112, 255, 0.1);
}

.circle-blue i {
  color: #0070ff;
}

.circle-danger {
  background: rgba(255, 0, 0, 0.1);
}

.circle-danger i {
  color: #ff0000;
}

.circle-hotpink {
  background: rgba(255, 105, 180, 0.1);
}

.circle-hotpink i {
  color: #ff69b4;
}

.no-data-found-wrapper {
  img {
    width: 220px;

    @media (max-width: 768px) {
      width: 170px;
    }
  }
}

/*------------------------------------------------------------------
   35. Check In
--------------------------------------------------------------------*/

.place-switch {
  height: 10%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  /* padding: 5px 10px; */
  position: relative;
}

.place-switch .switch-field {
  overflow: hidden;
  display: flex;
  align-items: center;
  background-color: var(--ot-primary-btn);
  padding: 6px;
  border-radius: 2rem;
  gap: 12px;

  @media (max-width: 576px) {
    gap: 0;
  }
}

.place-switch .switch-field input {
  display: none;
}

.place-switch .switch-field label {
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: transparent;
  color: #333;
  font-size: 1em;
  font-weight: 400;
  text-align: center;
  text-shadow: none;
  padding: 3px 10px;
  margin: 0;
  -webkit-transition: all 0.1s ease-in-out;
  -moz-transition: all 0.1s ease-in-out;
  -ms-transition: all 0.1s ease-in-out;
  -o-transition: all 0.1s ease-in-out;
  transition: all 0.1s ease-in-out;
  border-radius: 2rem;
  cursor: pointer;
  color: #fff;
}

.place-switch .switch-field label p {
  margin: 0;
  color: #fff;
  font-weight: 400;

  @media (max-width: 576px) {
    font-size: 12px;
  }
}

.place-switch .switch-field:hover {
  cursor: pointer;
}

.place-switch .switch-field input:checked+label {
  background-color: #fff;
  color: var(--primary-color);
  border-radius: 50px;
}

.place-switch .switch-field input:checked+label p {
  color: var(--primary-color);
}

.company-name-clock {
  color: var(--ot-primary-text);

  .session {
    font-size: 12px;
    color: var(--ot-primary-text);
  }

  .session-time {
    font-size: 12px;
    color: var(--ot-primary-text);
  }
}

/*------------------------------------------------------------------
   36. Button hold 
--------------------------------------------------------------------*/
.button-hold-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.button-hold {
  /* --background: #2b3044; */
  --progress-border: red;
  --progress-active: #fff;
  --progress-success: #5c86ff;
  --tick-stroke: var(--progress-active);
  --shadow: rgba(0, 9, 61, 0.2);
  width: 145px;
  height: 145px;
  border-radius: 50%;
  font-size: 16px;
  font-weight: 500;
  line-height: 19px;
  border: 0;
  outline: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  cursor: pointer;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  appearance: none;
  -webkit-appearance: none;
  transition: transform 0.3s, box-shadow 0.3s;
  transform: scale(var(--scale, 1)) translateZ(0);

  display: flex;
  align-items: center;
  justify-content: center;
}

.button-holdton:focus,
.button-holdton:active,
.button-hold:focus-visible {
  border: none;
  outline: none;
}

.button-hold>div {
  display: flex;
  align-items: center;
  justify-content: center;
}

.button-hold:focus {
  outline: none;
  border: none;
}

.button-hold>div:before {
  content: "";
  position: absolute;
  width: 92%;
  height: 92%;
  background: var(--ot-primary-btn);
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  margin: 0 auto;
  text-align: center;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 550%;
  border: 4px solid #fff;
}

.button-hold .icon-text {
  position: absolute;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.button-hold>div svg {
  display: block;
}

.button-hold>div svg.icon,
.button-hold>div svg.tick {
  position: absolute;
}

.button-hold>div svg.icon {
  width: 8px;
  height: 8px;
  left: 6px;
  top: 6px;
  fill: var(--icon);
  z-index: 1;
  transition: opacity 0.2s, transform 0.2s;
  opacity: var(--icon-opacity, 1);
  transform: translateY(var(--icon-y, 0)) scale(var(--icon-scale, 1));
}

.button-hold>div svg.progress,
.button-hold>div svg.tick {
  fill: none;
}

.button-hold>div svg.progress {
  width: 145px;
  height: 145px;
  background: var(--ot-primary-btn);
  border-radius: 50%;
  transform: rotate(-180deg) scale(var(--progress-scale, 1));
  transition: transform 0.5s ease;
}

.button-hold>div svg.progress circle {
  stroke-dashoffset: 1;
  stroke-dasharray: var(--progress-array, 0) 52;
  stroke-width: 16;
  stroke: var(--progress-active);
  transition: stroke-dasharray var(--duration) linear;
}

.button-hold>div svg.tick {
  width: 145px;
  height: 145px;
  left: 15px;
  top: 10px;
  stroke-width: 3;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke: var(--tick-stroke);
  transition: stroke 0.3s ease 0.7s;
}

.button-hold>div svg.tick polyline {
  stroke-dasharray: 18 18 18;
  stroke-dashoffset: var(--tick-offset, 18);
  transition: stroke-dashoffset 0.4s ease 0.7s;
}

.button-hold:focus:not(.process),
.button-hold:hover:not(.process) {
  --shadow-y: 8px;
  --shadow-blur: 16px;
}

.button-hold:active:not(.success) {
  --scale: 0.96;
  --shadow-y: 4px;
  --shadow-blur: 8px;
}

.button-hold.process,
.button-hold.success {
  --progress-array: 52;
  --icon-y: -4px;
  --icon-scale: 0.6;
  --icon-opacity: 0;
}

.button-hold.success {
  --progress-border: none;
  --progress-scale: 0.11;
  --tick-stroke: var(--progress-success);
  --background-scale: 0;
  --tick-offset: 36;
}

.button-hold.success>div svg.progress {
  -webkit-animation: tick 0.3s linear forwards 0.4s;
  animation: tick 0.3s linear forwards 0.4s;
}

@-webkit-keyframes tick {
  100% {
    transform: rotate(-90deg) translate(0, -5px) scale(var(--progress-scale));
  }
}

@keyframes tick {
  100% {
    transform: rotate(-90deg) translate(0, -5px) scale(var(--progress-scale));
  }
}

/*------------------------------------------------------------------
   37. Checkout Modal
--------------------------------------------------------------------*/
.button-hold>div svg.progress {
  width: 145px;
  height: 145px;
  background: var(--ot-primary-btn);
  border-radius: 50%;
  transform: rotate(-180deg) scale(var(--progress-scale, 1));
  transition: transform 0.5s ease;
}

.modal .form-select {
  height: 40px;
}

.form-select {
  height: 40px;
  border: 1px solid #f0eeee;
  display: block;
}

.daterange-table-filter:focus,
.daterange-table-filter:focus-visible {
  box-shadow: 0 0 10px rgb(10 175 255 / 35%);
  border: 1px solid var(--ot-primary-btn) !important;
  outline: none;
}

input.daterange-table-filter {
  height: 48px !important;
  border-radius: 4px;
  padding-left: 10px;
  display: block;
}

input[type="range"] {
  font-size: 1.5rem;
  width: 100%;
}

input[type="range"] {
  color: var(--primary-color);
  --thumb-height: 1.9rem;
  --track-height: 0.125em;
  --track-color: rgba(0, 0, 0, 0.2);
  --brightness-hover: 180%;
  --brightness-down: 80%;
  --clip-edges: 0.125em;
}

/* === range commons === */

/* 
input[type="range"] {
    position: relative;
    background: #fff0;
    overflow: hidden;
}

input[type="range"]:active {
    cursor: grabbing;
}

input[type="range"],
input[type="range"]::-webkit-slider-runnable-track,
input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    transition: all ease 100ms;
    height: var(--thumb-height);
}

input[type="range"]::-webkit-slider-runnable-track,
input[type="range"]::-webkit-slider-thumb,
input[type="range"]::-webkit-slider-runnable-track,
input[type="range"]::-webkit-slider-thumb {
    position: relative;
}

input[type="range"]::-webkit-slider-thumb {
    --thumb-radius: calc((var(--thumb-height) * 0.5) - 1px);
    --clip-top: calc((var(--thumb-height) - var(--track-height)) * 0.5 - 0.5px);
    --clip-bottom: calc(var(--thumb-height) - var(--clip-top));
    --clip-further: calc(100% + 1px);
    --box-fill: calc(-100vmax - var(--thumb-width, var(--thumb-height))) 0 0 100vmax currentColor;

    width: var(--thumb-width, var(--thumb-height));
    background: linear-gradient(currentColor 0 0) scroll no-repeat left center / 50% calc(var(--track-height) + 1px);
    background-color: currentColor;
    box-shadow: var(--box-fill);
    border-radius: var(--thumb-width, var(--thumb-height));

    filter: brightness(100%);
    clip-path: polygon(100% -1px,
    var(--clip-edges) -1px,
    0 var(--clip-top),
    -100vmax var(--clip-top),
    -100vmax var(--clip-bottom),
    0 var(--clip-bottom),
    var(--clip-edges) 100%,
    var(--clip-further) var(--clip-further));
}

input[type="range"]:hover::-webkit-slider-thumb {
    filter: brightness(var(--brightness-hover));
    cursor: grab;
}

input[type="range"]:active::-webkit-slider-thumb {
    filter: brightness(var(--brightness-down));
    cursor: grabbing;
}

input[type="range"]::-webkit-slider-runnable-track {
    background: linear-gradient(var(--track-color) 0 0) scroll no-repeat center / 100% calc(var(--track-height) + 1px);
}

input[type="range"]:disabled::-webkit-slider-thumb {
    cursor: not-allowed;
}


input[type="range"],
input[type="range"]::-moz-range-track,
input[type="range"]::-moz-range-thumb {
    appearance: none;
    transition: all ease 100ms;
    height: var(--thumb-height);
}

input[type="range"]::-moz-range-track,
input[type="range"]::-moz-range-thumb,
input[type="range"]::-moz-range-progress {
    background: #fff0;
}

input[type="range"]::-moz-range-thumb {
    background: currentColor;
    border: 0;
    width: var(--thumb-width, var(--thumb-height));
    border-radius: var(--thumb-width, var(--thumb-height));
    cursor: grab;
}

input[type="range"]:active::-moz-range-thumb {
    cursor: grabbing;
}

input[type="range"]::-moz-range-track {
    width: 100%;
    background: var(--track-color);
}

input[type="range"]::-moz-range-progress {
    appearance: none;
    background: currentColor;
    transition-delay: 30ms;
}

input[type="range"]::-moz-range-track,
input[type="range"]::-moz-range-progress {
    height: calc(var(--track-height) + 1px);
    border-radius: var(--track-height);
}

input[type="range"]::-moz-range-thumb,
input[type="range"]::-moz-range-progress {
    filter: brightness(100%);
}

input[type="range"]:hover::-moz-range-thumb,
input[type="range"]:hover::-moz-range-progress {
    filter: brightness(var(--brightness-hover));
}

input[type="range"]:active::-moz-range-thumb,
input[type="range"]:active::-moz-range-progress {
    filter: brightness(var(--brightness-down));
}

input[type="range"]:disabled::-moz-range-thumb {
    cursor: not-allowed;
} */

.topbar-dropdown-menu .topbar-dropdown-footer a {
  background: var(--ot-primary-btn);
  padding: 12px;
  border-radius: 4px;
  margin-top: 20px;
  text-align: center;
  display: block;
  color: #fff;
}

.topbar-dropdown-menu .topbar-dropdown-footer a:hover {
  background: var(--ot-primary-btn);
}

.fs-10 {
  font-size: 10px;
}

.font-italic {
  font-style: italic;
}

.img-circle {
  border-radius: 50%;
}

.img-bordered-sm {
  border: 2px solid #adb5bd;
  padding: 2px;
}

.user-block img {
  float: left;
  height: 40px;
  width: 40px;
}

.user-block .username {
  font-size: 16px;
  font-weight: 600;
  margin-top: -1px;
}

.user-block .description {
  color: #6c757d;
  font-size: 13px;
  margin-top: -3px;
}

.c-none {
  display: none;
}

.post {
  border-bottom: 0 !important;
}

.post {
  border-bottom: 1px solid #adb5bd;
  color: #666;
  margin-bottom: 15px;
  padding-bottom: 15px;
}

/*------------------------------------------------------------------
   38. Sticky Note
--------------------------------------------------------------------*/
.sticky-note {
  margin-bottom: 30px;
  background: #ff8;
  padding: 20px;
  padding-top: 10px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.25);
  -webkit-transition: transform 150ms cubic-bezier(0.165, 0.84, 0.44, 1);
  transition: transform 150ms cubic-bezier(0.165, 0.84, 0.44, 1);
  z-index: 1;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  text-align: justify;
}

.sticky-note:hover {
  cursor: move;
  transform: rotate(0deg);
}

.sticky-note>* {
  z-index: 1;
}

.sticky-note::after {
  content: "";
  position: absolute;
  z-index: 0;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  opacity: 0;
  -webkit-transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
  transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.sticky-note:hover::after {
  opacity: 1;
}

.cursor-pointer {
  cursor: pointer !important;
}

.single-post {
  position: relative;
  background: #fff;
  -webkit-box-shadow: 0 0 50px rgb(10 42 67 / 5%);
  box-shadow: 0 0 50px rgb(10 42 67 / 5%);
  border-radius: 4px;
}

.single-post .author {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  background: #fff;
  -webkit-box-shadow: 0 0 50px rgb(10 42 67 / 5%);
  box-shadow: 0 0 50px rgb(10 42 67 / 5%);
  border-radius: 15px;
  position: absolute;
  top: 10px;
  left: 10px;
  line-height: 30px;
  z-index: 1;
}

.single-post .author img {
  height: 30px;
  width: 30px;
  border-radius: 50%;
}

.profile-content {
  .profile-menu-body a.active {
    color: #5669ff;
    border-right: 2px solid #5669ff;
  }
}

.nav-link.settings-nav.active {
  background: transparent;
  color: var(--ot-primary-text) !important;
  font-weight: 600;
}

.profile-body-cus {
  width: 100% !important;
  padding: 20px !important;
}

.bg-gray {
  background: #f7fafc !important;
}

.break-btn i {
  font-size: 25px;
  color: #2cc0fe;
}

.break-color {
  background: rgb(168 225 249 / 10%);
}

.checkin-btn i {
  font-size: 25px;
  color: #55d28c;
}

.checkin-color {
  background: rgba(172, 224, 195, 0.1);
}

.checkout-btn i {
  font-size: 25px;
  color: #ff9f74;
}

.text-danger {
  color: rgba(var(--danger-color-rgb), 1);
}

/* Custom Icon Size */
.icon-size-24 svg {
  width: 24px;
  height: 24px;
}

.icon-size-22 svg {
  width: 22px;
  height: 22px;
}

.icon-size-20 svg {
  width: 20px;
  height: 20px;
}

.icon-size-18 svg {
  width: 18px;
  height: 18px;
}

.icon-size-16 svg {
  width: 16px;
  height: 16px;
}

.language-select-container {
  width: 100% !important;
}

.profile-expand-item span {
  display: flex;
  align-items: center;
}

.datepicker-icon {
  position: relative;
}

.datepicker-icon i {
  position: absolute;
  top: 10px;
  right: 8px;
  font-size: 20px;
  color: var(--ot-primary-text);
}

.table .dropdown-action .dropdown-item:hover {
  color: var(--ot-text-secondary) !important;
}

h6,
h5,
h4,
h3,
h2,
h1,
strong {
  font-family: var(--heading-fonts);
  color: var(--ot-primary-text);
}

.company-name-clock {
  font-size: 18px;
  color: var(--ot-primary-text);
  white-space: nowrap;
}

.profile-border {
  padding-bottom: 20px;
  border-bottom: 2px solid #f4f4f4;
}

/*------------------------------------------------------------------
    39. Select 2 Style - SCSS Version
--------------------------------------------------------------------*/

.single-select {
  width: 100%;

  >.select2-container {
    .select2-selection--single {
      background: var(--ot-primary-btn);
      border-radius: 4px;
      border: 0 !important;
      outline: 0 !important;
      height: 40px;

      .select2-selection-rendered {
        line-height: 40px;
        font-weight: 600;
        padding-right: 24px;
        padding-left: 0;
      }
    }

    &.select2-container--default {
      .select2-selection--single {
        .select2-selection-arrow {
          height: 40px;
          color: var(--ot-primary-text);
          padding-right: 6px;
          right: 2px;
        }
      }
    }
  }
}

.header-control-item {
  .single-select {
    >.select2-container--default {
      .select2-selection--single {
        .select2-selection-arrow {
          height: 13px;
          top: 15px;
          right: 7px;
          color: var(--ot-primary-text);
        }

        .select2-selection-rendered {
          line-height: 40px;
          font-weight: 600;
          padding-right: 24px;
          padding-left: 0;
          border: 1px solid rgba(var(--primary-color-rgb), 0.1);
          padding: 1px 26px 1px 16px;
          height: 40px;
          min-width: 111px;
          border-radius: 4px;
        }
      }
    }
  }
}

.select2-container {
  .select2-selection--single {
    height: 40px;
    background-color: var(--ot-bg-secondary) !important;
    border: 1px solid var(--ot-primary-border) !important;
  }
}

.select2-container--default {
  .select2-selection--single {
    .select2-selection-rendered {
      line-height: 40px;
      color: var(--ot-primary-text);
    }

    .select2-selection-arrow {
      height: 40px;
      color: var(--ot-primary-text);
      padding-right: 6px;
      right: 2px;
    }
  }

  .select2-results-option--highlighted.select2-results-option--selectable {
    background-color: var(--ot-primary-btn);
  }

  .select2-results-option--selected {
    background-color: var(--ot-primary-btn);
    color: var(--bs-white);
  }
}

.selection--single {
  .select2-selection-rendered {
    color: #dde1ff;
    line-height: 40px;
  }
}

.radio-button-group {
  .btn-group {
    .btn.radio-active {
      box-shadow: none;
      border-radius: 0.3rem;
      background: var(--ot-primary-btn);
      border: 1px solid #21c6fb;
      transition: all 0.25s ease-in-out;
      color: #fff;
    }
  }
}

/*------------------------------------------------------------------
   40. Auth Page CSS
--------------------------------------------------------------------*/
.auth-container {
  height: 100vh;
  padding: 50px 209px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;
  overflow-x: auto;

  @media screen and (min-width: 1499.99px) and (max-width: 1800px) {
    padding: 50px 120px;
  }

  @media screen and (min-width: 992px) and (max-width: 1199px) {
    padding: 50px 50px;
  }

  @media (max-width: 992px) {
    padding: 30px 40px 40px;
  }

  @media (max-width: 767.99px) {
    padding: 30px 40px 40px;
  }

  /* @media (max-width: 576px) {
        padding: 30px 40px 40px;
    } */

  .form-wrapper {
    .form-logo {
      img {
        max-width: 170px;
      }
    }

    .form-heading {
      .title {
        font-weight: 700;
        font-size: 18px;
        color: var(--ot-primary-text);
      }

      .subtitle {
        font-weight: 400;
        font-size: 14px;
        line-height: 21px;
        color: var(--ot-primary-text);
      }
    }

    .social-login {
      width: 100%;

      .social-login-item {
        border: 1px solid #eaeaea;
        border-radius: 4px;

        a {
          width: 100%;
          height: 100%;
          padding: 17px 38px;
          font-weight: 500;
          font-size: 14px;
          line-height: 16px;
          color: var(--ot-primary-text);
          text-decoration: none;
        }

        &.facebook-login {
          background-color: #395799;

          a {
            color: #fff;
          }
        }

        &:hover {
          box-shadow: 0 0 10px rgba(10, 175, 255, 0.35);
          border: 1px solid #0010f7;
          outline: none;
        }
      }
    }

    .login-with-divider-text {
      font-weight: 600;
      font-size: 10px;
      line-height: 14px;
      color: var(--ot-primary-text);
      margin-bottom: 0;
    }

    .auth-form {
      label {
        font-style: normal;
        font-weight: 500;
        font-size: 14px;
        line-height: 16px;
        color: var(--ot-primary-text);
        margin-bottom: 8px;
      }

      .input-field-group {
        width: 100%;
        line-height: 0;
      }

      input[type="text"],
      input[type="password"],
      input[type="date"],
      select,
      input[type="email"] {
        width: 100%;
        font-size: 14px;
        font-weight: 400;
        padding: 16px 18px;
        background: #ffffff;
        border: 1px solid var(--ot-primary-border);
        border-radius: 4px;
        color: var(--ot-primary-text);
        height: 40px;
      }

      .custom-input-field {
        width: 100%;
        position: relative;

        img {
          position: absolute;
          left: 14px;
          top: 50%;
          transform: translateY(-50%);
        }

        i {
          position: absolute;
          right: 16px;
          top: 50%;
          transform: translateY(-50%);
          color: var(--ot-primary-text);
          font-size: 18px;
          cursor: pointer;
        }
      }

      .input-error {
        font-weight: 400;
        font-size: 10px;
        line-height: 14px;
        margin-bottom: 0;

        &.error-danger {
          color: #ff6a54;
        }

        &.error-warning {
          color: #ff991a;
        }
      }

      .remember-me {
        label {
          margin-left: 8px;
          margin-bottom: 0;
          color: var(--ot-primary-text) !important;
        }
      }

      .forgot-password,
      .remember-me label {
        font-weight: 500;
        font-size: 14px;
        line-height: 16px;
        color: var(--ot-primary-text);
      }
    }

    .authenticate-now {
      font-weight: 500;
      font-size: 14px;
      line-height: 18px;
      color: var(--ot-primary-text);

      a {
        background: var(--ot-primary-btn);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;

        &:hover {
          background: var(--ot-primary-btn);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }

    .privacy-policy-navigation {

      a,
      span {
        font-weight: 500;
        font-size: 12px;
        line-height: 21px;
        color: var(--ot-primary-text);
      }

      span {
        font-size: 16px;
      }
    }
  }
}

.auth-menu {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  max-width: 400px;
  margin: 0 auto;
}

.auth-menu a {
  font-size: 14px;
}

.fogotPassword {
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  color: #f33;
  text-transform: capitalize;
  text-decoration: none;
}

.submit-button-only-border {
  background: transparent !important;
  border: 1px solid var(--primary-color) !important;
  color: var(--primary-color);
}

.submit-button-only-border:hover {
  background: var(--ot-primary-btn) !important;
  color: #fff !important;
  border: none !important;
}

/*------------------------------------------------------------------
    41. Error Page CSS
--------------------------------------------------------------------*/
.cus-error {
  font-weight: 400;
  font-size: 10px;
  line-height: 14px;
  margin-bottom: 10px;
}

.progress {
  background: var(--ot-bg-progress);
}

.w-60 {
  width: 60px;
}

.w-50 {
  width: 50px !important;
}

.w-40 {
  width: 40px;
}

.w-30 {
  width: 30px;
}

.w-20 {
  width: 20px;
}

.w-70 {
  width: 70px;
}

.w-80 {
  width: 80px;
}

.h-80 {
  height: 80px;
}

.w-90 {
  width: 90px;
}

.w-200 {
  width: 200px;
}

.w-300 {
  width: 300px;
}

.w-400 {
  width: 400px;
}

@media (min-width: 1600px) {
  .d-xxxl-block {
    display: block !important;
  }
}

.accordion-button:focus {
  box-shadow: 0 0 10px rgb(10 175 255 / 35%) !important;
  border: 1px solid var(--ot-primary-btn) !important;
}

.accordion-item,
.accordion-button {
  background-color: var(--ot-bg-secondary) !important;
  border: 1px solid var(--ot-primary-border) !important;
}

.accordion-button span {
  color: var(--ot-primary-text) !important;
}

.accordion-button::after {
  background-image: none;
  content: "\f107";
  font-family: "Line Awesome Free";
  font-weight: 900;
  color: var(--ot-primary-text);
}

.accordion-button:not(.collapsed)::after {
  background-image: none;
}

.accordion-button.collapsed,
.accordion-button {
  padding: 5px 20px;
}

.sidebar-logo {
  width: 130px;
  height: 50px;
}

img.full-logo {
  width: 100%;
}

.icon-upload {
  display: flex;
  justify-content: center;
}

.icon-upload .sizing {
  position: relative;
  width: 80px;
  height: 80px;
}

.icon-upload .sizing img {
  border: 1px solid #adb5bd;
  border-radius: 10px;
  margin: 0 auto;
  padding: 1px;
  width: 80px !important;
  height: 80px !important;
}

.icon-upload .sizing label {
  position: absolute;
  top: 50px;
  right: 1px;
}

.download-icon {
  border-radius: 4px;
  width: 28px;
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #7f58fea3;
}

.download-icon i {
  color: #fff;
}

.dropdown-menu.show {
  border-radius: 5px 5px 10px 10px;
  border: 0;
  padding: 15px 0;
  margin-top: 0 !important;
  width: 100%;
  min-width: 180px;
  max-width: 150px;
}

.topbar-dropdown-menu h1 {
  font-size: 20px;
  padding-left: 0;
  font-weight: 500;
  color: var(--bs-black);
}

.table-toolbar .btn-add {
  padding: 11px 17px;
  border: 0;
}

.topbar-notifications {
  max-width: 340px !important;
}

.off-shadow {
  filter: none;
}

.footer {
  border-top: 0;
  border-bottom: 0;
  background: var(--primary-bg);
  box-shadow: rgba(50, 50, 93, 0) 0 6px 12px -2px,
    rgba(0, 0, 0, 0.04) 0 3px 7px -3px;
  margin-top: 20px;
  padding: 21px 0;
  width: 100%;
}

.footer p {
  display: flex;
  margin-left: 28px;
  color: var(--ot-primary-text);
  justify-content: center;
}

.footer p a {
  margin: 0 3px;
  color: #1890ff;
}

div#layout-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
}

#layout-wrapper main {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

#layout-wrapper .page-content {
  flex: 1 1 0;

  @media (max-width: 768px) {
    padding-bottom: 90px;
  }
}

.table-toolbar .dropdown-export .dropdown-menu {
  padding: 24px;
  margin-top: 0 !important;
  border-radius: 5px 5px 10px 10px;
  border: 0;
  width: 100%;
  min-width: 180px;
  box-shadow: rgba(149, 157, 165, 0.2) 0 8px 24px;
}

.sidebar .sidebar-menu .sidebar-menu-section .sidebar-menu-item>ul>li>ul>li>a {
  padding-left: 51px;
  padding-top: 7px;
  padding-bottom: 7px;
}

.radio-button-group .btn-group .btn.radio-active {
  border-radius: 0 0.3rem 0.3rem 0;
}

.radio-button-group .btn-group .btn.radio-active {
  border-radius: 0 0.3rem 0.3rem 0;
}

.radio-button-group .btn-group .btn:not(:first-child) {
  border-radius: 0 0.3rem 0.3rem 0;
}

.radio-button-group .btn-group .btn:not(:last-child) {
  border-radius: 0.3rem 0 0 0.3rem;
}

.ot-form-control:focus-visible,
.ot-form-control:focus,
.form-select:focus-visible {
  box-shadow: none !important;
}

.cus-error {
  margin-top: 5px;
}

/*noinspection CssUnknownTarget*/
.modal-header.modal-bg {
  background-image: url("/assets/images/Small.jpg");
}

.height-20 {
  height: 20px;
}

.height-600 {
  height: 600px;
}

.fs-30 {
  font-size: 30px;
}

.grayColor1 {
  color: #555555 !important;
}

.grayColor2 {
  color: #555658 !important;
}

.fs-90 {
  font-size: 90px;
}

/*------------------------------------------------------------------
    42. Award modal css
--------------------------------------------------------------------*/
.page-404 {
  padding: 40px 0;
  background: #fff;
  font-family: "Arvo", serif;
}

.page-404 img {
  width: 100%;
}

.four-zero-four-bg {
  background-image: url(https://cdn.dribbble.com/users/285475/screenshots/2083086/dribbble_1.gif);
  height: 400px;
  background-position: center;
}

.four-zero-four-bg h1 {
  font-size: 80px;
}

.four-zero-four-bg h3 {
  font-size: 80px;
}

.link-404 {
  background: linear-gradient(93.58deg, #a58cf6 0%, #645496 100%);
  color: #fff;
  transition: all 0.25s ease-in-out;
  padding: 0.45rem 1.2rem;
  font-size: 0.975rem;
  border-radius: 4px;
  box-shadow: 0 4px 4px rgb(0 0 0 / 20%);
}

.link-404:hover {
  color: #fff;
}

.err-page h4 {
  color: #6400ff;
  font-size: 14pt;
}

.contant-box-404 {
  margin-top: -50px;
}

.err-page-right h1 {
  font-family: "Noto Sans", sans-serif;
  font-size: 70pt;
  margin: 0;
  color: #6400ff;
}

.err-page-right p {
  font-size: 14pt;
  color: #737373;
}

.err-btn {
  background: #fff;
  border: 2px solid #6400ff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 8px 15px rgb(0 0 0 / 10%);
  cursor: pointer;
  font-size: 13pt;
  transition: 0.5s;
}

.fs-15 {
  font-size: 15px !important;
}

.fs-12 {
  font-size: 12px !important;
}

.green-color {
  color: #81b441;
}

.color-pink {
  color: #ee316b;
}

.modal-headerBg-color {
  background: #001f3f;
}

.mapH-500 {
  height: 500px;
}

.w-19px {
  width: 19px;
}

.color-red {
  color: red;
}

.error {
  color: red;
  font-size: 12px;
}

.ot-fileUploader .ot-btn-primary {
  margin-left: 5px;
}

.ot-dropdown-btn:hover,
.ot-dropdown-btn:focus {
  background: var(--ot-primary-btn);
}

.empty-table {
  padding: 30px;
}

.step {
  position: relative;
  padding: 20px;
  border-left: 5px solid rgb(122, 197, 228);
  position: relative;
  margin-left: 1em;
  border-bottom: 1px solid #ddd;
}

.scroll-step {
  height: 500px;
  overflow-y: scroll;
}

.step h2 {
  color: #000000de;
  font-weight: 500;
  font-size: 13px;
  border: solid;
  border-color: #e0e0e0;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
  border-width: 1px;
  font-weight: 500;
  padding: 2px 12px;
  padding: 8px 15px;
  margin-bottom: 0;
}

.step p {
  color: rgba(0, 0, 0, 0.54);
  font-size: 12px;
}

.step h2::before {
  content: attr(data-step-id);
  border-radius: 2em;
  background-color: rgb(122, 197, 228);
  width: 20px;
  height: 20px;
  display: inline-block;
  position: absolute;
  left: -13px;
  top: 0;
  text-align: center;
  line-height: 2em;
  font-size: 1.2em;
  font-weight: normal;
  color: #eeeeee;
  text-indent: 0;
}

.step::before {
  content: "";
  border: 0.8em solid transparent;
  border-top-width: 1.5em;
  position: absolute;
  display: inline-block;
  left: -0.9em;
  bottom: -0.9em;
}

[class*="step"]:last-of-type {
  border-color: transparent;
}

[class*="step"]:last-of-type::before {
  content: none;
}

.cms-style {
  width: 145px;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  border: 1px solid #ddd;
  padding: 10px;
  text-align: center;
}

.cms-style-image {
  margin-bottom: 10px !important;
  display: block;
}

.cms-style-image img {
  width: 100% !important;
}

.cms-style-name {
  font-size: 12px;
  text-transform: capitalize;
}

/*------------------------------------------------------------------
    43. File Uploader
--------------------------------------------------------------------*/
.ot-fileUploader {
  display: flex;
  align-items: center;
  border: 1px solid var(--ot-primary-border) !important;
  height: 40px;
  border-radius: 6px;
}

.ot-fileUploader input.form-control {
  border: 0 !important;
  border-radius: 3px;
  border: 0;
  height: 40px;
  width: 100%;
  box-shadow: none !important;
  background: none;
}

.ot-fileUploader button {
  background: transparent;
}

.ot-fileUploader .ot-btn-primary {
  padding: 7px 15px;
  font-size: 16px;
  font-weight: 500;
}

.ot-fileUploader .ot-btn-primary:hover {
  border: 1px solid transparent;
}

.input-check-radio .form-check .form-check-input,
.input-check-radio .form-check .form-check-label {
  cursor: pointer;
}

.left-side {
  flex-direction: row-reverse;
}

.fw-bold.title-color h4 {
  font-weight: 500;
}

.input-box input {
  height: 46px;
  background: none;
  color: var(--ot-subtitle-text);
}

.input-box input::placeholder {
  color: var(--ot-subtitle-text);
}

.form-control:focus {
  background: none !important;
  color: var(--ot-subtitle-text) !important;
}

.input-box input {
  box-shadow: none !important;
}

.no-data-found-wrapper img {
  margin-bottom: 10px;
}

body ::-webkit-scrollbar-track {
  background: rgb(0 0 0 / 5%) !important;
  border-radius: 0;
}

::-webkit-scrollbar-track {
  background: rgb(0 0 0 / 5%) !important;
}

.dropdown-item:hover {
  background-color: var(--ot-bg-secondary);
  color: var(--ot-primary-text) !important;
}

.dropdown-item.active,
.dropdown-item:active {
  color: var(--ot-primary-text) !important;
  text-decoration: none;
  background: none;
}

.dropdown-item {
  color: var(--ot-primary-text) !important;
}

.dropdown-menu.show {
  display: block;
  background: var(--ot-bg-secondary) !important;
  box-shadow: rgba(149, 157, 165, 0.2) 0 8px 24px;
}

.btn.show:focus,
.btn:active:focus {
  outline: none !important;
  box-shadow: none !important;
}

button:focus:not(:focus-visible) {
  outline: none !important;
}

.btn-check:focus+.btn,
.btn:focus {
  box-shadow: none !important;
}

.role-permisssion-control {
  border: 1px solid var(--ot-primary-border);
  border-radius: 4px;
  padding: 28px;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px var(--ot-bg-secondary) inset !important;
}

input:-webkit-autofill {
  -webkit-text-fill-color: var(--ot-primary-text) !important;
}

.breadcrumb-warning h3 {
  font-size: 18px;
  line-height: 1;
}

.highlight {
  color: var(--ot-subtitle-text) !important;
  font-weight: 600;
  font-size: 13px;
}

.notification-counter {
  top: -3px;
  right: 3px;
  color: #fff;
  background: var(--primary-color);
  border-radius: 50%;
  height: 8px;
  font-size: 10px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  width: 8px;
}

.message-counter {
  top: -11px;
  right: -11px;
  color: #fff;
  background: var(--ot-bg-badge-danger);
  border-radius: 50%;
  font-size: 9px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 2px;
  width: 18px;
  height: 18px;
  font-weight: 600;
}

.topbar-dropdown-menu .topbar-dropdown-content .topbar-dropdown-item {
  padding: 10px 10px;
}

.notification-body {
  height: 260px;
  overflow: hidden;
  overflow-y: auto;
}

.half-expand .sidebar .sidebar-menu .child-menu-list.mm-show.mm-collapse .sidebar-menu-item .parent-item-content.has-arrow {
  display: flex;
  align-items: center;
  justify-content: start;
}

.half-expand .sidebar .sidebar-menu .child-menu-list.mm-show.mm-collapse .sidebar-menu-item .parent-item-content.has-arrow span {
  display: block !important;
}

.min-height-500 {
  min-height: 500px;
}

.min-height-100 {
  min-height: 100px;
}

.min-height-50 {
  min-height: 50px;
}

.min-height-80 {
  min-height: 80px;
}

.min-height-200 {
  min-height: 200px;
}

.min-height-300 {
  min-height: 300px;
}

/*------------------------------------------------------------------
    44. Award modal css
--------------------------------------------------------------------*/
button.btn-close-award {
  position: absolute;
  right: -29px;
  top: -24px;
  width: 30px;
  height: 30px;
  background: #fff;
  border-radius: 50%;
  font-size: 15px;
  color: red;
}

.award-content {
  box-shadow: rgba(0, 0, 0, 0.24) 0 3px 8px;
  max-width: 350px;
  padding: 19px;
  text-align: center;
  margin: 0 auto;
  position: relative;
}

.award-content .congrats {
  text-transform: uppercase;
  font-size: 36px;
  text-align: center;
  margin: 0 !important;
  margin-bottom: 20px !important;
}

.award-content .logo {
  width: 100%;
  margin-bottom: 20px;
}

.award-content img {
  margin-bottom: 20px;
}

.award-content .got-btn {
  border-radius: 30px;
  padding: 10px 20px;
  text-transform: capitalize;
  font-weight: 700;
}

.modal-content.bg-transparent {
  background: none !important;
}

.gold-button {
  color: #fff;
  background-image: linear-gradient(180deg, #f6e27a 20%, #cb9b51 80%);
  font-weight: bold;
  padding: 12px 20px;
  border-radius: 30px;
  margin: 5px;
  border-width: 0;
  display: inline-flex;
  align-items: center;

  box-shadow: 0 4px 12px 0 #ffcd57;
  transition: all 0.5s ease;

  justify-content: center;
  text-align: center;
  text-decoration: none;

  position: relative;
  overflow: hidden;

  flex: 1;
}

.text-subtitle {
  color: var(--ot-subtitle-text) !important;
}

.border-color {
  color: var(--ot-primary-border);
}

.border-top {
  border-top: 1px solid var(--ot-primary-border) !important;
}

.border-bottom {
  border-bottom: 1px solid var(--ot-primary-border) !important;
}

.border-right {
  border-right: 1px solid var(--ot-primary-border);
}

.border-left {
  border-left: 1px solid var(--ot-primary-border);
}

.border {
  border: 1px solid var(--ot-primary-border) !important;
}

.border-style-dashed {
  border-style: dashed !important;
}

.border-style-dotted {
  border-style: dotted !important;
}

.border-color-soft {
  border-color: rgba(var(--primary-color-rgb), 0.1) !important;
}

.text-secondary {
  color: var(--ot-secondary-text) !important;
}

.off-filter-drop-shadow {
  filter: unset !important;
  box-shadow: unset !important;
}

/*------------------------------------------------------------------
    46. Login Page css
--------------------------------------------------------------------*/
.auth-right-content {
  padding: 10px 50px 10px 83px;

  .title {
    font-size: 40px;
    font-weight: 700;
    color: var(--ot-primary-text);
    margin-bottom: 4px;
  }

  .feature {
    background: var(--ot-tertiary-text);
    padding: 5px 8px;
    font-weight: 500;
    border-radius: 4px;
    text-transform: capitalize;
    color: #fff;
    font-size: 14px;
    display: inline-block;
    margin-bottom: 17px;
  }

  .para {
    color: var(--ot-subtitle-text);
    margin-bottom: 22px;
    font-size: 20px;
  }

  .feature-list {
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 8px;

    .list-items {
      list-style: none;
      font-size: 14px;
      display: flex;
      align-items: center;
      gap: 8px;
      color: var(--ot-subtitle-text);
    }
  }
}

/*------------------------------------------------------------------
    47. New CSS - 2 0 2 5
--------------------------------------------------------------------*/
.footer-content {
  position: absolute;
  bottom: 0;
  background: #fff;
  padding: 13px 10px 8px 10px;
  right: 0;
  left: 0;
  border-top: 1px solid var(--ot-primary-border);

  @media (max-width: 992px) {
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(6px);
  }
}

.hide-scrollbar,
.hide-scroll {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.create-menu {
  ul {
    padding: 0;
    margin: 0;
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 14px;
    padding: 0 5px;

    li {
      padding: 0px 12px;
      border-radius: 5px;
      color: var(--ot-primary-text);
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
    }
  }
}

/*------------------------------------------------------------------
    48. Profile
--------------------------------------------------------------------*/
.user-profile-photo {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: var(--primary-bg);
  overflow: hidden;
}

.profile-menu {
  display: flex;
  align-items: center;
  gap: 18px;
  flex-wrap: wrap;

  .profile-menu-item {
    &:not(:last-child) {
      border-right: 1px solid var(--ot-primary-border);
      padding-right: 30px;
      margin-right: 13px;
    }

    .contents {
      .paragraph {
        margin-bottom: 9px;
        font-size: 14px;
        font-weight: 500;
      }

      .title {
        margin: 0;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}

.line-style {
  position: relative;

  &::before {
    content: "";
    width: 1px;
    height: 50px;
    background: var(--ot-primary-border);
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 2px;
    display: block;
    z-index: 1;
    left: 0;
  }
}

.line-style2 {
  position: relative;
  height: 65px;

  &::before {
    content: "";
    width: 1px;
    height: 65px;
    background: var(--ot-primary-border);
    position: absolute;
    right: 0;
    top: 0;
    /* transform: translateY(-50%); */
    border-radius: 2px;
    display: block;
    z-index: 1;
    left: 0;
  }
}

.line-style .notifications-feed {
  background-color: #ffebae;
  padding: 4px 9px;
  font-weight: 500;
  border-radius: 4px;
  font-size: 12px;
  text-transform: capitalize;
}

.single-traking .user-box img {
  width: 35px;
  height: 35px;
  object-fit: cover;
  border-radius: 100%;
}

/*------------------------------------------------------------------
    49. Profile Complete UI
--------------------------------------------------------------------*/
.profile-completed-wrapper {
  max-width: 250px;
  margin: 0 auto;

  .card-header {
    text-align: center;
    margin-bottom: 16px;

    .profile-title {
      font-size: 20px;
      font-weight: 600;
      color: #333;
    }
  }

  .chart-container {
    position: relative;
    width: 200px;
    height: 120px;
    margin: 0 auto 30px;
  }

  .percentage {
    position: absolute;
    top: 70%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    width: 100%;

    .percentage-value {
      font-size: 32px;
      font-weight: bold;
      color: #333;
      line-height: 1.2;
    }

    .percentage-label {
      font-size: 14px;
      color: #777;
      line-height: 1;
    }
  }

  .checklist {
    list-style-type: none;

    .checklist-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #eee;

      &:last-child {
        border-bottom: none;
        border-bottom: 1px solid var(--ot-primary-border);
      }

      &.active {
        .checklist-label {
          color: var(--primary-color);
        }

        .checklist-status {
          .circle {
            border-color: var(--primary-color);
          }
        }

        /* &:last-child {
                    border-color: var(--primary-color);
                } */
      }

      .checklist-status {
        margin-right: 12px;
        position: relative;

        .circle {
          width: 20px;
          height: 20px;
          border: 2px solid #ddd;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        &.checked {
          .circle {
            background-color: var(--primary-color);
            border-color: var(--primary-color);

            &::after {
              content: "";
              width: 5px;
              height: 9px;
              border: solid white;
              border-width: 0 2px 2px 0;
              transform: rotate(45deg);
              margin-bottom: 2px;
            }
          }
        }
      }

      .checklist-label {
        flex: 1;
        font-size: 14px;
        font-weight: 600;
        line-height: 1;
      }

      .checklist-count {
        font-size: 12px;
        color: #777;
        font-weight: 500;

        &.complete {
          color: var(--primary-color);
        }
      }
    }
  }
}

/*------------------------------------------------------------------
    50. Tab style 02
--------------------------------------------------------------------*/
.tab-style-2 {
  background-color: var(--ot-bg-primary);

  &.nav-pills {
    .nav-link {
      display: flex;
      font-size: 14px;
      font-weight: 600;
      color: var(--ot-primary-text);
      text-decoration: none;
      gap: 10px;
      align-items: center;
      margin: 0 0;

      &.active {
        background: #fff;

        svg {
          color: var(--primary-color) !important;
        }
      }
    }
  }
}

.tab-style-3 {
  background-color: transparent;

  &.nav-pills {
    .nav-link {
      display: flex;
      font-size: 14px;
      font-weight: 500;
      color: var(--ot-primary-text);
      text-decoration: none;
      gap: 10px;
      align-items: center;
      background: var(--primary-bg);

      &.active {
        background: #cde0fb;
        color: var(--primary-color);

        svg {
          color: var(--primary-color);
        }
      }
    }
  }
}

.tab-style-4 {
  background-color: transparent;

  &.nav-pills {
    border: 1px solid var(--ot-primary-border);
    border-radius: 30px;
    margin-bottom: 10px;
    align-items: center;
    padding: 3px;

    .nav-link {
      display: flex;
      font-size: 12px;
      font-weight: 600;
      color: var(--ot-primary-text);
      text-decoration: none;
      gap: 4px;
      align-items: center;
      background: transparent;
      border-radius: 30px;
      padding: 4px 14px;

      &.active {
        background: var(--ot-bg-primary);
        color: var(--primary-color);

        svg {
          color: var(--primary-color);
        }
      }
    }
  }
}

.text-area-view {
  border: transparent !important;
  padding: 0 !important;

  &:focus {
    border: transparent !important;
  }
}

/*------------------------------------------------------------------
   51. User Dashboard CSS
--------------------------------------------------------------------*/
.dashboard-heading-wrapper {
  .welcome-icon {
    max-width: 50px;
    max-height: 50px;
  }

  .dashboard-heading {
    .title {
      font-size: 24px;
      color: var(--ot-primary-text);

      .user-title {
        color: var(--primary-color);
        font-weight: 300;
      }
    }

    .paragraph {
      color: var(--ot-primary-text);
      margin: 0;
    }
  }
}

.card-title {
  font-size: 20px;
  color: var(--ot-primary-text);
  font-weight: 600;
  margin-bottom: 20px;
  text-transform: capitalize;
}

.card-title-small {
  font-size: 16px;
  color: var(--ot-primary-text);
  font-weight: 500;
  margin-bottom: 20px;
  text-transform: capitalize;
}

.text-40 {
  font-size: 40px !important;
}

.red-color {
  color: #cd4040;
}

.h-calc {
  height: calc(100%);
}

.h-calc-100 {
  height: calc(100% - 100px);
}

.h-calc-50 {
  height: calc(100% - 50px);
}

.mt-100-percent {
  margin-top: 100%;
}

.mt-50-percent {
  margin-top: 50%;
}

/*------------------------------------------------------------------
    52. Error Page CSS
--------------------------------------------------------------------*/
.error-page-wrapper {
  height: 100vh;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin-top: 40px;
  padding-bottom: 50px;

  .error-image {
    max-width: 220px;
    margin-bottom: 30px;
  }

  .title {
    font-size: 22px;
    color: var(--ot-primary-text);
    font-weight: 700;
    margin-bottom: 14px;
  }

  .pera {
    font-size: 16px;
    color: var(--ot-primary-text);
  }
}

/*------------------------------------------------------------------
    53. New Table CSS
--------------------------------------------------------------------*/

.timeline {
  min-width: 400px;
  margin: 20px auto;
  position: relative;

  @media (max-width: 767.99px) {
    min-width: auto;
  }

  .timeline-item {
    display: flex;
    align-items: flex-start;
    position: relative;

    .time {
      min-width: 81px;
      font-size: 14px;
      color: var(--ot-primary-text);
      text-align: right;
      padding-right: 16px;
      border-right: 1px dashed #ddd;
      padding-bottom: 21px;
      font-weight: 600;

      span {
        font-size: 12px;
        color: #999;
      }
    }

    .circle {
      width: 8px;
      height: 8px;
      background-color: var(--primary-color);
      border-radius: 50%;
      position: absolute;
      left: 76px;
      top: 8px;

      &::after {
        position: absolute;
        content: "";
        width: 14px;
        height: 14px;
        border-radius: 50%;
        left: -3px;
        top: -3px;
        border: 1px solid var(--primary-color);
      }
    }

    .content {
      padding: 0 15px;
      padding-right: 0;
      border-radius: 8px;
      margin-left: 20px;
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;

      /* background: var(--primary-bg); */

      .user {
        display: flex;
        align-items: center;
        gap: 10px;

        img {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          object-fit: cover;
        }

        div {
          span {
            font-size: 12px;
            color: #777;
          }
        }
      }
    }
  }
}

/*------------------------------------------------------------------
   54. Status Badge CSS
--------------------------------------------------------------------*/

.status {
  font-size: 12px;
  padding: 5px 10px;
  border-radius: 5px;
  font-weight: 700;
  white-space: nowrap;

  &.in {
    background-color: #e0f7e9d4;
    color: #16b274;
  }

  &.out {
    background-color: #fde8e8ab;
    color: #dc3545;
  }

  &.break {
    background-color: #ff991a24;
    color: #ff991a;
  }

  &.message {
    background-color: #f0ecfcbf;
    color: #6f42c1;
  }
}

/*------------------------------------------------------------------
   55. Report page
--------------------------------------------------------------------*/

#table-length {
  display: inline-flex;
  z-index: 10060;
}

.dt-buttons {
  display: inline-flex;
  width: 100%;
  text-align: end;
  clear: both;
}

/* .dataTables-wrapper {
    .dataTables-processing {
        background: transparent;
        border: 0;
    }
} */

@media (max-width: 500px) {
  #table-length {
    position: relative;
    padding: 10px 0 0 0;
    text-align: center;
  }

  .dt-buttons {
    position: relative;
    text-align: center;
  }

  table#table {
    padding-top: 0;
  }
}

/*------------------------------------------------------------------
   56. New CSS
--------------------------------------------------------------------*/
.summery-card-two {
  background-color: var(--ot-bg-secondary);
  filter: drop-shadow(0px 2px 8px rgba(0, 0, 0, 0.01));
  border-radius: 8px;
  padding: 20px;
  border: 1px solid var(--ot-primary-border);

  .card-content {
    .count {
      color: var(--ot-primary-text);
      font-size: 24px;
      display: block;
      font-weight: 700;
    }

    .leave-type {
      margin-bottom: 0;
      font-weight: 500;
    }
  }

  .card-bottom {
    .card-states {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      gap: 10px;

      .card-badge {
        border-radius: 50px;
        padding: 4px 10px;
        gap: 10px;
        font-size: 12px;
        font-weight: 600;
        color: var(--ot-primary-text);

        &:first-child {
          background-color: #e9faf4;

          .count {
            color: #7ab668;
          }
        }

        &:last-child {
          background-color: #fff0ed;

          .count {
            color: #ff6a54;
          }
        }
      }
    }
  }
}

/*------------------------------------------------------------------
   57. single days Time Log
--------------------------------------------------------------------*/
.attendance-summary {
  display: flex;
  align-items: end;
  gap: 20px;
  background: #fff;

  .time-section {
    text-align: center;
    min-width: 80px;

    strong {
      display: block;
      font-size: 16px;
      color: #000;
    }

    span {
      font-size: 12px;
      color: #666;
    }
  }

  .timeline {
    min-width: 400px;
    margin-bottom: 18px;
    margin-top: 0;
    position: relative;
    display: flex;
    gap: 1px;
    padding: 3px 0;

    @media (max-width: 767.99px) {
      min-width: auto;
    }

    .block {
      min-width: 10px;
      height: 25px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-weight: 700;
      font-size: 12px;
      overflow: hidden;
      /* cursor: context-menu; */

      &.working {
        background-color: rgba(var(--success-color-rgb), 0.2);
        color: rgba(var(--success-color-rgb), 1);
      }

      &.break {
        background-color: rgba(var(--warning-color-rgb), 0.1);
        color: rgba(var(--warning-color-rgb), 1);
      }

      &.overtime {
        background-color: rgba(var(--info-color-rgb), 0.1);
        color: rgba(var(--info-color-rgb), 1);
      }

      &.late {
        background-color: rgba(var(--danger-color-rgb), 0.1);
        color: rgba(var(--danger-color-rgb), 1);
      }

      &.consider {
        background-color: #e1e1e1;
      }
    }
  }

  .time-labels {
    display: flex;
    gap: 25px;
    font-size: 12px;
    color: #666;
    flex-wrap: wrap;
    justify-content: space-between;
  }
}

/*------------------------------------------------------------------
   58. Months Time Log
--------------------------------------------------------------------*/
.monthly-attendance-summary {
  display: flex;
  align-items: end;
  gap: 20px;
  background: #fff;

  .time-section {
    text-align: center;
    min-width: 80px;

    strong {
      display: block;
      font-size: 16px;
      color: #000;
    }

    span {
      font-size: 12px;
      color: #666;
    }
  }

  .timeline {
    min-width: auto;
    margin-bottom: 18px;
    margin-top: 0;
    position: relative;
    display: flex;
    gap: 4px;

    .block {
      min-width: auto;
      height: 16px;
      border-radius: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-weight: 600;
      font-size: 14px;

      &.working {
        background-color: #16b274;
      }

      &.break {
        background-color: #f39c12;
      }

      &.overtime {
        background-color: #007bff;
      }
    }
  }

  .time-labels {
    display: flex;
    gap: 25px;
    font-size: 12px;
    color: #666;
    justify-content: space-between;
  }
}

.doted {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

/*------------------------------------------------------------------
  59. Permission Tab
--------------------------------------------------------------------*/
.permission-tab {
  .nav-link {
    color: var(--ot-primary-text);
    padding: 8px 12px;
    border-radius: 4px;
    font-weight: 600;
    font-size: 14px;
    background: var(--primary-bg);
    margin-bottom: 5px;
    width: 100%;

    &.active {
      color: #fff;
      background: var(--ot-primary-btn);
    }
  }
}

/*------------------------------------------------------------------
   60. Progress CSS
--------------------------------------------------------------------*/

.progress.profile-custom-progress {
  height: 8px;
  overflow: visible;
  position: relative;
  width: 260px;

  .progress-bar {
    background-color: var(--primary-color);
    border-radius: 5px;

    .show-status {
      position: absolute;
      right: 0;
      top: -25px;
      color: var(--ot-primary-text);
      font-size: 15px;
    }
  }
}

.offcanvas-body {
  padding: 24px;
}

.offcanvas-header {
  padding: 24px;
}

.progress.custom-progress {
  height: 8px;
  overflow: visible;
  position: relative;
  width: 100%;

  .progress-bar {
    background-color: var(--primary-color);
    border-radius: 5px;
    position: relative;
    overflow: visible;

    .total-present {
      position: absolute;
      background: var(--primary-color);
      border-radius: 10px 10px 10px 0;
      font-size: 12px;
      color: #fff;
      width: 34px;
      height: 20px;
      right: -33px;
      top: -25px;
      font-weight: 600;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .total-employee {
    position: absolute;
    right: 0;
    top: -25px;
    color: var(--ot-primary-text);
    font-size: 15px;
  }
}

/*------------------------------------------------------------------
    61. Department by present CSS
--------------------------------------------------------------------*/
.department-by-present-content {
  .single-progress {
    &:nth-child(1) {
      .progress.custom-progress {
        .progress-bar {
          background-color: #5b86e6;

          .total-present {
            background: rgba(var(--primary-color-rgb), 0.1);
            color: rgba(var(--primary-color-rgb), 1);
          }
        }
      }
    }

    &:nth-child(2) {
      .progress.custom-progress {
        .progress-bar {
          background-color: #bc4c6a;

          .total-present {
            background-color: rgba(var(--danger-color-rgb), 0.1);
            color: rgba(var(--danger-color-rgb), 1);
          }
        }
      }
    }

    &:nth-child(3) {
      .progress.custom-progress {
        .progress-bar {
          background-color: #b682a6;

          .total-present {
            background: rgba(182, 130, 166, 0.1);
            color: #b682a6;
          }
        }
      }
    }

    &:nth-child(4) {
      .progress.custom-progress {
        .progress-bar {
          background-color: #547c86;

          .total-present {
            background: rgba(84, 124, 134, 0.1);
            color: #547c86;
          }
        }
      }
    }

    &:nth-child(5) {
      .progress.custom-progress {
        .progress-bar {
          background-color: #4d4b64;

          .total-present {
            background: rgba(227, 95, 119, 0.1);
            color: #e35f77;
          }
        }
      }
    }

    &:nth-child(6) {
      .progress.custom-progress {
        .progress-bar {
          background-color: #e35f77;

          .total-present {
            background: rgba(22, 178, 116, 0.1);
            color: #16b274;
          }
        }
      }
    }

    &:nth-child(7) {
      .progress.custom-progress {
        .progress-bar {
          background-color: #3498db;

          .total-present {
            background: rgba(154, 95, 227, 0.1);
            color: #9a5fe3;
          }
        }
      }
    }

    &:nth-child(8) {
      .progress.custom-progress {
        .progress-bar {
          background-color: #5853bc;

          .total-present {
            background: #5853bc;
          }
        }
      }
    }

    &:nth-child(9) {
      .progress.custom-progress {
        .progress-bar {
          background-color: #f27370;

          .total-present {
            background: #f27370;
          }
        }
      }
    }

    &:nth-child(10) {
      .progress.custom-progress {
        .progress-bar {
          background-color: #c24d2c;

          .total-present {
            background: #c24d2c;
          }
        }
      }
    }

    &:nth-child(11) {
      .progress.custom-progress {
        .progress-bar {
          background-color: #0e5f76;

          .total-present {
            background: #0e5f76;
          }
        }
      }
    }

    &:nth-child(12) {
      .progress.custom-progress {
        .progress-bar {
          background-color: #ca4b7c;

          .total-present {
            background: #ca4b7c;
          }
        }
      }
    }

    &:nth-child(13) {
      .progress.custom-progress {
        .progress-bar {
          background-color: #ff6868;

          .total-present {
            background: #ff6868;
          }
        }
      }
    }

    &:nth-child(14) {
      .progress.custom-progress {
        .progress-bar {
          background-color: #7bc7fa;

          .total-present {
            background: #7bc7fa;
          }
        }
      }
    }

    &:nth-child(15) {
      .progress.custom-progress {
        .progress-bar {
          background-color: #b67ccf;

          .total-present {
            background: #b67ccf;
          }
        }
      }
    }

    &:nth-child(16) {
      .progress.custom-progress {
        .progress-bar {
          background-color: #7971ea;

          .total-present {
            background: #7971ea;
          }
        }
      }
    }

    &:nth-child(17) {
      .progress.custom-progress {
        .progress-bar {
          background-color: #978d58;

          .total-present {
            background: #978d58;
          }
        }
      }
    }
  }
}

/*------------------------------------------------------------------
  62. Employee time line
--------------------------------------------------------------------*/
.profile-basic-details {
  .profile {
    .profile-photos {
      width: 120px;
      height: 120px;
      object-fit: cover;
      margin-bottom: 10px;
      border-radius: 50%;
      background-color: var(--primary-bg);
    }
  }
}

.timeline {
  position: relative;
  padding: 40px 0;
  padding-left: 120px;

  .timeline-entry {
    position: relative;
    border-left: 1px solid #cfd8dc;
    padding-left: 36px;

    &:not(:last-child) {
      padding-bottom: 25px;
    }

    &::before {
      content: "";
      position: absolute;
      top: -1px;
      left: -7px;
      width: 12px;
      height: 12px;
      background-color: #ddd;
      border-radius: 50%;
    }

    .timeline-date {
      position: absolute;
      left: -120px;
      top: 0;
      background-color: #ccf3dd;
      color: #1a1a1a;
      font-size: 12px;
      padding: 4px 10px;
      border-radius: 20px;
      font-weight: bold;
      text-transform: uppercase;
    }

    .timeline-content {
      background: transparent;

      h4 {
        margin: 0 0 6px;
        font-size: 16px;
        font-weight: 700;
      }

      p {
        margin: 2px 0;
        font-size: 14px;
        color: #555;
      }
    }
  }
}

/*------------------------------------------------------------------
   63. Avatar group component
--------------------------------------------------------------------*/

.avatar-group {
  display: flex;
  align-items: center;
}

.avatar {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  border: 2px solid #fff;
  overflow: hidden;
  object-fit: cover;
  margin-left: -10px;
}

.avatar:first-child {
  margin-left: 0;
}

.more-count {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  background-color: #dfdfdf;
  color: #7a7a7a;
  font-size: 10px;
  font-weight: 500;
  border: 2px solid #fff;
  margin-left: -10px;
}

/*------------------------------------------------------------------
   64. New CSS
--------------------------------------------------------------------*/

.grid-template-100 {
  grid-template-columns: 100%;
}

/*------------------------------------------------------------------
  65. swal2 CSS
--------------------------------------------------------------------*/
.active-status {
  padding: 1px 8px;
  background: #e8f7f1;
  border-radius: 30px;
  font-size: 11px;
  font-weight: 600;
  line-height: 1;
  color: #16b274;
  border: 1px solid #d3f0e4;
  margin: 0 2px;
  text-transform: capitalize;
  display: flex;
  align-items: center;
  gap: 4px;
  height: 19px;
}

.active-floor {
  padding: 3px 7px;
  background: #5cb30224;
  border-radius: 30px;
  font-size: 12px;
  font-weight: 500;
  line-height: 15px;
  color: #5cb302;
  border: 1px solid #5cb30240;
  margin: 0 2px;
  display: inline-flex;
  height: 21px;
}

.box-shadow-primary {
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.03);
}

.swal2-title {
  font-size: 16px !important;
  font-weight: 500 !important;
}

.swal2-styled.swal2-confirm {
  background-color: var(--ot-primary-btn) !important;
  box-shadow: none !important;
}

.swal2-popup {
  background: var(--ot-bg-primary) !important;
  width: 24em !important;
}

.swal2-container.swal2-backdrop-show,
.swal2-container.swal2-noanimation {
  background: rgba(0, 0, 0, 0.4);
  transition: backdrop-filter ease-in 0.5s;
  background-color: RGBA(255, 255, 255, 0.3);
  backdrop-filter: blur(2px);
}

.swal2-styled {
  max-height: 36px;
  line-height: 1;
}

.dashboard-btn-soft-success {
  color: #16b274 !important;
  background: #e8f7f1;
}

.dashboard-btn-soft-primary {
  color: #1a73e9 !important;
  background: #e8f1fd;
}

.soft-light-primary {
  background: rgba(var(--primary-color-rgb), 0.05);
  border: 1px solid rgba(var(--primary-color-rgb), 0.1);
}

.border-left-success {
  border-left: 3px solid #16b274;

  @media (max-width: 576px) {
    border-left: 0 !important;
  }
}

.border-left-primary {
  border-left: 3px solid #1a73e9;

  @media (max-width: 576px) {
    border-left: 0 !important;
  }
}

/*------------------------------------------------------------------
    66. 500 Error Page
--------------------------------------------------------------------*/
.maintenance-wrapper {
  grid-gap: 80px;

  @media (max-width: 768px) {
    grid-gap: 30px;
    flex-direction: column !important;
  }

  .error-page-info {
    max-width: 550px;
    /* flex-shrink: 0; */
    width: 100%;

    .title {
      font-size: 40px;
      line-height: 1;

      @media (max-width: 768px) {
        font-size: 30px;
      }
    }

    @media (max-width: 768px) {
      order: 2;
    }
  }

  .maintenance-img {
    max-width: 400px;
    /* flex-shrink: 0; */
    width: 100%;

    @media (max-width: 768px) {
      order: 1;
      max-width: 200px;
    }
  }
}

.copyable-icon {
  cursor: pointer;
  padding: 0 4px;
}

/*------------------------------------------------------------------
  67. Animate CSS
--------------------------------------------------------------------*/

.break-running {
  .button-hold>div:before {
    background: #cd4040;
  }

  .button-hold>div svg.progress {
    background: #cd4040;
  }
}

.heartbeat {
  animation: heartbeat 1s infinite alternate;
}

@-webkit-keyframes heartbeat {
  to {
    transform: scale(1.05);
  }
}

.heartbeat2 {
  animation: heartbeat 2s infinite alternate;
}

@-webkit-keyframes heartbeat {
  to {
    transform: scale(1.2);
  }
}

.heartbeat3 {
  animation: heartbeat 2s infinite alternate;
}

@keyframes heartbeat {
  to {
    transform: scale(1.1);
  }
}

@-webkit-keyframes heartbeat {
  to {
    transform: scale(1.1);
  }
}

.running {
  animation: nudge 10s linear infinite alternate;
}

.runningTwo {
  animation: nudge 10s linear infinite alternate;

  @keyframes nudge {

    0%,
    100% {
      transform: translate(0, 0);
    }

    50% {
      transform: translate(-20px, 0);

      @media #{$xs} {
        transform: translate(-20px, 0) !important;
      }
    }

    80% {
      transform: translate(20px, 0);

      @media #{$xs} {
        transform: translate(20px, 0) !important;
      }
    }
  }
}

.live {
  display: inline-block;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: -10px;
    background-color: var(--ot-bg-secondary);
    width: 10px;
    height: 10px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    z-index: 1;
  }

  &::after {
    content: "";
    display: block;
    position: absolute;
    background-color: rgba(225, 36, 84, 0.3);
    width: 20px;
    height: 20px;
    border-radius: 50%;
    -webkit-animation: live 2s ease-in-out infinite;
    animation: live 2s ease-in-out infinite;
    z-index: 1;
    top: -15px;
    left: -5px;
  }
}

@-webkit-keyframes live {
  0% {
    transform: scale(1, 1);
  }

  100% {
    transform: scale(3.5, 3.5);
    background-color: rgba(225, 36, 84, 0.3);
  }
}

@keyframes live {
  0% {
    transform: scale(1, 1);
  }

  100% {
    transform: scale(3.5, 3.5);
    background-color: rgba(255, 0, 0, 0);
  }
}

/*------------------------------------------------------------------
    68. Switch
--------------------------------------------------------------------*/
.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;

  input {
    opacity: 0;
    width: 0;
    height: 0;
  }
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(var(--secondary-color-rgb), 0.2);
  transition: 0.4s;
  width: 40px;

  &::before {
    position: absolute;
    content: "";
    height: 13px;
    width: 13px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
  }

  &.round {
    border-radius: 30px;
    height: 20px;

    &::before {
      border-radius: 50%;
    }
  }
}

/* Input + slider styles */
input {
  &:checked+.slider {
    background: rgba(var(--primary-color-rgb), 0.3);

    &::before {
      transform: translateX(18px);
      background: var(--primary-color);
    }
  }

  &:focus+.slider {
    box-shadow: none !important;
  }
}

/*------------------------------------------------------------------
    69. line clamp CSS
--------------------------------------------------------------------*/
.line-clamp-1 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  line-clamp: 1;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  line-clamp: 2;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  line-clamp: 3;
  -webkit-line-clamp: 3;
}

.line-clamp-4 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  line-clamp: 4;
  -webkit-line-clamp: 4;
}

.line-clamp-5 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  line-clamp: 5;
  -webkit-line-clamp: 5;
}

/*------------------------------------------------------------------
    70. Buttons CSS
--------------------------------------------------------------------*/
.btn-primary-fill[size="sm"],
.btn-primary-outline[size="sm"] {
  height: 30px;
  padding: 0 10px;
  font-size: 12px;
}

.action-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-weight: 500;
  outline: none;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
  font-size: 0.875rem;
  height: 2.25rem;
  padding: 0 1rem;
  border: 1px solid transparent;

  &.size-sm {
    height: 2rem;
    padding: 0 0.75rem;
    font-size: 0.75rem;
  }

  &.size-lg {
    height: 2.75rem;
    padding: 0 1.5rem;
    font-size: 1rem;
  }

  &.size-icon {
    height: 2.25rem;
    width: 2.25rem;
    padding: 0;

    &.size-sm {
      height: 2rem;
      width: 2rem;
    }

    &.size-lg {
      height: 2.75rem;
      width: 2.75rem;
    }
  }
}

/*------------------------------------------------------------------
   71. Action BUtton Variants CSS
--------------------------------------------------------------------*/
.action-icon[variant="primary"] {
  background-color: rgba(var(--primary-color-rgb), 1);
  color: white;
  border-color: rgba(var(--primary-color-rgb), 1);
}

.action-icon[variant="primary"]:hover {
  background-color: rgba(var(--primary-color-rgb), 0.8);
  border-color: rgba(var(--primary-color-rgb), 0.8);
}

.action-icon[variant="primary"]:active {
  background-color: rgba(var(--primary-color-rgb), 0.6);
  border-color: rgba(var(--primary-color-rgb), 0.6);
}

.action-icon[variant="secondary"] {
  background-color: rgba(var(--secondary-color-rgb), 1);
  color: var(--ot-subtitle-text, #0f172a);
  border-color: rgba(var(--secondary-color-rgb), 1);
}

.action-icon[variant="secondary"]:hover {
  background-color: rgba(var(--secondary-color-rgb), 0.8);
  border-color: rgba(var(--secondary-color-rgb), 0.8);
}

.action-icon[variant="secondary"]:active {
  background-color: rgba(var(--secondary-color-rgb), 0.6);
  border-color: rgba(var(--secondary-color-rgb), 0.6);
}

.action-icon[variant="danger"] {
  background-color: rgba(var(--danger-color-rgb), 0.7);
  color: white;
  border-color: rgba(var(--danger-color-rgb), 0.7);
}

.action-icon[variant="danger"]:hover {
  background-color: rgba(var(--danger-color-rgb), 0.6);
  border-color: rgba(var(--danger-color-rgb), 0.6);
}

.action-icon[variant="danger"]:active {
  background-color: rgba(var(--danger-color-rgb), 0.6);
  border-color: rgba(var(--danger-color-rgb), 0.6);
}

/*------------------------------------------------------------------
   72. Ghost Variant CSS
--------------------------------------------------------------------*/
.action-icon[variant="ghost"] {
  background-color: transparent;
  color: var(--ot-subtitle-text, #0f172a);
}

.action-icon[variant="ghost"]:hover {
  background-color: var(--ghost-hover, rgba(0, 0, 0, 0.05));
}

.action-icon[variant="ghost"]:active {
  background-color: var(--ghost-active, rgba(0, 0, 0, 0.1));
}

.action-icon[variant="destructive"] {
  background-color: rgba(var(--danger-color-rgb), 1);
  color: white;
  border-color: rgba(var(--danger-color-rgb), 1);
}

.action-icon[variant="destructive"]:hover {
  background-color: rgba(var(--danger-color-rgb), 0.8);
  border-color: rgba(var(--danger-color-rgb), 0.8);
}

.action-icon[variant="destructive"]:active {
  background-color: rgba(var(--danger-color-rgb), 0.6);
  border-color: rgba(var(--danger-color-rgb), 0.6);
}

.action-icon[variant="link"] {
  background-color: transparent;
  color: rgba(var(--primary-color-rgb), 1);
  text-decoration: underline;
  height: auto;
  padding: 0;
}

.action-icon[variant="link"]:hover {
  color: rgba(var(--primary-color-rgb), 0.8);
}

.action-icon[variant="link"]:active {
  color: rgba(var(--primary-color-rgb), 0.6);
}

.action-icon[size="icon"] {
  border: none;
  padding: 0;
  height: auto;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 26px;
  height: 26px;
  border-radius: 8px;
}

/*------------------------------------------------------------------
  73. Badge Variant
--------------------------------------------------------------------*/
.tyne-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-weight: 500;
  outline: none;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
  font-size: 12px;
  height: 26px;
  padding: 0 10px;
  border: 0;
  grid-gap: 5px;

  .badge-count {
    font-size: 14px;
  }
}

.tyne-badge[variant="primary"] {
  background-color: rgba(var(--primary-color-rgb), 0.2);
  color: rgba(var(--primary-color-rgb), 1);
  border-color: rgba(var(--primary-color-rgb), 1);
}

.tyne-badge[variant="secondary"] {
  background-color: rgba(var(--secondary-color-rgb), 0.1);
  color: rgba(var(--secondary-color-rgb), 1);
  border-color: rgba(var(--secondary-color-rgb), 0.1);
}

.tyne-badge[variant="tertiary"] {
  background-color: rgba(var(--tertiary-color-rgb), 0.1);
  color: rgba(var(--tertiary-color-rgb), 1);
  border-color: rgba(var(--tertiary-color-rgb), 0.1);
}

.tyne-badge[variant="destructive"] {
  background-color: rgba(var(--danger-color-rgb), 0.1);
  color: rgba(var(--danger-color-rgb), 1);
  border-color: rgba(var(--danger-color-rgb), 1);
}

.tyne-badge[variant="success"] {
  background-color: rgba(var(--success-color-rgb), 0.1);
  color: rgba(var(--success-color-rgb), 1);
  border-color: rgba(var(--success-color-rgb), 0.1);
}

.tyne-badge[variant="warning"] {
  background-color: rgba(var(--warning-color-rgb), 0.1);
  color: rgba(var(--warning-color-rgb), 1);
  border-color: rgba(var(--warning-color-rgb), 0.1);
}

.tyne-badge[variant="info"] {
  background-color: rgba(var(--info-color-rgb), 0.1);
  color: rgba(var(--info-color-rgb), 1);
  border-color: rgba(var(--info-color-rgb), 0.1);
}

.tyne-badge[size="icon"] {
  width: 26px;
  height: 26px;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.tyne-badge:hover {
  opacity: 0.8;
}

.notes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(292px, 1fr));
  gap: 16px;
}

.addons-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.login-demo {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 16px;
}

.addon-widget {
  background-color: var(--ot-bg-secondary);
  border-color: var(--ot-primary-border);
  min-height: 180px;
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--primary-color) !important;

    .addon-title {
      a {
        color: var(--primary-color) !important;
      }
    }
  }

  .addon-icon {
    width: 40px;
    height: 40px;
    background-color: var(--ot-bg-secondary);
  }

  .premium-addon-badge {
    background-color: var(--primary-color);
    border-color: var(--ot-primary-border);
    width: 154px;
    transform: rotate(45deg);
    right: -40px;
    top: 18px;
    text-align: center;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .addon-title {
    max-width: 90%;
  }

  .addon-title a {
    transition: 0.3s;
  }

  .addon-title a::after {
    position: absolute;
    width: 100%;
    height: 100%;
    content: "";
    top: 0;
    left: 0;
  }
}

/*------------------------------------------------------------------
  74. Credentials CSS
--------------------------------------------------------------------*/

.credentials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(292px, 1fr));
  gap: 24px;
}

.credential-card .credentials-link:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

.credential-card .credentials-link::before {
  position: absolute;
  width: 100%;
  height: 100%;
  content: "";
  top: 0;
  left: 0;
}

/*------------------------------------------------------------------
  75. Chat Box SCSS
--------------------------------------------------------------------*/

.chat-container {
  height: 90vh;
  display: flex;

  .chat-sidebar {
    width: 348px;
    border-left: 1px solid var(--ot-primary-border);
    display: flex;
    flex-direction: column;

    .search-input {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 20px;
      padding: 8px 15px;
      width: 100%;
      font-size: 14px;

      &:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
      }
    }

    .chat-list {
      flex: 1;
      overflow-y: auto;

      .chat-item {
        padding: 10px;
        cursor: pointer;
        transition: background-color 0.2s;
        position: relative;
        border-radius: 8px;
        margin-bottom: 8px;

        &::after {
          content: "";
          position: absolute;
          left: 0;
          bottom: -4px;
          width: 100%;
          border-bottom: 1px solid #f5f5f7;
        }

        &:hover,
        &.active {
          background-color: #f5f5f7;
        }

        .chat-info {
          flex: 1;
          min-width: 0;
          padding-right: 60px;

          .chat-name-row {
            display: flex;
            align-items: center;
            margin-bottom: 6px;

            .chat-name {
              font-weight: 700;
              color: var(--ot-primary-text);
              font-size: 14px;
              margin-right: 8px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              margin-bottom: 0;
            }
          }

          .chat-message {
            color: #545c66;
            font-size: 12px;
            line-height: 1;
            margin: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .chat-meta {
          position: absolute;
          top: 50%;
          right: 10px;
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: 4px;
          transform: translateY(-50%);

          .chat-time {
            color: #545c66;
            font-size: 12px;
            font-weight: 400;
          }

          .chat-badge {
            border-radius: 10px;
            background: rgba(26, 115, 233, 0.1);
            color: var(--primary-color);
            padding: 2px 7px;
            font-size: 12px;
            font-weight: 700;
            min-width: 20px;
            text-align: center;
            line-height: 1.3;
          }
        }
      }
    }
  }

  .chat-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 12px;
    flex-shrink: 0;
    box-shadow: 0 0 30px 0 rgba(0, 0, 0, 0.06);
    border: 2px solid #fff;
    position: relative;
    z-index: 2;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 50%;
    }

    .online-status {
      position: absolute;
      bottom: -9px;
      right: 0;
      z-index: 3;
    }
  }

  .chat-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;

    .chat-main-header {
      padding: 15px 24px;
      border-bottom: 1px solid #e9ecef;
      background: white;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .current-chat-info {
        display: flex;
        align-items: center;

        .current-chat-avatar {
          width: 35px;
          height: 35px;
          border-radius: 50%;
          margin-right: 12px;
        }

        .current-chat-name {
          font-size: 14px;
          font-weight: 700;
          color: var(--ot-primary-text);
          margin: 0;
        }

        .current-chat-status {
          color: #28a745;
          font-size: 12px;
        }
      }

      .chat-actions {
        display: flex;
        gap: 10px;

        i {
          color: #666;
          cursor: pointer;
          padding: 5px;
        }
      }
    }

    .chat-messages {
      flex: 1;
      padding: 24px;
      overflow-y: auto;
      background: #fff;

      .message {
        margin-bottom: 20px;
        display: flex;
        align-items: flex-end;

        &.sent {
          justify-content: flex-end;

          .message-avatar {
            margin-right: 0;
            margin-left: 10px;
            order: 2;
          }

          .message-content {
            order: 1;

            .message-content-text {
              background: rgba(26, 115, 233, 0.1);
              color: #161f2b;
            }

            .message-time {
              text-align: left;
            }
          }
        }

        .message-avatar {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          margin-right: 10px;
          flex-shrink: 0;
        }

        .message-content {
          max-width: 70%;
          position: relative;
          top: 25px;
          margin-top: 0;

          .message-content-text {
            max-width: 100%;
            background: #f5f5f7;
            padding: 13px 10px;
            border-radius: 8px;
            position: relative;

            .message-text {
              margin: 0;
              font-size: 14px;
              font-weight: 400;
              line-height: 24px;
              color: #161f2b;
            }

            .message-time {
              font-size: 12px;
              color: #919aa4;
              font-weight: 400;
              margin-top: 5px;
              text-align: right;
            }
          }
        }
      }
    }
  }
}

.chat-input-area {
  padding: 20px 25px;
  border-top: 1px solid #e9ecef;
  background: white;
}

.input-wrapper {
  position: relative;
  border: 1px solid #d6e2ef;
  border-radius: 8px;
  background: white;
  min-height: 100px;
  width: 100%;
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.1);

  .message-textarea {
    width: 100%;
    border: none;
    outline: none;
    padding: 16px;
    font-size: 14px;
    color: #545c66;
    background: transparent;
    resize: none;
    min-height: 70px;
    font-family: inherit;

    &::placeholder {
      color: #9ca3af;
      font-weight: 400;
    }
  }
}

.bottom-actions {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 0 24px 16px 24px;
}

.left-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: #6b7280;
  font-size: 13px;
  padding: 0;
  cursor: pointer;
  font-family: inherit;
}

.action-button:hover {
  color: #374151;
}

.action-button i {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

.chat-input-container {
  margin: 24px;
}

.sent-btn {
  height: 40px;
  font-size: 14px;
  border-radius: 50%;
  width: 40px;
  padding: 0;
}

.credentials-separator {
  font-size: 20px;
  font-weight: 600;
  text-transform: capitalize;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  grid-gap: 20px;

  span {
    white-space: nowrap;
  }
}

.credentials-separator::after {
  background: var(--ot-primary-border);
  width: 100%;
  height: 1px;
  content: "";
  position: relative;
  display: block;
}

/*------------------------------------------------------------------
  76. Ai Chat Boot
--------------------------------------------------------------------*/
.chat-sidebar-toggle {
  top: 56px;
  left: -16px;
}

.sidebar-toggle-btn {
  &.btn-primary-outline {
    padding: 0;
    width: 30px;
    height: 30px;
    font-size: 10px !important;
    border-radius: 15px;

    i {
      font-size: 14px;
    }
  }
}

.chat-container {
  .chat-sidebar {
    transform: translateX(0%);
    transition: transform width opacity 0.3s ease-in-out;
  }

  &.is-open-history {
    .chat-sidebar {
      transition: transform 0.3s ease-in-out;
      transform: translateX(100%);
      margin-left: 0;
      width: 0;
      padding: 0 !important;
      opacity: 0;
      visibility: hidden;
    }
  }
}

.chat-main {
  transition: transform 0.3s ease-in-out;
}

/* Typing indicator */
.typing-indicator {
  display: none;
  background-color: #f5f5f7;
  padding: 15px 20px;
  border-radius: 20px;
  width: fit-content;
  margin: 15px;
  position: relative;

  &.active {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  span {
    height: 8px;
    width: 8px;
    background: #919aa4;
    display: block;
    border-radius: 50%;
    opacity: 0.4;

    &:nth-of-type(1) {
      animation: typing 1s infinite;
    }

    &:nth-of-type(2) {
      animation: typing 1s infinite 0.2s;
    }

    &:nth-of-type(3) {
      animation: typing 1s infinite 0.4s;
    }
  }
}

@keyframes typing {
  0% {
    opacity: 0.4;
    transform: translateY(0px);
  }

  50% {
    opacity: 1;
    transform: translateY(-4px);
  }

  100% {
    opacity: 0.4;
    transform: translateY(0px);
  }
}

/* Empty Chat */
.empty-chat {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100%;
  text-align: center;
  padding: 20px;
  background: #fff;

  .empty-chat-icon {
    width: 180px;
    height: 180px;
    margin-bottom: 24px;
    background: rgba(26, 115, 233, 0.05);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    svg {
      width: 80px;
      height: 80px;
      color: var(--primary-color);
    }
  }

  h5 {
    font-size: 24px;
    font-weight: 600;
    color: var(--ot-primary-text);
    margin-bottom: 12px;
  }

  p {
    font-size: 16px;
    color: var(--ot-subtitle-text);
    max-width: 460px;
    margin-bottom: 24px;
    line-height: 1.5;
  }

  .suggestion-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    justify-content: center;
    margin-top: 20px;

    .suggestion-chip {
      padding: 6px 16px;
      background: rgb(249 250 251);
      border: 1px solid rgb(237 237 237);
      border-radius: 20px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: var(--ot-primary-btn);
        color: white;
        border-color: transparent;
      }
    }
  }
}

/* Sources section */
.sources-section {
  font-size: 0.8rem;
  background-color: rgba(240, 240, 240, 0.5);
  padding: 10px;
  border-radius: 6px;
  margin-top: 15px;

  p.fw-bold {
    color: #555;
    margin-bottom: 5px;
  }

  .sources-list {
    list-style-type: disc;

    li {
      margin-bottom: 8px;
      color: #555;

      strong {
        color: #333;
      }
    }
  }

  .source-preview {
    font-size: 0.75rem;
    color: #666;
    margin-top: 2px;
    margin-left: 5px;
    padding-left: 8px;
    border-left: 2px solid #ddd;
    font-style: italic;
    line-height: 1.3;
  }
}

/*------------------------------------------------------------------
  77. Working Schedule List CSS
--------------------------------------------------------------------*/
.working-schedule-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 266px;
}

.working-schedule-item {
  grid-gap: 10px;

  .vertical-bar {
    width: 2px;
    height: 100%;
    background: var(--ot-primary-border);
    height: 64px;

    &[variant="danger"] {
      background: rgba(var(--danger-color-rgb), 1);
    }

    &[variant="warning"] {
      background: rgba(var(--warning-color-rgb), 1);
    }

    &[variant="primary"] {
      background: rgba(var(--primary-color-rgb), 1);
    }
  }
}

.working-schedule-days {
  .working-schedule-day {
    grid-gap: 10px;
    cursor: pointer;

    .day-name {
      font-size: 14px;
      color: #919aa4;
      font-weight: 400;
      line-height: 1;
    }

    .day-date {
      font-size: 16px;
      color: #161f2b;
      font-weight: 600;
      line-height: 1;
    }

    &.active,
    &:hover {
      .day-name {
        color: #ff991a;
      }

      .day-date {
        color: #ff991a;
      }
    }
  }
}

.comming-soon-img {
  max-width: 300px;
  margin: 0 auto;
  margin-bottom: 30px;

  img {
    width: 100%;
  }
}

.max-width-750 {
  max-width: 750px;
}

.sidebar-menu-item {
  view-transition-name: sidebar-menu-item;
}

.locked-page-form {
  max-width: 400px;
  width: 100%;
}

.locked-page-content {
  max-width: 400px;
  width: 100%;
  flex-shrink: 0;
}

.locked-page-img {
  width: 70px;
  height: 70px;
  flex-shrink: 0;
}

.locked-page-img img {
  object-fit: cover;
}

.locked-page-logo {
  max-width: 180px;
}

.locked-form-inner button {
  padding: 10px;
}

.locked-form-inner .password-input i {
  right: 10px !important;
}

/*------------------------------------------------------------------
    79. Assets Details CSS
--------------------------------------------------------------------*/

.assest-info-box {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 24px;
}

.primary-tabs {
  .nav-link {
    padding: 9px 19px;
    font-size: 16px;
    font-weight: 700;
    text-transform: capitalize;
    border-radius: 4px;
    text-align: center;
    cursor: pointer;
    display: inline-block;
    overflow: hidden;
    border: 1px solid transparent;
    transition: 0.3s;
    display: flex;
    align-items: center;
    height: 40px;
    justify-content: center;
    gap: 8px;
    color: var(--ot-primary-text);

    &.active {
      background: var(--ot-primary-btn);
      color: #fff;
    }
  }
}

/*------------------------------------------------------------------
  80. Complaint Form CSS
--------------------------------------------------------------------*/
.complaint-form-item {
  margin-bottom: 10px;

  >span {
    font-size: 16px;
    font-weight: 500;
    color: var(--ot-primary-text);
  }

  input.ot-input.form-control,
  select.ot-select.form-control {
    border-bottom: 1px solid var(--ot-primary-text) !important;
    border-top: 0 !important;
    border-right: 0 !important;
    border-left: 0 !important;
    border-radius: 0 !important;
  }

  textarea.form-control {
    border: 0 !important;
    padding: 0 !important;
  }

  .complaint-select-box {
    min-width: 200px;

    .select2-container .select2-selection--multiple {
      border-bottom: 1px solid var(--ot-primary-text) !important;
      border: 0;
      border-radius: 0;
    }

    .select2-container--default.select2-container--focus .select2-selection--multiple,
    .select2-container .select2-selection--single {
      border-bottom: 1px solid var(--ot-primary-text) !important;
      border-radius: 0;
      border-top: 0 !important;
      border-right: 0 !important;
      border-left: 0 !important;
    }
  }
}

.h-250 {
  height: 250px !important;
}

.h-70 {
  height: 70px !important;
}

.over-flow-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.profile-complete-status {
  border-radius: 3px;
  padding: 2px 5px;
  line-height: 1;
  height: 15px;
  display: inline-block;
  font-size: 11px;
  min-width: 36px;
  text-align: center;
  margin: 0 auto;
  color: #fff;
  margin: 0;
  font-weight: 700;
  position: absolute;
  right: 10px;
  bottom: -3px;
  background: #f99404;
}

.fc-event,
.fc-event-dot {
  background-color: transparent !important;
  border: 1px solid transparent !important;
}

.fc-toolbar h2 {
  font-size: 20px;
  color: var(--ot-primary-text);
  font-weight: 600;
  margin-bottom: 20px;
  text-transform: capitalize;
}

.is-weekend .fc-title {
  background: #ff6a5454 !important;
  border-color: transparent !important;
  color: #ff6a54;
  padding: 11px 5px;
  border-left: 3px solid #ff6a54 !important;
  border-radius: 0;
  font-weight: 600;
  display: block;
}

.is-holiday .fc-title {
  background: #dbaa5291 !important;
  border-color: transparent !important;
  color: #dbaa52;
  padding: 11px 5px;
  border-left: 3px solid #dbaa52 !important;
  border-radius: 0;
  font-weight: 600;
  display: block;
}

.default-duty .fc-title {
  background: #41ab3536 !important;
  border-color: transparent !important;
  color: #308726;
  padding: 11px 5px;
  border-left: 3px solid #41ab35 !important;
  border-radius: 0;
  font-weight: 600;
  display: block;
}

.is-leave .fc-title {
  background: #ffb3b3 !important;
  /* light red/pink */
  border-color: transparent !important;
  color: #e74c3c;
  /* red text */
  padding: 11px 5px;
  border-left: 3px solid #e74c3c !important;
  border-radius: 0;
  font-weight: 600;
  display: block;
}

.fc-day.is-meal {
  border: 2px dashed green;
}

/*------------------------------------------------------------------
    81. Phone Book CSS
--------------------------------------------------------------------*/
.active-status-employee {
  .profile-image {
    border: 2px solid #16b274 !important;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      width: 14px;
      height: 14px;
      background: #16b274 !important;
      border-radius: 50%;
      right: 0;
      bottom: 2px;
      z-index: 1;
      border: 2px solid #fff;
      box-shadow: rgba(67, 71, 85, 0.27) 0 0 0.25em,
        rgba(90, 125, 188, 0.05) 0 0.25em 1em;
    }
  }
}

.leave-status-employee {
  /* background: rgba(var(--danger-color-rgb), 0.05);
  border-color: rgba(var(--danger-color-rgb), 0.09) !important; */

  .profile-image {
    border: 2px solid rgba(var(--danger-color-rgb), 1) !important;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      width: 14px;
      height: 14px;
      background: rgba(var(--danger-color-rgb), 1);
      border-radius: 50%;
      right: 0;
      bottom: 2px;
      z-index: 1;
      border: 2px solid #fff;
      box-shadow: rgba(67, 71, 85, 0.27) 0 0 0.25em,
        rgba(90, 125, 188, 0.05) 0 0.25em 1em;
    }
  }
}

.break-status-employee {
  /* border-color: rgba(var(--warning-color-rgb), 0.3) !important;
  background: rgba(var(--warning-color-rgb), 0.05) !important; */

  .profile-image {
    border: 2px solid rgba(var(--warning-color-rgb), 1) !important;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      width: 14px;
      height: 14px;
      background: rgba(var(--warning-color-rgb), 1);
      border-radius: 50%;
      right: 0;
      bottom: 2px;
      z-index: 1;
      border: 2px solid #fff;
      box-shadow: rgba(67, 71, 85, 0.27) 0 0 0.25em,
        rgba(90, 125, 188, 0.05) 0 0.25em 1em;
    }
  }
}

.inactive-status-employee {
  /* background-color: var(--primary-bg) !important;
  border-color: #e4e4e4 !important; */

  .profile-image {
    border: 2px solid rgba(var(--warning-color-rgb), 1) !important;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      width: 14px;
      height: 14px;
      background: rgba(var(--warning-color-rgb), 1);
      border-radius: 50%;
      right: 0;
      bottom: 2px;
      z-index: 1;
      border: 2px solid #fff;
      box-shadow: rgba(67, 71, 85, 0.27) 0 0 0.25em,
        rgba(90, 125, 188, 0.05) 0 0.25em 1em;
    }
  }
}

.hover-border {
  &:hover {
    border: 1px solid rgba(var(--primary-color-rgb), 0.4) !important;
    cursor: pointer;
  }
}

/*------------------------------------------------------------------
  82. Complaint Form CSS
--------------------------------------------------------------------*/
.list {
  margin-left: 15px;
  margin-bottom: 0;

  .list-items {
    list-style: auto;
  }
}

.accordion-style2 {

  .accordion-item,
  .accordion-button {
    background-color: transparent !important;
    border: 1px solid transparent !important;
  }

  .accordion-button:not(.collapsed) {
    color: var(--ot-bg-badge-primary) !important;
    background-color: transparent !important;
    box-shadow: none !important;
  }

  .accordion-button:focus {
    box-shadow: none !important;
    border: 1px solid transparent !important;
  }
}

.hover-buttons:hover {
  .btn-primary-outline {
    background: var(--ot-primary-btn);
    color: #fff;
  }
}

.hover-bg:hover {
  background: var(--primary-bg) !important;
}

.text-editor-image {
  img {
    width: 100% !important;
  }
}

/*------------------------------------------------------------------
  83. Complaint Form CSS
--------------------------------------------------------------------*/
.header-bottom-bar {
  height: 35px;
  position: fixed;
  top: 0;
  background: #f0f3f5;
  width: 100%;
  z-index: 99999;
  border-bottom: 1px solid var(--ot-primary-border);
}

.marquee-wrapper {
  grid-gap: 10px;
  min-height: 35px;

  .marquee-Heading {
    min-height: 35px;
    background: var(--ot-primary-btn);
    color: #fff;
    max-width: 95px;
    min-width: 94px;
    text-align: center;
    font-size: 16px;
    font-weight: 600;
    width: 100%;
    flex: 178px 0 0;
    align-items: center;
    display: inline-flex;
    justify-content: center;
  }

  .marquee-list {
    grid-gap: 40px;
    overflow: hidden;

    .marquee-item {
      position: relative;
      display: flex;
      padding-left: 32px;

      .marquee-img {
        width: 24px;
        position: absolute;
        left: 0;
        top: -4px;
      }
    }
  }
}

.marquee-text {
  font-size: 14px;
  font-weight: 500;
  color: #000;
  height: 23px;
  overflow: hidden;
}

.webTicker-item {
  position: relative;
  display: flex;
  padding-left: 32px;

  .marquee-text {
    display: flex;
    align-items: center;
    grid-gap: 6px;
    top: 4px;
    position: relative;
    white-space: nowrap;
    font-size: 18px;
    font-weight: 400;
    color: #666;

    i {
      font-size: 20px;
      position: relative;
      top: 0;
    }
  }
}

.single-theme-label {
  padding: 10px;
  border: 2px solid var(--ot-primary-border);
  border-radius: 12px;
  cursor: pointer;

  &.active {
    border-color: var(--primary-color);

    .form-check-label {
      color: var(--primary-color) !important;
    }
  }

  .check-box {
    position: absolute;
    bottom: -50px;
    right: 0;
    left: 0;
    text-align: center;
    margin: 0 auto;

    /*noinspection CssUnknownTarget*/

    .form-check-input:checked {
      background: url("/assets/images/ok.svg"), var(--primary-color);
      background-repeat: no-repeat;
      background-position: center;
      border: none;
    }

    .form-check-input {
      border-radius: 50% !important;
    }
  }
}

/*------------------------------------------------------------------
   84. Color picker
--------------------------------------------------------------------*/

.pic-color-icon {
  position: absolute;
  right: 10px;
  top: 6px;
  font-size: 22px;
  color: #9b9b9b;
  cursor: pointer;
  z-index: 1;
  padding: 5px;
}

.changingColor {
  font-size: 16px;
}

.show-color {
  display: flex;
  gap: 10px;
  font-size: 15px;
}

.input-box-color-pic {
  height: 40px;
  border: 1px solid var(--ot-primary-border) !important;
  border-radius: 5px;
}

.input-box-color-pic {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sp-dd {
  display: none;
}

.show-color {
  position: absolute;
  left: 1px;
  top: 2px;
  font-size: 22px;
  cursor: pointer;
  z-index: 1;
  padding: 5px;
}

.sp-replacer {
  background: none;
  border: 0;
}

.colorBox {
  display: inline-block;
  vertical-align: middle;
  margin: 2px;
  width: 20px;
  height: 20px;
  border: 1px solid #ddd;
  border-radius: 3px;
  box-sizing: border-box;
}

.sp-replacer.sp-light {
  position: absolute;
  right: 0;
  top: 5px;
}

.sp-preview {
  width: 20px;
  border: 0;
}

/*------------------------------------------------------------------
   85. Custom Date Picker
--------------------------------------------------------------------*/

.theme-datePicker {
  .datepicker {
    width: 100%;

    .datepicker-picker {
      background-color: var(--ot-bg-secondary);
      border: 1px solid var(--ot-secondary-border);
      border-radius: 8px;
      padding: 24px;
      display: flex;
      flex-direction: column;

      .datepicker-header {
        display: flex;
        justify-content: space-between;
        align-items: start;
        gap: 10px;
        padding-bottom: 24px;

        .button {
          font-size: 16px;
          border-radius: 10px;
          padding: 8px 14px;
          text-transform: uppercase;
          height: 40px;
        }

        .view-switch {
          border: 1px solid var(--ot-secondary-border);
          background-color: transparent;
          color: var(--ot-primary-text);
          font-size: 14px;
        }

        .prev-button,
        .next-button {
          cursor: pointer;
          overflow: hidden;
          background: none;
          border: 1px solid var(--ot-secondary-border);
          border-radius: 10px;
          padding: 0;
          width: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-basis: 40px;

          &:hover {
            background-color: inherit;
          }
        }
      }

      .datepicker-main {
        border: 1px dashed var(--ot-secondary-border);
        border-radius: 8px;
        padding: 24px;

        .datepicker-view {
          width: 100%;

          .days-of-week {
            display: flex;
            text-transform: uppercase;
            padding: 0 0 14px 0;
          }

          .datepicker-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            grid-row-gap: 10px;
            grid-column-gap: 10px;

            span {
              border: 1px solid var(--ot-secondary-border);
              background-color: var(--ot-bg-secondary);
              color: var(--ot-subtitle-text);
              font-weight: 600;
              border-radius: 8px;
              height: 40px;
            }
          }

          .datepicker-cell {
            &.disabled {
              background-color: #f5f5f7;
              color: #cdd0d6;
            }

            &.today,
            &.today.selected,
            &.today.selected:hover {
              border: 1px solid var(--primary-color) !important;
              background-color: var(--primary-color) !important;
              color: var(--ot-bg-secondary) !important;
            }

            &.next,
            &.prev {
              color: var(--ot-secondary-border) !important;
            }

            &.selected,
            &.selected:hover {
              border: 1px solid var(--ot-bg-secondary) !important;
              background-color: #e9f1fd !important;
              color: var(--ot-subtitle-text) !important;
            }
          }

          &.decades,
          &.months,
          &.years {
            padding: 10px 0;
          }
        }
      }
    }

    .datepicker-title {
      font-weight: 600;
      font-size: 20px;
      color: var(--ot-primary-text);
      background-color: transparent;
      box-shadow: none;
      padding: 0;
    }

    .left-group,
    .right-group {
      display: flex;
      align-items: center;
      gap: 6px;
    }
  }
}

/*------------------------------------------------------------------
   86. Mobile Menu Footer - SCSS Format
--------------------------------------------------------------------*/

.mobile-menu-footer {
  display: none;

  @media (max-width: 768px) {
    display: block;
  }
}

@media (max-width: 575px) {
  .footer-wrapper {
    padding-bottom: 80px;
  }
}

.footer-mobile-wrapper {
  box-shadow: rgba(0, 0, 0, 0.15) 0px 5px 15px 0px;
  /* background: #f5f5f7; */
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(4px);

  position: fixed;
  margin-bottom: 0;
  z-index: 99;
  border-radius: 16px;
  line-height: 1;
  height: 66px;
  bottom: 5px;
  left: 10px;
  right: 10px;
  padding: 5px 10px;

  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(var(--primary-color-rgb), 0.1);

  .listing {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 0;
    margin-bottom: 0;

    .single-list {
      list-style: none;

      a {
        text-align: center;
        display: block;
        padding: 8px 12px;
        color: var(--ot-primary-text);
        display: flex;
        align-items: center;
        gap: 2px;

        border-radius: 27px;

        &.active {
          color: var(--ot-primary-text);
          background: var(--ot-primary-btn);
          /* @media (max-width: 550px) {
                        display: block;
                    } */

          .title {
            color: #fff;
            display: block;
          }

          svg {
            color: #fff !important;
          }
        }

        .title {
          display: block;
          font-size: 14px;
          font-weight: 600;
          text-transform: capitalize;
          color: var(--ot-primary-text);
          width: 50px;
          line-height: 1;
          display: none;
        }
      }
    }
  }

  .counter {
    position: relative;

    .count {
      position: absolute;
      background: #ff0000;
      color: #fff !important;
      width: 16px !important;
      height: 16px;
      border-radius: 50%;
      display: block !important;
      top: -7px;
      right: -4px;
      line-height: 16px !important;
      font-size: 10px !important;
    }
  }
}

.ml-lg-0 {
  @media (max-width: 1399.98px) {
    margin-left: 0 !important;
  }

  @media (max-width: 992px) {
    margin-left: 0 !important;
  }
}

@media (max-width: 992px) {
  body.rtl .header {
    padding: 10px;
    right: 0;
  }

  .sidebar .half-expand-toggle {
    display: none;
  }

  .ot--datePicker .datepicker--nav-title {
    font-size: 16px;
  }
}

@media (max-width: 767.99px) {
  body.rtl .sidebar {
    right: -100%;
    transition: right 0.3s;
  }

  body.rtl .main-content {
    margin-right: 0;
  }

  .half-expand .sidebar .sidebar-header .sidebar-close {
    display: block;
  }

  .frame {
    display: none !important;
  }

  .form-wrapper .form-content .form .input-field-focus {
    width: 100%;
  }

  body.rtl .header .header-controls .header-control-item {
    margin-left: 0;
    margin-right: auto;
  }

  body.rtl .header .header-controls {
    flex: 1 0 0;
  }
}

@media (max-width: 576px) {
  .form-wrapper .form-content .form .input-field-focus input {
    height: 40px;
    padding: 10px;
  }

  .text-sm-14 {
    font-size: 14px !important;
  }

  .border-left-success-sm-transparent {
    border-left: 3px solid transparent;
  }

  .p-sm-0 {
    padding: 0 !important;
  }
}

@media (max-width: 480px) {

  .form-container,
  .auth-form {
    width: 280px !important;
  }
}

/*  */

.custom-dropdown {
  width: 300px;
  position: relative;
  user-select: none;
}

.dropdown-header {
  background: #eaf2ff;
  border-radius: 10px;
  padding: 15px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.branch-info {
  display: flex;
  gap: 10px;
  align-items: center;
}

.branch-icon {
  width: 30px;
  height: 30px;
  background: #fff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

/* Colored icons */
.icon-blue {
  background-color: #d0e2ff;
}

.icon-green {
  background-color: #d0f5e2;
}

.icon-yellow {
  background-color: #fff5d0;
}

.icon-red {
  background-color: #ffd0d0;
}

.icon-purple {
  background-color: #e5d0ff;
}

.branch-title {
  margin: 0;
  font-weight: 600;
  font-size: 14px;
}

.branch-subtitle {
  margin: 0;
  font-size: 12px;
  color: gray;
}

.arrow {
  font-size: 14px;
}

.dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 8px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  list-style: none;
  padding: 0;
  display: none;
  z-index: 99;
}

.dropdown-list li {
  padding: 10px 15px;
  cursor: pointer;
}

.dropdown-list li:hover {
  background-color: #f4f7fb;
}

.branch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.branch-item .check-icon {
  display: none;
  color: green;
  font-size: 14px;
}

.branch-item.selected .check-icon {
  display: inline;
}

/*------------------------------------------------------------------
   87. Custom Dropdown
--------------------------------------------------------------------*/

.customDrop {
  width: 256px;
  position: relative;
  user-select: none;
  margin: 0 auto;
  margin-top: 21px;
  padding-bottom: 5px;
}

.sidebar-branch {
  background: #fff;
  position: relative;
  z-index: 9991;
}

.customDrop-selected {
  background: rgba(var(--primary-color-rgb), 0.1);
  border: 1px solid rgba(var(--primary-color-rgb), 0.2);
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: border-color 0.3s ease;

  &:hover {
    border-color: rgba(var(--primary-color-rgb), 0.5);
  }
}

.customDrop-selected-info {
  display: flex;
  align-items: center;
  gap: 10px;

  .customDrop-icon {
    flex-shrink: 0;
    border-radius: 4px;
    width: 32px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(var(--primary-color-rgb), 0.2);
    color: rgba(var(--primary-color-rgb), 1);
    border-color: rgba(var(--primary-color-rgb), 1);
  }
}

.selected-texts {
  display: flex;
  flex-direction: column;

  .selected-title {
    font-weight: 600;
    font-size: 14px;
    color: var(--ot-primary-text);
  }

  .selected-subtitle {
    font-size: 12px;
    color: var(--ot-subtitle-text);
  }
}

.customDrop-menu {
  position: absolute;
  top: calc(100% + 8px);
  left: 0;
  width: 100%;
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgb(20 20 20 / 0.1);
  border: 1px solid #d7e3fd;
  max-height: 220px;
  padding: 10px 10px;
  overflow-y: auto;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  pointer-events: none;

  .item-title {
    font-weight: 600;
    font-size: 14px;
    color: #344054;
  }

  .item-subtitle {
    font-size: 12px;
    color: var(--ot-subtitle-text);
  }
}

.customDrop.open .customDrop-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  pointer-events: auto;
}

.customDrop-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  border-radius: 10px;
  gap: 10px;
  transition: background-color 0.2s ease;
  user-select: none;
  margin-bottom: 5px;

  &:nth-child(1) {
    .customDrop-item-icon {
      background-color: rgba(var(--primary-color-rgb), 0.1);
      color: rgba(var(--primary-color-rgb), 1);
      border-color: rgba(var(--primary-color-rgb), 1);
    }
  }

  &:nth-child(2) {
    .customDrop-item-icon {
      background-color: rgba(var(--success-color-rgb), 0.1);
      color: rgba(var(--success-color-rgb), 1);
      border-color: rgba(var(--success-color-rgb), 0.1);
    }
  }

  &:nth-child(3) {
    .customDrop-item-icon {
      background-color: rgba(var(--warning-color-rgb), 0.1);
      color: rgba(var(--warning-color-rgb), 1);
      border-color: rgba(var(--warning-color-rgb), 0.1);
    }
  }

  &:nth-child(4) {
    .customDrop-item-icon {
      background-color: rgba(var(--info-color-rgb), 0.1);
      color: rgba(var(--info-color-rgb), 1);
      border-color: rgba(var(--info-color-rgb), 0.1);
    }
  }

  &:nth-child(5) {
    .customDrop-item-icon {
      background-color: rgba(var(--tertiary-color-rgb), 0.1);
      color: rgba(var(--tertiary-color-rgb), 1);
      border-color: rgba(var(--tertiary-color-rgb), 0.1);
    }
  }

  &:nth-child(6) {
    .customDrop-item-icon {
      background-color: rgba(var(--secondary-color-rgb), 0.1);
      color: rgba(var(--secondary-color-rgb), 1);
      border-color: rgba(var(--secondary-color-rgb), 0.1);
    }
  }

  &:nth-child(7) {
    .customDrop-item-icon {
      background-color: rgba(var(--primary-color-rgb), 0.2);
      color: rgba(var(--primary-color-rgb), 1);
      border-color: rgba(var(--primary-color-rgb), 1);
    }
  }

  .customDrop-item-icon {
    border-radius: 6px;
    width: 32px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    display: flex;
    background-color: rgba(var(--primary-color-rgb), 0.2);
    color: rgba(var(--primary-color-rgb), 1);
    border-color: rgba(var(--primary-color-rgb), 1);
  }

  /* Texts for item */

  .item-texts {
    display: flex;
    flex-direction: column;
    flex-grow: 1;

    .item-title {
      font-weight: 600;
      font-size: 14px;
      color: var(--ot-primary-text);
    }

    .item-subtitle {
      font-size: 12px;
      color: var(--ot-subtitle-text);
    }
  }

  &:hover {
    background: var(--ot-bg-primary);
  }

  &.selected {
    background: transparent;

    .checkmark {
      visibility: visible;
    }
  }
}

.checkmark {
  width: 18px;
  height: 18px;
  fill: var(--ot-bg-primary);
  flex-shrink: 0;
  visibility: hidden;
}

.half-expand {
  .sidebar-branch {
    display: none;
  }

  .customDrop {
    width: 75px;
  }

  .sidebar-dropdown-menu {
    border: 0 !important;
    padding-top: 0 !important;
  }

  .selected-texts {
    display: none;
  }

  .customDrop-item-icon {
    display: none;
  }
}

/*------------------------------------------------------------------
  Custom Radio Style
--------------------------------------------------------------------*/

.radio-wrap {

  input[type="radio"]:checked,
  input[type="radio"]:not(:checked) {
    position: absolute;
    left: -9999px;
  }

  input[type="radio"]:checked+label,
  input[type="radio"]:not(:checked)+label {
    position: relative;
    padding-left: 28px;
    cursor: pointer;
    line-height: 20px;
    display: inline-block;
    color: var(--ot-primary-text);
    font-weight: 300;
    font-size: 1rem;
    margin-bottom: 8px;
  }

  input[type="radio"]:checked+label:before,
  input[type="radio"]:not(:checked)+label:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 20px;
    height: 20px;
    border: 1px solid #ddd;
    border-radius: 100%;
    background: #fff;
  }

  input[type="radio"]:checked+label:after,
  input[type="radio"]:not(:checked)+label:after {
    content: "";
    width: 12px;
    height: 12px;
    background: var(--ot-primary-btn);
    position: absolute;
    top: 4px;
    left: 4px;
    border-radius: 100%;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
  }

  input[type="radio"]:not(:checked)+label:after {
    opacity: 0;
    -webkit-transform: scale(0);
    transform: scale(0);
  }

  input[type="radio"]:checked+label:after {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}