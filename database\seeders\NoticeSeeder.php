<?php

namespace Database\Seeders;

use App\Models\Department;
use App\Models\Notice;
use Illuminate\Database\Seeder;

class NoticeSeeder extends Seeder
{
    public function run()
    {
        $departmentIds = Department::pluck('id')->toArray();

        // generate between 60–70 notices
        $totalNotices = rand(60, 70);

        for ($i = 1; $i <= $totalNotices; $i++) {
            $notice = Notice::create([
                'title' => "Dummy Notice $i",
                'date' => now()->subDays(rand(0, 30))->toDateString(),
                'expire_date' => now()->addDays(rand(5, 15))->toDateString(),
                'description' => "This is the description for dummy notice $i.",
                'status' => 'active',
                'created_by' => 1,
                'company_id' => 1,
                'branch_id' => 1,
            ]);

            if (! empty($departmentIds)) {
                $notice->departments()->sync($departmentIds);
            }
        }

        $this->command->info("✅ Notices seeded: $totalNotices");
    }
}
