<?php

namespace Modules\EmployeePerformance\Repositories;

use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Modules\EmployeePerformance\Entities\Award;
use Modules\EmployeePerformance\Entities\AwardType;

class AwardRepository
{
    public function __construct(
        protected Award $model,
        protected User $userModel,
        protected AwardType $awardTypeModel
    ) {}

    public function getPaginateData($request, $fields = ['*']): LengthAwarePaginator
    {
        return $this->model::select($fields)->latest('id')
            ->with(['user', 'type', 'createdBy'])
            ->when($request->search, function ($query) use ($request) {
                $query->where(function ($q) use ($request) {
                    $q->where('description', 'like', '%'.$request->search.'%')
                        ->orWhereHas('user', function ($userQuery) use ($request) {
                            $userQuery->where('name', 'like', '%'.$request->search.'%');
                        })
                        ->orWhereHas('type', function ($typeQuery) use ($request) {
                            $typeQuery->where('name', 'like', '%'.$request->search.'%');
                        });
                });
            })
            ->when($request->status, function ($query) use ($request) {
                $query->where('status', $request->status);
            })
            ->when($request->user_id, function ($query) use ($request) {
                $query->where('user_id', $request->user_id);
            })
            ->when($request->award_type_id, function ($query) use ($request) {
                $query->where('award_type_id', $request->award_type_id);
            })
            ->paginate($request->limit ?? 10);
    }

    /**
     * Get all employees for award assignment
     *
     * @return Collection
     */
    public function employees()
    {
        return $this->userModel->where('status', 1)
            ->select('id', 'name', 'email', 'designation_id', 'department_id')
            ->with(['designation:id,title', 'department:id,title'])
            ->get();
    }

    public function types()
    {
        return $this->awardTypeModel->where('status', 'active')->get();
    }

    public function availableAwardTypes()
    {
        $userAwardTypeIds = $this->model
            ->where('user_id', Auth::id())
            ->pluck('award_type_id')
            ->toArray();

        return $this->awardTypeModel
            ->where('status', 'active')
            ->whereNotIn('id', $userAwardTypeIds)
            ->get();
    }

    public function store(array $data)
    {
        DB::beginTransaction();
        try {
            $data['created_by'] = Auth::id();
            $data['company_id'] = Auth::user()->company_id;
            $data['branch_id'] = Auth::user()->branch_id;
            $data['show_popup'] = $data['show_popup'] ?? true;

            // Calculate expire_date based on award_type duration (only for new awards)
            if (! isset($data['expire_date']) || empty($data['expire_date'])) {
                $awardType = $this->awardTypeModel->find($data['award_type_id']);
                if ($awardType && $awardType->duration) {
                    $data['expire_date'] = Carbon::parse($data['date'])->addDays($awardType->duration);
                }
            }

            $created = $this->model->create($data);

            if ($created) {
                DB::commit();

                return $created;
            }
        } catch (Exception $e) {
            DB::rollBack();

            return catchHandler($e);
        }
    }

    public function show($id)
    {
        return $this->model->with(['user', 'type', 'createdBy', 'updatedBy'])->findOrFail($id);
    }

    public function update($id, array $data)
    {
        DB::beginTransaction();
        try {
            $award = $this->model->findOrFail($id);
            $data['updated_by'] = Auth::id();
            $data['show_popup'] = $data['show_popup'] ?? $award->show_popup;

            // Handle expire_date: recalculate only if award_type or date changed AND no expire_date provided
            if ((isset($data['award_type_id']) || isset($data['date'])) && ! isset($data['expire_date'])) {
                $awardType = $this->awardTypeModel->find($data['award_type_id'] ?? $award->award_type_id);
                if ($awardType && $awardType->duration) {
                    $awardDate = $data['date'] ?? $award->date;
                    $data['expire_date'] = Carbon::parse($awardDate)->addDays($awardType->duration);
                }
            }
            // If expire_date is provided in the request, use it (admin override)
            elseif (isset($data['expire_date']) && ! empty($data['expire_date'])) {
                // Keep the provided expire_date as is
            }
            // If no expire_date provided and no recalculation needed, keep the existing one
            else {
                unset($data['expire_date']);
            }

            $updated = $award->update($data);

            if ($updated) {
                DB::commit();

                return $award->fresh();
            }
        } catch (Exception $e) {
            DB::rollBack();

            return catchHandler($e);
        }
    }

    public function delete($id)
    {
        DB::beginTransaction();
        try {
            $award = $this->model->findOrFail($id);
            $deleted = $award->delete();

            if ($deleted) {
                DB::commit();

                return true;
            }
        } catch (Exception $e) {
            DB::rollBack();

            return catchHandler($e);
        }
    }

    public function getUnseenAward()
    {
        try {
            $model = $this->model
                ->where('user_id', Auth::id())
                ->where('show_popup', true)
                ->where('status', 'active')
                ->where(function ($query) {
                    $query->whereNull('expire_date')
                        ->orWhere('expire_date', '>=', now());
                })
                ->where('date', '<=', now())
                ->latest()
                ->first();

            return [
                'id' => $model->id ?? null,
                'badge' => $model->type->badge_icon ?? null,
            ];
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function seenAward($request)
    {
        try {
            return $this->model
                ->where('user_id', Auth::id())
                ->where('id', $request->id)
                ->update(['show_popup' => false]);
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function getAwardsByUser($userId)
    {
        return $this->model->where('user_id', $userId)
            ->with(['type', 'createdBy'])
            ->latest()
            ->get();
    }

    public function getExpiredAwards()
    {
        return $this->model->where('expire_date', '<', now())
            ->where('status', 'active')
            ->with(['user', 'type'])
            ->get();
    }
}
