/*--------------------------------------------------------------------------------------------------------------------------------------------------------
    Index Of JS File
----------------------------------------------------------------------------------------------------------------------------------------------------------

    @Version            : 1.0.0
    @Application Name   : Onest HRM
    @Application Author : www.onesttech.com


    1. Sidebar MetisMenu with Intersection Observer    20. Show Approve Confirmation Modal             39. Check-in via Geolocation API                        
    2. Sidebar Toggle (Half Expand & Close)            21. Show Complete Confirmation Modal            40. Geolocation Error Handler       
    3. Current Time Function with Clock Display        22. Select2 Initialization                      41. Store Attendance Location via AJAX           
    4. Add Duty Calendar Modal with Date Range         23. Terms & Conditions Checkbox Toggle          42. Auto-hide Bootstrap Tooltips after Page Load              
    5. Stop Propagation of Dropdown Menus              24. Account Statement AJAX Fetch & Render       43. AJAX-powered Select2 for Custom User Selection   
    6. Bootstrap Tooltip Initialization                25. Vehicle Status Update Button Handler        44. AJAX-powered Select2 for Performance Goals       
    7. Base URL Configuration                          26. File Upload Preview Handler                 45. Initialize and Handle Date Range Picker                    
    8. CSRF Token Setup                                27. Select Users by Department with AJAX        46. AJAX-powered Select2 for Getting Members User List 
    9. Currency Symbol Setup                           28. Initialize Payroll Salary Advance Data      47. AJAX-powered Select2 with Search for Employ       
    10. Date Range Picker Hide/Show Logic              29. Initialize Leave Request Data               48. Single Date Picker for Start Date              
    11. SweetAlert2 Toast Setup                        30. Initialize Visit Data                       49. Single Date Picker for Expire Date with Clear Option               
    12. SweetAlert2 - Global Delete Confirmation       31. Initialize Break History Data               50. Set Formatted Date on Expire Date Picker Apply             
    13. SweetAlert2 - General Delete Alert             32. Initialize Visit Report Data                51. Single Time Picker Setup for .s_time Inputs     
    14. SweetAlert2 - Approve/Reject Confirmation      33. Load Users by Department with AJAX          52. AJAX Language Change on #change-user-lang Select                  
    15. SweetAlert2 - Generic Reusable Confirmation    34. AJAX-powered Select2 for User Selection     53. Initialize Select2 on .company-select with custom container class       
    16. SweetAlert2 - HR Admin Action Confirmation     35. AJAX-powered Select2 for Attendance         54. Initialize Select2 on .branch-select with custom container class                  
    17. Close All Existing Modals and Backdrops        36. Load & Display Break Modal with Navigation  55. Toggle Menu Input Visibility Based on Menu Type       
    18. Load Modal Content Dynamically via AJAX        37. Load & Display Break Start Modal            56. File Uploader with Multiple Inputs                 
    19. Show Reject Confirmation Modal                 38. Load & Show Main Modal via AJAX             57. Confirmation to Restore Employee (SweetAlert2)  
                                                                                                       58. No Close On Backdrop for Offcanvas

----------------------------------------------------------------------------------------------------------------------------------------------------------
    End-of JS File
----------------------------------------------------------------------------------------------------------------------------------------------------------*/

// (function ($) {
//     "use strict";

/*------------------------------------------------------------------
    1. Sidebar MetisMenu with Intersection Observer
--------------------------------------------------------------------*/
let interSectedLastItemOfElement = false;
const identifyLastItemOfMenuElement = (function () {
    const init = function () {
        createObserver();
    };

    const createObserver = function () {
        let options = {
            root: document.querySelector("#sidebar"),
            rootMargin: "0px 0px -200px 0px",
            threshold: 1.0,
        };

        let observer = new IntersectionObserver(function (entries, observer) {
            handleIntersect(entries, observer);
        }, options);

        let targetElements = document.querySelectorAll(
            ".sidebar-menu-item:last-child"
        );

        targetElements.forEach((targetElement) => {
            observer.observe(targetElement);
        });
    };
    const handleIntersect = function (entries, observer) {
        entries.forEach((entry) => {
            if (entry.isIntersecting) {
                interSectedLastItemOfElement = true;
            }
        });
    };

    return {
        init,
    };
})();
identifyLastItemOfMenuElement.init();
$(document).ready(function () {
    let sidebarOffsetTop = $(".sidebar-menu").offset().top;
    let scrollTop = sidebarOffsetTop;
    $(".sidebar").on("wheel", function (event) {
        let direction = event.originalEvent.deltaY;

        if (direction > 0) {
            scrollTop = scrollTop - 10;

            if (!interSectedLastItemOfElement) {
                $(".sidebar-menu").css("top", `${scrollTop}px`);
            }
        } else {
            if (sidebarOffsetTop > scrollTop) {
                interSectedLastItemOfElement = false;
                scrollTop = scrollTop + 10;
                $(".sidebar-menu").css("top", `${scrollTop}px`);
            }
        }
    });
    let current = location.pathname.split("/")[1];
    $(".sidebar-dropdown-menu li a").each(function () {
        let $this = $(this);

        if ($this.attr("href") == current) {
            $this.parents(".child-menu-list").addClass("mm-show");
            $this.parents(".sidebar-menu-item").addClass("mm-active");

            // scroll to selected menu item start

            let $container = $(".sidebar-menu");
            let $scrollTo = $this.parents(".sidebar-menu-item .mm-active");

        }
    });
});

// MetisMenu Active
$(".sidebar-dropdown-menu").metisMenu();


/*------------------------------------------------------------------
    2. Sidebar Toggle (Half Expand & Close)
--------------------------------------------------------------------*/
$(".half-expand-toggle").on("click", function () {
    scrollTop = $(".sidebar-menu").offset().top;
    $("#layout-wrapper").toggleClass("half-expand");
});
$(".close-toggle").on("click", function () {
    $("#layout-wrapper").toggleClass("sidebar-expand");
});

/*------------------------------------------------------------------
    3. Current Time Function with Clock Display
--------------------------------------------------------------------*/
$(document).ready(function () {
    function currentTime() {
        let date = new Date();
        let hh = date.getHours();
        let mm = date.getMinutes();
        let ss = date.getSeconds();
        let session = "AM";

        if (hh === 0) {
            hh = 12;
        }
        if (hh == 12) {
            session = "PM";
        }
        if (hh > 12) {
            hh = hh - 12;
            session = "PM";
        }

        hh = hh < 10 ? "0" + hh : hh;
        mm = mm < 10 ? "0" + mm : mm;
        ss = ss < 10 ? "0" + ss : ss;

        let time = hh + ":" + mm + ":" + ss + " " + session;
        $(".clock").html(time);
        // document.getElementById("clock").innerText = time;
        let t = setTimeout(function () {
            currentTime();
        }, 1000);
    }
    currentTime();
});


/*------------------------------------------------------------------
    4. Add Duty Calendar Modal with Date Range
--------------------------------------------------------------------*/
identifyLastItemOfMenuElement.init();
$(document).ready(function () {
    updateConfigText();

    function updateConfigText() {
        var options = {};
        if ($("#dateLimit").is(":checked")) options.dateLimit = { days: 7 };
        $("#config-text").val(
            "$('#demo').daterangepicker(" +
            JSON.stringify(options, null, "    ") +
            ", function(start, end, label) {\n  console.log(\"New date range selected: ' + start.format('YYYY-MM-DD') + ' to ' + end.format('YYYY-MM-DD') + ' (predefined range: ' + label + ')\");\n});"
        );
    }

    // Modal trigger — initialize when modal is opened
    $("#addDutyCalendarModal").on("shown.bs.modal", function () {
        var options = {};
        if ($("#dateLimit").is(":checked")) options.dateLimit = { days: 7 };

        $(".daterange-table-filter")
            .daterangepicker(options, function (start, end, label) {
                console.log(
                    "New date range selected: " +
                    start.format("YYYY-MM-DD") +
                    " to " +
                    end.format("YYYY-MM-DD") +
                    " (predefined range: " +
                    label +
                    ")"
                );
            })
            .click(); // Optional: trigger click if needed
    });
});


/*------------------------------------------------------------------
    5. Stop Propagation of Dropdown Menus
--------------------------------------------------------------------*/
$("body").on("click", function (e) {
    if (
        !$(".dropdown-menu").is(e.target) &&
        $(".dropdown-menu").has(e.target).length === 0
    ) {
        // console.log("clicked outside");
    } else {
        e.stopPropagation();
    }
});


/*------------------------------------------------------------------
    6. Bootstrap Tooltip Initialization
--------------------------------------------------------------------*/
$(document).ready(function () {
    $('[data-bs-toggle="tooltip"]').each(function () {
        new bootstrap.Tooltip(this);
    });
});

/*------------------------------------------------------------------
    7. Base URL Configuration
-------------------------------------------------------------------*/
var url = $('meta[name="base-url"]').attr("content");


/*------------------------------------------------------------------
    8. CSRF Token Setup
-------------------------------------------------------------------*/
var _token = $('meta[name="csrf-token"]').attr("content");


/*------------------------------------------------------------------
    9. Currency Symbol Setup
-------------------------------------------------------------------*/
// global delete method where we try to use this for every delete
var currency_symbol = $("#currency_symbol").val();


/*------------------------------------------------------------------
    10. Date Range Picker Hide/Show Logic
--------------------------------------------------------------------*/
window.onload = function () {
    $(".daterangepicker").css("display", "none");
};
$(".daterangepicker").on("click", function () {
    $(this).css("display", "block");
});

/*------------------------------------------------------------------
    11. SweetAlert2 Toast Setup 
-------------------------------------------------------------------*/
const Toast = Swal.mixin({
    toast: true,
    position: "top-right",
    animation: false,
    iconColor: "white",
    customClass: {
        popup: "colored-toast",
    },
    showConfirmButton: false,
    timer: 1500,
    timerProgressBar: true,
    didOpen: (toast) => {
        toast.addEventListener("mouseenter", Swal.stopTimer);
        toast.addEventListener("mouseleave", Swal.resumeTimer);
    },
});


/*------------------------------------------------------------------
    12. SweetAlert2 - Global Delete Confirmation
--------------------------------------------------------------------*/
GlobalApproveId = (id, ur, title) => {
    Swal.fire({
        title: "Are you sure?",
        icon: "success",
        showCancelButton: true,
        confirmButtonText: title,
        cancelButtonText: "Cancel",
        reverseButtons: true,
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = `${url + "/" + ur + id}`;
        } else if (result.dismiss === Swal.DismissReason.cancel) {
            Swal.fire("Cancelled");
        }
    });
};


/*------------------------------------------------------------------
    13. SweetAlert2 - General Delete Alert
--------------------------------------------------------------------*/
GlobalApprove = (ur, title) => {
    Swal.fire({
        title: "Are you sure?",
        icon: "success",
        showCancelButton: true,
        confirmButtonText: title,
        cancelButtonText: "Cancel",
        reverseButtons: true,
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = ur;
        } else if (result.dismiss === Swal.DismissReason.cancel) {
            Swal.fire("Cancelled");
        }
    });
};


/*------------------------------------------------------------------
    14. SweetAlert2 - Approve/Reject Confirmation
--------------------------------------------------------------------*/
ApproveOrReject = (id, status, ur, title) => {
    Swal.fire({
        title: "Are you sure?",
        icon: "success",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
        reverseButtons: true,
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = `${url + "/" + ur + id + "/" + status}`;
        } else if (result.dismiss === Swal.DismissReason.cancel) {
            // Swal.fire(
            //     'Cancelled',
            // )
        }
    });
};

/*------------------------------------------------------------------
    15. SweetAlert2 - Generic Reusable Confirmation
--------------------------------------------------------------------*/
GlobalSweetAlert = (title, text, icon, button, go_url) => {
    Swal.fire({
        title: title,
        text: text,
        icon: icon,
        showCancelButton: true,
        confirmButtonText: button,
        cancelButtonText: $("#cancel").val(),
        reverseButtons: true,
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = go_url;
        } else if (result.dismiss === Swal.DismissReason.cancel) {
        }
    });
};

/*------------------------------------------------------------------
    16. SweetAlert2 - HR Admin Action Confirmation
--------------------------------------------------------------------*/
MakeHrByAdmin = (id, status, ur, title) => {
    let new_url = `${url + "/" + status + id}`;
    // console.log(new_url);

    Swal.fire({
        title: "Are you sure?",
        icon: "success",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
        reverseButtons: true,
    }).then((result) => {
        if (result.isConfirmed) {
            // window.location.href = new_url;
        } else if (result.dismiss === Swal.DismissReason.cancel) {
            // Swal.fire(
            //     'Cancelled',
            // )
        }
    });
};


/*------------------------------------------------------------------
    17. Close All Existing Modals and Backdrops
--------------------------------------------------------------------*/
var modalClose = (event) => {
    $(".modal").remove();
    $(".modal-barkdrop").remove();
    $(".modal-backdrop").remove();
    $(".modal-open").removeClass("modal-open");
    $(".modal-backdrop").removeClass("modal-backdrop");
    $(".modal-backdrop").removeClass("modal-backdrop-open");
    $(".modal-backdrop").removeClass("show");
};

/*------------------------------------------------------------------
    18. Load Modal Content Dynamically via AJAX
--------------------------------------------------------------------*/
viewModal = (ur) => {
    modalClose();

    $.get(ur)
        .done(function (data) {
            // Check if the response is a JSON object with error
            if (typeof data === "object" && data.result === false) {
                Toast.fire({
                    icon: "error",
                    title: data.message || "Something went wrong!",
                });

                return;
            }

            // If HTML is returned, append and show modal
            $(data).appendTo("body");
            $(".modal").modal("show");
        })
        .fail(function (xhr) {
            let message = "Something went wrong!";
            try {
                const response = JSON.parse(xhr.responseText);
                message = response.message || message;
            } catch (e) {
                // keep default message
            }

            Toast.fire({
                icon: "error",
                title: message,
            });
        });
};

/*------------------------------------------------------------------
    19. Show Reject Confirmation Modal
--------------------------------------------------------------------*/
Reject = (id, ur) => {
    var html = `<div class="modal modal-blur fade" id="delete" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                    <div class="modal-title">
                    <h1 class="text-center danger"><i class="las la-exclamation-circle"></i></h1>
                    <br>
                    <h3 class="text-center">Are you sure?</h3> </div>
                    </div>
                    <div class="modal-footer">
                    <button type="button" class="btn btn-link btn-default mr-auto" data-dismiss="modal">Cancel</button>
                    <a href="${url + "/" + ur + id
        }" class="btn-cancel-soft">Reject</a>
                    </div>
                </div>
            </div>
            </div>`;
    $(html).appendTo("body").modal();
};

/*------------------------------------------------------------------
    20. Show Approve Confirmation Modal
--------------------------------------------------------------------*/
Approve = (id, ur) => {
    var html = `<div class="modal modal-blur fade" id="delete" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                    <div class="modal-title">
                    <h1 class="text-center text-success"><i class="las la-check-circle"></i></h1>
                    <br>
                    <h3 class="text-center">Are you sure?</h3> </div>
                    </div>
                    <div class="modal-footer">
                    <button type="button" class="btn btn-link btn-default mr-auto" data-dismiss="modal">Cancel</button>
                    <a href="${url + "/" + ur + id
        }" class="btn btn-primary">Approve</a>
                    </div>
                </div>
            </div>
            </div>`;
    $(html).appendTo("body").modal();
};


/*------------------------------------------------------------------
    21. Show Complete Confirmation Modal
--------------------------------------------------------------------*/
Complete = (id, ur) => {
    var html = `<div class="modal modal-blur fade" id="delete" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                    <div class="modal-title">
                    <h1 class="text-center text-success"><i class="las la-check-circle"></i></h1>
                    <br>
                    <h3 class="text-center">Are you sure?</h3> </div>
                    </div>
                    <div class="modal-footer">
                    <button type="button" class="btn btn-link btn-default mr-auto" data-dismiss="modal">Cancel</button>
                    <a href="${url + "/" + ur + id
        }" class="btn btn-primary">Complete</a>
                    </div>
                </div>
            </div>
            </div>`;
    $(html).appendTo("body").modal();
};


/*------------------------------------------------------------------
    22. Select2 Initialization
--------------------------------------------------------------------*/
$(".select2").select2({
    placeholder: "Choose one",
    width: "100%",
});
const progressValue = (val) => {
    $("#progress_percentage").html(val + "%");
};

$(function () {
    function formatText(icon) {
        return $(
            '<span><i class="' +
            $(icon.element).data("icon") +
            '"></i> ' +
            icon.text +
            "</span>"
        );
    }

    // select2 input with icon
    $(".select2-input-image").select2({
        width: "100%",
        marginBottom: "10px",
        templateSelection: formatText,
        templateResult: formatText,
    });

    // select2 input without image
    $(".select2-input").select2({
        width: "100%",
    });

    // select2 style
    $(".select2.select2-container").css({
        "margin-bottom": "10px",
    });

    $(".select2.select2-selection.select2-selection--single").addClass(
        "ot-input"
    );

    // remove default arrow and add custom arrow start

    $('b[role="presentation"]').hide();
    $(".select2-selection-arrow").append('<i class="las la-angle-down"></i>');

    $(".select2-selection-arrow").css({
        display: "flex",
        "align-items": "center",
        "justify-content": "center",
    });

    // remove default arrow and add custom arrow end
});


/*------------------------------------------------------------------
    23. Terms & Conditions Checkbox Toggle
--------------------------------------------------------------------*/
$("#terms_check_make_parent").on("click", function () {
    if ($(this).prop("checked")) {
        $("#terms_check_make_button").prop("disabled", false);
    } else {
        $("#terms_check_make_button").attr("disabled", true);
    }
});


/*------------------------------------------------------------------
    24. Account Statement AJAX Fetch & Render
--------------------------------------------------------------------*/
accountStatement = () => {
    $.ajax({
        type: "POST",
        dataType: "html",
        data: {
            start_date: $("#start_date").val(),
            end_date: $("#end_date").val(),
            _token: _token,
        },
        url: url + "/" + "dashboard/reports/ajax-account-statement",
        success: function (data) {
            $("#__account_statement").html(data);
        },
        error: function (data) { },
    });
};

$("#__account_statement").length > 0 && accountStatement();

//show leave type modal

/*------------------------------------------------------------------
    25. Vehicle Status Update Button Handler
--------------------------------------------------------------------*/
//status updating for vehicle
$("body").on("click", ".statusActionBtn", function () {
    let id = $(this).attr("item-id");
    let url = `vehicle/update-status/${id}`;
    console.log(url);
    $.ajax({
        type: "GET",
        url: url,
        success: function (data) {
            window.location.reload();
        },
        error: function (data) {
            console.log(data);
        },
    });
});


/*------------------------------------------------------------------
    26. File Upload Preview Handler
--------------------------------------------------------------------*/
$(".upload_file").length > 0
    ? (upload_file.onchange = (evt) => {
        const [file] = upload_file.files;
        if (file) {
            bruh.src = URL.createObjectURL(file);
        }
    })
    : "";

/*------------------------------------------------------------------
    27. Select Users by Department with AJAX-powered Select2
--------------------------------------------------------------------*/

//get department wise user
function selectDepartmentUsers() {
    let department_id = $("#department").select2("val");
    $("#selected_department").val(department_id);
    $("#__user_id").select2({
        placeholder: "Choose User",
        ajax: {
            url: url + "/dashboard/user/get-all-user-by-dep-des",
            data: {
                department_id: department_id,
                _token: _token,
            },
            type: "POST",
            delay: 250,
            processResults: function (data) {
                let users = data.data.users;
                return {
                    results: $.map(users, function (item) {
                        return {
                            text: item.name,
                            id: item.id,
                        };
                    }),
                };
            },
            cache: false,
        },
    });
}


/*------------------------------------------------------------------
    28. Initialize Payroll Salary Advance Data & Department Users
--------------------------------------------------------------------*/
function selectDepartmentUsersAll() {
    salaryAdvanceDatatable();
    departmentWiseUsers();
}


/*------------------------------------------------------------------
    29. Initialize Leave Request Data & Department Users
--------------------------------------------------------------------*/
function selectLeaveRequestDepartmentUsers() {
    leaveRequestDatatable();
    departmentWiseUsers();
}

/*------------------------------------------------------------------
    30. Initialize Visit Data & Department Users
--------------------------------------------------------------------*/
function selectVisitDepartmentUsers() {
    visitDatatable();
    departmentWiseUsers();
}


/*------------------------------------------------------------------
    31. Initialize Break History Data & Department Users
--------------------------------------------------------------------*/
function selectBreakHistoryDepartmentUsers() {
    breakTable();
    departmentWiseUsers();
}


/*------------------------------------------------------------------
    32. Initialize Visit Report Data & Department Users
--------------------------------------------------------------------*/
function selectVisitReportDepartmentUsers() {
    visitReportDatatable();
    departmentWiseUsers();
}

/*------------------------------------------------------------------
    33. Load Users by Department with AJAX-powered Select2 
--------------------------------------------------------------------*/
function departmentWiseUsers() {
    let department_id = $("#department_id").select2("val");
    $("#selected_department").val(department_id);
    $("#__user_id").select2({
        placeholder: "Choose User",
        ajax: {
            url: url + "/dashboard/user/get-all-user-by-dep-des",
            data: {
                department_id: department_id,
                _token: _token,
            },
            type: "POST",
            delay: 250,
            processResults: function (data) {
                let users = data.data.users;
                return {
                    results: $.map(users, function (item) {
                        return {
                            text: item.name,
                            id: item.id,
                        };
                    }),
                };
            },
            cache: false,
        },
    });
}


/*------------------------------------------------------------------
    34. AJAX-powered Select2 for User Selection 
--------------------------------------------------------------------*/
$("#user_id").select2({
    placeholder: $("#choose_employee_text").val(),
    placement: "bottom",
    ajax: {
        url: $("#get_custom_user_url").val(),
        dataType: "json",
        data: function (params) {
            return {
                _token: _token,
                term: params.term, // Pass the typed value as the 'term' parameter
            };
        },
        type: "POST",
        delay: 250,
        processResults: function (data) {
            return {
                results: $.map(data, function (item) {
                    return {
                        text: item.name,
                        id: item.id,
                    };
                }),
            };
        },
        cache: false,
    },
});


/*------------------------------------------------------------------
    35. AJAX-powered Select2 for Attendance Method 
--------------------------------------------------------------------*/
$("#attendance_method").select2({
    placeholder: "Choose Attendance Method",
    placement: "bottom",
    ajax: {
        url: $("#get_attendance_method_data").val(),
        dataType: "json",
        data: {
            _token: _token,
        },
        type: "POST",
        delay: 250,
        processResults: function (data) {
            return {
                results: $.map(data, function (item) {
                    return {
                        text: item.name,
                        id: item.id,
                    };
                }),
            };
        },
        cache: false,
    },
});

/*------------------------------------------------------------------
    36. Load & Display Break Modal with Navigation
--------------------------------------------------------------------*/
breakBack = (ur, next_url) => {
    modalClose();
    $.get(ur, function (data) {
        if (data == "fail") {
            Toast.fire({
                title: $("#something_wrong").val(),
                type: "error",
                icon: "error",
            });
        } else {
            $(".break_back_button").html("");
            $(".break_back_button").html(`<button onclick="breakBack('${next_url}')"
                    class="ml-2 mr-2 btn btn-info box-shadow d-flex align-items-center sm-btn-with-radius">
                    <img class="zoom-in-zoom-out" src="${$("#break_icon").val()}"
                        alt="onest-hrm" style=" width: 19px; height: 19px; padding:0px !important">
                </button>`);
            $(data).appendTo("body");
            $(".modal").modal("show");
        }
    });
};

/*------------------------------------------------------------------
    37. Load & Display Break Start Modal
--------------------------------------------------------------------*/
breakStart = (ur) => {
    modalClose();
    $.get(ur, function (data) {
        if (data == "fail") {
            Toast.fire({
                title: $("#something_wrong").val(),
                type: "error",
                icon: "error",
            });
        } else {
            $(data).appendTo("body");
            $(".modal").modal("show");
        }
    });
};

/*------------------------------------------------------------------
    38. Load & Show Main Modal via AJAX
--------------------------------------------------------------------*/
mainModalOpen = (ur) => {
    // alert(ur);return false;
    modalClose();
    $.ajax({
        url: ur,
        type: "GET",
        success: function (data) {
            if (data == "fail") {
                Toast.fire({
                    title: $("#something_wrong").val(),
                    type: "error",
                    icon: "error",
                });
            } else {
                //modal show using javascript
                $(data).appendTo("body");
                $(".modal").modal("show");
                // $(data).appendTo('body').modal('show');
            }
        },
        error: function (err) {
            if (err?.responseJSON?.message) {
                Toast.fire({
                    iconColor: "white",
                    icon: "error",
                    title: err.responseJSON.message || "Something went wrong!",
                });
            }
        },
    });
};


/*------------------------------------------------------------------
    39. Check-in via Geolocation API
--------------------------------------------------------------------*/
let check_url;
checkIn = (url) => {
    check_url = url;
    if (navigator?.geolocation) {
        navigator.geolocation.getCurrentPosition(attendanceStore, positionError, {
            timeout: 10000,
        });
    } else {
        console.log("Geolocation is not supported by this browser.");
    }
};


/*------------------------------------------------------------------
    40. Geolocation Error Handler
--------------------------------------------------------------------*/
function positionError(error) {
    var errorCode = error.code;
    var message = error.message;
    // toastr.error(message, 'Error!', {
    //     timeOut: 3000
    // });

    attendanceStore();
}

/*------------------------------------------------------------------
    41. Store Attendance Location via AJAX
--------------------------------------------------------------------*/
function attendanceStore(position = null) {
    // console.log(position);
    $.ajax({
        type: "GET",
        url: check_url,
        data: {
            latitude: position?.coords?.latitude ?? "23.7909811",
            longitude: position?.coords?.longitude ?? "90.4067015",
        },
        success: function (data) {
            // console.log(data);
            if (data?.result) {
                Toast.fire({
                    icon: "success",
                    title: data.message,
                });
                setTimeout(function () {
                    window.location.href = data?.data;
                }, 3000);
            } else {
                Toast.fire({
                    iconColor: "white",
                    icon: "error",
                    title: "Something went wrong!",
                });
            }
        },
        error: function (data) {
            if (data?.responseJSON?.message) {
                Toast.fire({
                    iconColor: "white",
                    icon: "error",
                    title: data.responseJSON.message,
                });
                // if (data?.responseJSON?.error) {
                //     setTimeout(function () {
                //         window.location.href = data?.responseJSON?.error;
                //     }, 2000)
                // }
            }
        },
    });
}

/*------------------------------------------------------------------
    42. Auto-hide Bootstrap Tooltips after Page Load 
--------------------------------------------------------------------*/
$(document).ready(function () {
    setTimeout(function () {
        $('[data-toggle="tooltip"]').tooltip("hide", {
            animated: "fade",
            placement: "bottom",
            html: true,
        });
    }, 100);
});


/*------------------------------------------------------------------
    43. AJAX-powered Select2 for Custom User Selection 
--------------------------------------------------------------------*/
$("#custom_user").select2({
    placeholder: $("#select_custom_members").val(),
    placement: "bottom",
    ajax: {
        url: $("#get_custom_user_url").val(),
        dataType: "json",
        data: {
            _token: _token,
        },
        type: "POST",
        delay: 250,
        processResults: function (data) {
            return {
                results: $.map(data, function (item) {
                    return {
                        text: item.name,
                        id: item.id,
                    };
                }),
            };
        },
        cache: true,
    },
});


/*------------------------------------------------------------------
    44. AJAX-powered Select2 for Performance Goals 
--------------------------------------------------------------------*/
$("#goal_id").select2({
    placeholder: $("#select_goals").val(),
    placement: "bottom",
    ajax: {
        url: url + "/admin/performance/goal/get-goal",
        dataType: "json",
        type: "POST",
        delay: 250,
        processResults: function (data) {
            return {
                results: $.map(data, function (item) {
                    return {
                        text: item.name,
                        id: item.id,
                    };
                }),
            };
        },
        cache: true,
    },
});


/*------------------------------------------------------------------
    45. Initialize and Handle Date Range Picker
--------------------------------------------------------------------*/
var __date_range = {};
$(function () {
    $("#daterange").daterangepicker(
        {
            showDropdowns: false,
            applyButtonClasses: "apply-btn",
            cancelButtonClasses: "cancel-btn",
            locale: {
                cancelLabel: "Cancel",
                applyLabel: "Set Data",
                format: "YYYY-MM-DD",
            },

            ranges: {
                Today: [moment(), moment()],
                Yesterday: [moment().subtract(1, "days"), moment().subtract(1, "days")],
                "Last 7 Days": [moment().subtract(6, "days"), moment()],
                "Last 30 Days": [moment().subtract(29, "days"), moment()],
                "This Month": [moment().startOf("month"), moment().endOf("month")],
                "Last Month": [
                    moment().subtract(1, "month").startOf("month"),
                    moment().subtract(1, "month").endOf("month"),
                ],
            },
            showCustomRangeLabel: true,
            alwaysShowCalendars: true,
            startDate: moment(),
            endDate: moment(),
            drops: "auto",
        },
        function (start, end) {
            __date_range = {
                from: start.format("YYYY-MM-DD"),
                to: end.format("YYYY-MM-DD"),
            };

            $("#daterangeWrapper").append(
                `<input type="hidden" name="from" value="${start.format(
                    "YYYY-MM-DD"
                )}">`
            );
            $("#daterangeWrapper").append(
                `<input type="hidden" name="to" value="${end.format("YYYY-MM-DD")}">`
            );

            if ($("#daterange-input").length > 0) {
                $("#daterange-input").change();
            }
        }
    );

    $("#daterange").on("cancel.daterangepicker", function (ev, picker) {
        picker.setStartDate(moment());
        picker.setEndDate(moment());
        __date_range = {
            from: "",
            to: "",
        };
        $("#daterange-input").val("");
        $("#daterange-input").trigger("change");
    });

    // cb(start, end);
});


/*------------------------------------------------------------------
    46. AJAX-powered Select2 for Getting Members User List 
--------------------------------------------------------------------*/
$("#members").select2({
    placeholder: $("#select_members").val(),
    placement: "bottom",
    width: "100%",
    ajax: {
        url: $("#get_user_url").val(),
        dataType: "json",
        data: {
            _token: _token,
        },
        type: "POST",
        delay: 250,
        processResults: function (data) {
            return {
                results: $.map(data, function (item) {
                    return {
                        text: item.name,
                        id: item.id,
                    };
                }),
            };
        },
        cache: true,
    },
});


/*------------------------------------------------------------------
    47. AJAX-powered Select2 with Search for Employees 
--------------------------------------------------------------------*/
$("#_employees").select2({
    placeholder: $("#select_members").val(),
    placement: "bottom",
    width: "100%",
    search: true,
    ajax: {
        url: $("#get_user_url").val(),
        dataType: "json",
        data: function (params) {
            return {
                _token: _token,
                term: params.term,
            };
        },
        type: "POST",
        delay: 250,
        processResults: function (data) {
            return {
                results: $.map(data, function (item) {
                    return {
                        text: item.name,
                        id: item.id,
                    };
                }),
            };
        },
        cache: true,
    },
});

/*------------------------------------------------------------------
    48. Single Date Picker for Start Date
--------------------------------------------------------------------*/
$(".s_date").daterangepicker({
    singleDatePicker: true,
    showDropdowns: true,
});

/*------------------------------------------------------------------
    49. Single Date Picker for Expire Date with Clear Option
--------------------------------------------------------------------*/
$(".expire-date").daterangepicker({
    singleDatePicker: true,
    showDropdowns: true,
    autoUpdateInput: false,
    locale: {
        cancelLabel: "Clear",
    },
});

/*------------------------------------------------------------------
    50. Set Formatted Date on Expire Date Picker Apply
--------------------------------------------------------------------*/
$(".expire-date").on("apply.daterangepicker", function (ev, picker) {
    $(this).val(picker.startDate.format("MM/DD/YYYY"));
});


/*------------------------------------------------------------------
    51. Single Time Picker Setup for .s_time Inputs
--------------------------------------------------------------------*/
$(function () {
    $(".s_time")
        .daterangepicker({
            timePicker: true,
            singleDatePicker: true,
            timePicker24Hour: $("#time_format").val() == "h" ? false : true,
            timePickerIncrement: 1,
            timePickerSeconds: true,
            locale: {
                format: "HH:mm:ss",
            },
        })
        .on("show.daterangepicker", function (ev, picker) {
            picker.container.find(".calendar-table").hide();
        });
});


/*------------------------------------------------------------------
    52. AJAX Language Change on #change-user-lang Select
--------------------------------------------------------------------*/
const progress = document.querySelector("input[type=range]");
$("#change-user-lang").on("change", function () {
    let selected_lang = $(this).val();
    $.ajax({
        url: $("#change_lang_url").val(),
        type: "POST",
        data: {
            lang: selected_lang,
            _token: _token,
        },
        success: function (data) {
            console.log(data.success);
            if (data.success) {
                //page reload
                location.reload();
                toastr.success(data.message, "Success");
            } else {
                toastr.error(data.message, "Error");
            }
        },
    });
});


/*------------------------------------------------------------------
    53. Initialize Select2 on .company-select with custom container class
--------------------------------------------------------------------*/
if ($(".company-select").length > 0) {
    $select_company = $(".company-select").select2({});
    $select_company
        .data("select2")
        .$container.addClass("company-select-container");
}


/*------------------------------------------------------------------
    54. Initialize Select2 on .branch-select with custom container class
--------------------------------------------------------------------*/
if ($(".branch-select").length > 0) {
    $select_branch = $(".branch-select").select2({});
    $select_branch
        .data("select2")
        .$container.addClass("language-select-container");
}


/*------------------------------------------------------------------
    55. Toggle Menu Input Visibility Based on Menu Type
--------------------------------------------------------------------*/
var MenuType = (val) => {
    if (val == 1) {
        $("#menu_page_id").addClass("d-none");
        $("#menu_url_link").removeClass("d-none");
    } else {
        $("#menu_url_link").addClass("d-none");
        $("#menu_page_id").removeClass("d-none");
    }
};


/*------------------------------------------------------------------
    56. File Uploader with Multiple Inputs
--------------------------------------------------------------------*/
document.addEventListener("DOMContentLoaded", function () {
    for (let i = 0; i < 20; i++) {
        const fileInput = document.getElementById(`fileBrouse${i}`);
        if (fileInput) {
            fileInput.addEventListener("change", function (event) {
                const fileName = event.target.files[0]?.name; // Ensure file is selected
                const placeholder = document.getElementById(`placeholder${i}`);
                if (fileName && placeholder) {
                    placeholder.value = fileName; // Use `.value` instead of `.placeholder`
                }
            });
        }
    }
});

/*------------------------------------------------------------------
    57. Confirmation to Restore Employee (SweetAlert2)
--------------------------------------------------------------------*/
RestoreEmployee = (url) => {
    Swal.fire({
        title: "Are you sure to restore this Employee?",
        icon: "success",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
        reverseButtons: true,
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = `${url}`;
        }
    });
};


/*------------------------------------------------------------------
    58. No Close On Backdrop for Offcanvas
--------------------------------------------------------------------*/
$(document).ready(function () {
    $(".no-backdrop-close")
        .on({
            "show.bs.offcanvas": function () {
                $(this).one("shown.bs.offcanvas", () => {
                    setTimeout(() => {
                        $(".offcanvas-backdrop")
                            .off("click.nbc")
                            .on("click.nbc", (e) => {
                                e.preventDefault();
                                e.stopImmediatePropagation();
                                $(this).addClass("backdrop-blocked");
                                setTimeout(() => $(this).removeClass("backdrop-blocked"), 200);
                                return false;
                            });
                    }, 10);
                });
            },
            "hidden.bs.offcanvas": () => $(".offcanvas-backdrop").off("click.nbc"),
            "hide.bs.offcanvas": function (e) {
                if (
                    !$(this).data("manual-close") &&
                    !$(document.activeElement).closest(
                        '[data-bs-dismiss="offcanvas"], .btn-close'
                    ).length
                ) {
                    e.preventDefault();
                    e.stopImmediatePropagation();
                    $(this).addClass("backdrop-blocked");
                    setTimeout(() => $(this).removeClass("backdrop-blocked"), 200);
                    return false;
                }
            },
        })
        .attr("data-bs-backdrop", "true");
    // Handle close button clicks
    $(document).on(
        "click",
        '.no-backdrop-close [data-bs-dismiss="offcanvas"], .no-backdrop-close .btn-close',
        function () {
            $(this).closest(".no-backdrop-close").data("manual-close", true);
            setTimeout(
                () => $(this).closest(".no-backdrop-close").removeData("manual-close"),
                100
            );
        }
    );
});

// })(jQuery);

/*------------------------------------------------------------------
    59. Dynamic Custom Tooltip
--------------------------------------------------------------------*/

(function () {
    // Create tooltip
    const tooltip = document.createElement('div');
    tooltip.id = 'custom-tooltip';
    tooltip.innerHTML = `
    <div id="tooltip-image-wrapper" class="tooltip-image-wrapper" style="display: none;">
      <img id="tooltip-img" src="" alt="tooltip image" />
      <span id="tooltip-label" class="tooltip-label" style="display: none;"></span>
    </div>
    <div class="tooltip-box">
      <h3 id="tooltip-title"></h3>
      <p id="tooltip-description"></p>
      <a id="tooltip-link" href="#" target="_blank" style="display: none;">Learn More</a>
    </div>
  `;
    tooltip.style.display = 'none';
    document.body.appendChild(tooltip);

    // Inject CSS
    const style = document.createElement('style');
    style.textContent = `
    #custom-tooltip {
      position: absolute;
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
      width: 300px;
      z-index: 9999;
      font-family: sans-serif;
      overflow: hidden;
      cursor: pointer;
    }
    .tooltip-image-wrapper {
      position: relative;
      width: 100%;
      height: 160px;
      overflow: hidden;
    }
    .tooltip-image-wrapper img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    .tooltip-label {
      position: absolute;
      top: 10px;
      right: 10px;
      background: #111;
      color: #fff;
      font-size: 12px;
      padding: 4px 8px;
      border-radius: 10px;
      font-weight: 500;
    }
    .tooltip-box {
      padding: 16px;
      text-align: left;
    }
    .tooltip-box h3 {
      font-size: 16px;
      font-weight: bold;
      margin: 0 0 8px 0;
    }
    .tooltip-box p {
      font-size: 13px;
      color: #444;
      margin: 0 0 12px 0;
    }
    .tooltip-box a {
      font-size: 13px;
      color: #000;
      font-weight: bold;
      text-decoration: underline;
    }
  `;
    document.head.appendChild(style);

    // Reference elements
    const titleEl = tooltip.querySelector('#tooltip-title');
    const descEl = tooltip.querySelector('#tooltip-description');
    const linkEl = tooltip.querySelector('#tooltip-link');
    const imgEl = tooltip.querySelector('#tooltip-img');
    const labelEl = tooltip.querySelector('#tooltip-label');
    const imageWrapperEl = tooltip.querySelector('#tooltip-image-wrapper');

    let hideTimeout;

    document.querySelectorAll('.custom-tooltip-trigger').forEach(elem => {
        elem.addEventListener('mouseenter', () => {
            clearTimeout(hideTimeout);

            // Get all data attributes
            const title = elem.getAttribute('data-title');
            const desc = elem.getAttribute('data-description');
            const image = elem.getAttribute('data-image');
            const label = elem.getAttribute('data-label');
            const link = elem.getAttribute('data-link');

            // Set text content
            titleEl.textContent = title || '';
            descEl.textContent = desc || '';

            // Image logic
            if (image) {
                imgEl.src = image;
                imageWrapperEl.style.display = 'block';
            } else {
                imageWrapperEl.style.display = 'none';
            }

            // Label logic
            if (label) {
                labelEl.textContent = label;
                labelEl.style.display = 'inline-block';
            } else {
                labelEl.style.display = 'none';
            }

            // Link logic
            if (link) {
                linkEl.href = link;
                linkEl.style.display = 'inline-block';
            } else {
                linkEl.style.display = 'none';
            }

            // Position tooltip
            const rect = elem.getBoundingClientRect();
            const scrollTop = window.scrollY || document.documentElement.scrollTop;
            const scrollLeft = window.scrollX || document.documentElement.scrollLeft;

            tooltip.style.display = 'block';
            tooltip.style.top = `${rect.bottom + scrollTop + 10}px`;
            tooltip.style.left = `${rect.left + scrollLeft}px`;
        });

        elem.addEventListener('mouseleave', () => {
            hideTimeout = setTimeout(() => {
                tooltip.style.display = 'none';
            }, 150);
        });
    });

    // Keep tooltip open if hovered
    tooltip.addEventListener('mouseenter', () => clearTimeout(hideTimeout));
    tooltip.addEventListener('mouseleave', () => {
        tooltip.style.display = 'none';
    });

    /*------------------------------------------------------------------
        61. Mobile Filter Form Clone
    --------------------------------------------------------------------*/
    $('#mobileFilter').on('show.bs.offcanvas', function () {
        const $clonedForm = $('#tableSearchForm').children('form').clone(false, false);

        // Clean Select2 artifacts
        $clonedForm.find('select').each(function () {
            const $select = $(this);
            $select.removeClass('select2-hidden-accessible')
                .removeAttr('data-select2-id')
                .removeAttr('aria-hidden')
                .next('.select2').remove();
        });

        // // Inject the clone
        $('#cloneTableSearchForm').html($clonedForm);
        $('#cloneTableSearchForm').find('select').select2({
            dropdownParent: $('#mobileFilter')
        });
    });


    /*------------------------------------------------------------------
           62. Vanila Datepicker
    --------------------------------------------------------------------*/
    $(document).ready(function () {
        if ($(".tyne-datepicker").length) {
            $(".tyne-datepicker").each(function () {
                const $elem = $(this);
                const DATE_FORMAT = $elem.data("date-format") || "yyyy-mm-dd";
                const PICK_LEVEL = $elem.data("pick-level") || 0; // default day view

                // Configure datepicker options based on pick level
                let datepickerOptions = {
                    container: $elem.closest(".tyne-datepicker-container")[0],
                    inputClass: "form-control ot-form-control ot-input",
                    format: DATE_FORMAT,
                    autoclose: true,
                    todayHighlight: false,
                    language: "br", // en, ar
                    weekStart: 1,
                    inline: true,
                    clearButton: true,
                    prevArrow:
                        '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="m15 18-6-6 6-6"/></svg>',
                    nextArrow:
                        '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="m9 18 6-6-6-6"/></svg>',
                };

                // If pick level is 3 (year only), configure for year view
                if (PICK_LEVEL === 3) {
                    datepickerOptions.format = "yyyy";
                    datepickerOptions.startView = 2; // Start with year view
                    datepickerOptions.viewMode = 2; // Only show year view
                    datepickerOptions.minViewMode = 2; // Only allow year selection
                    datepickerOptions.maxViewMode = 2; // Only allow year selection
                    datepickerOptions.pickLevel = 2; // Only pick year level
                }
                if (PICK_LEVEL === 2) {
                    datepickerOptions.format = "mm";
                    datepickerOptions.startView = 1; // Start with year view
                    datepickerOptions.viewMode = 1; // Only show year view
                    datepickerOptions.minViewMode = 1; // Only allow year selection
                    datepickerOptions.maxViewMode = 1; // Only allow year selection
                    datepickerOptions.pickLevel = 1; // Only pick year level
                }

                const datepicker = new Datepicker(this, datepickerOptions);

                // Add click functionality to the date icon
                const $dateIcon = $elem.closest(".tyne-datepicker-container").find(".date-icon");
                if ($dateIcon.length) {
                    $dateIcon.on("click", function () {
                        if (datepicker.isOpen) {
                            datepicker.hide();
                        } else {
                            datepicker.show();
                        }
                    });
                }
            });
        }
    });

    /*------------------------------------------------------------------
           63. Open calendar
    --------------------------------------------------------------------*/
    

})();