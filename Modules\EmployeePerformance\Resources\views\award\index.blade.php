@extends('backend.layouts.app')
@section('title', @$title)
@section('content')
    <x-container :title="@$title">
        <x-table buttonTitle="Create" buttonRoute="{{ route('award.create') }}" permission="award_create">
            <x-slot name="filters">
                <div class="form-group h-42 w-lg-100">
                    <select class="form-select ot-input select2" name="user_id">
                        <option value="">{{ _trans('common.Select Employee') }}</option>
                        @foreach ($employees as $employee)
                            <option value="{{ $employee->id }}" {{ request('user_id') == $employee->id ? 'selected' : '' }}>
                                {{ $employee->name }} - {{ @$employee->designation->title ?? 'N/A' }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="form-group h-42 w-lg-100">
                    <select class="form-select ot-input select2" name="award_type_id">
                        <option value="">{{ _trans('common.Select Award Type') }}</option>
                        @foreach ($award_types as $type)
                            <option value="{{ $type->id }}"
                                {{ request('award_type_id') == $type->id ? 'selected' : '' }}>
                                {{ $type->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="form-group h-42 w-lg-100">
                    <select class="form-select ot-input select2" name="status">
                        <option value="">{{ _trans('common.Select Status') }}</option>
                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>
                            {{ _trans('common.Active') }}</option>
                        <option value="hold" {{ request('status') == 'hold' ? 'selected' : '' }}>
                            {{ _trans('common.Hold') }}</option>
                        <option value="executed" {{ request('status') == 'executed' ? 'selected' : '' }}>
                            {{ _trans('common.Executed') }}</option>
                    </select>
                </div>
            </x-slot>

            <table class="table table-bordered" id="table">
                <thead class="thead">
                    <tr>
                        <th class="sorting-asc w-60 no-export">
                            <div class="check-box">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="all_check" />
                                </div>
                            </div>
                        </th>
                        <th class="w-90">{{ _trans('common.SL') }}</th>
                        <th>{{ _trans('common.Employee') }}</th>
                        <th>{{ _trans('common.Award Type') }}</th>
                        <th>{{ _trans('common.Description') }}</th>
                        <th>{{ _trans('common.Date') }}</th>
                        <th>{{ _trans('common.Expire Date') }}</th>
                        <th>{{ _trans('common.Status') }}</th>
                        <th class="w-90 no-export">{{ _trans('common.Action') }}</th>
                    </tr>

                </thead>
                <tbody class="tbody">
                    @forelse ($collection ?? [] as $row)
                        <tr>
                            <td class="no-export">
                                <div class="check-box">
                                    <div class="form-check">
                                        <input class="form-check-input column_id" id="column_{{ $row->id }}"
                                            onclick="columnID({{ $row->id }})" type="checkbox" name="column_id[]"
                                            value="{{ $row->id }}">
                                    </div>
                                </div>
                            </td>
                            <td>{{ @$collection->firstItem() + $loop->index }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    @if (@$row->user->avatar)
                                        <img src="{{ @$row->user->avatar }}" alt="Avatar" class="rounded-circle me-2"
                                            width="32" height="32">
                                    @endif
                                    <div>
                                        <div class="fw-bold">{{ @$row->user->name }}</div>
                                        <small class="text-muted">{{ @$row->user->designation->title ?? 'N/A' }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    @if (@$row->type->badge_icon)
                                        <img src="{{ @$row->type->badge_icon }}" alt="Badge" class="me-2"
                                            width="24" height="24">
                                    @endif
                                    <span>{{ @$row->type->name }}</span>
                                </div>
                            </td>
                            <td>
                                @if (@$row->description)
                                    {{ Str::limit(@$row->description, 50) }}
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>{{ @$row->date ? $row->date->format('d M Y') : '-' }}</td>
                            <td>
                                @if (@$row->expire_date)
                                    <span class="{{ $row->isExpired() ? 'text-danger' : 'text-success' }}">
                                        {{ $row->expire_date->format('d M Y') }}
                                    </span>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                <x-common.badge :status="$row->status" :data="['active' => 'info', 'hold' => 'warning', 'executed' => 'success']" />
                            </td>
                            <td class="text-center no-export">
                                <x-table.action>
                                    @if (hasPermission('award_read'))
                                        <a href="{{ route('award.view', $row->id) }}" class="dropdown-item">
                                            <x-common.icons name="eye" class="text-title" size="16"
                                                stroke-width="1.5" />
                                            {{ _trans('common.View') }}
                                        </a>
                                    @endif
                                    @if (hasPermission('award_update'))
                                        <a href="{{ route('award.edit', $row->id) }}" class="dropdown-item">
                                            <x-common.icons name="edit" class="text-title" size="16"
                                                stroke-width="1.5" />
                                            {{ _trans('common.Edit') }}
                                        </a>
                                    @endif

                                    @if (hasPermission('award_delete'))
                                        <x-table.action.delete url="{{ route('award.delete', $row->id) }}">
                                        </x-table.action.delete>
                                    @endif
                                </x-table.action>
                            </td>
                        </tr>
                    @empty
                        <x-table.empty :colspan="9" />
                    @endforelse
                </tbody>
            </table>

            <x-table.pagination :data="$collection" />
        </x-table>
    </x-container>
@endsection
