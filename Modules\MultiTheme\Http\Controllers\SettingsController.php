<?php

namespace Modules\MultiTheme\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Setting\Setting;
use Exception;
use Illuminate\Http\Request;
use Modules\Notify\Services\NotificationService;

class SettingsController extends Controller
{
    public function __construct()
    {
        if (! isModuleActive('MultiTheme')) {
            abort('404');
        }
    }

    public function appThemeSetup(Request $request)
    {
        try {
            Setting::where('name', 'default_theme')->update(['value' => $request->app_theme]);
            (new NotificationService)->storeAppThemeChangeNotification();

            return redirect('/admin/settings/?app_theme_setup=true')->with('success', _trans('settings.App theme updated successfully'));
        } catch (Exception $e) {
            return redirect()->back()->with('error', $e->getMessage());
        }
    }
}
