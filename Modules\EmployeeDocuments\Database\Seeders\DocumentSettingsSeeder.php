<?php

namespace Modules\EmployeeDocuments\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\EmployeeDocuments\Entities\DocumentSetting;

class DocumentSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DocumentSetting::insert([
            [
                'key' => 'max_upload_size',
                'value' => '10MB',
                'status' => 'active',
                'is_boolean' => 0,
                'company_id' => 1,
                'branch_id' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'default_storage_location',
                'value' => 'local',
                'status' => 'active',
                'is_boolean' => 0,
                'company_id' => 1,
                'branch_id' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'allowed_file_types',
                'value' => 'pdf,docx,txt',
                'status' => 'active',
                'is_boolean' => 0,
                'company_id' => 1,
                'branch_id' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'auto_archive_days',
                'value' => '30',
                'status' => 'active',
                'is_boolean' => 0,
                'company_id' => 1,
                'branch_id' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'notification_email',
                'value' => '<EMAIL>',
                'status' => 'active',
                'is_boolean' => 0,
                'company_id' => 1,
                'branch_id' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }
}
