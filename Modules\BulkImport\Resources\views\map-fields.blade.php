@extends('backend.layouts.app')
@section('title', @$data['title'])

@section('content')
    <x-container :title="@$data['title']">

        {{-- Instructions Card --}}
        <div class="ot-card bg-primary-soft border mb-3">
            <h3 class="card-title d-flex align-items-center gap-10 mb-25">
                <x-common.icons name="chart-analytics" class="text-primary" size="22" stroke-width="1.5" />
                <span>{{ _trans('common.Field Mapping Instructions') }}</span>
            </h3>

            <p class="text-subtitle mb-20">
                {{ _trans('common.For each column in your CSV file, select the corresponding database field.') }}</p>

            <div class="user-related-info d-flex align-items-center justify-content-between flex-wrap gap-20">
                <div class="user-related-info-item d-flex align-items-center gap-10">
                    <div class="contents">
                        <x-common.tyne-badge :data="['compatible' => 'info']" :status="'Compatible'" variant="info" class="fw-semibold" />
                        <p class="paragraph mb-0 text-subtitle text-14">Data appears compatible with the field type</p>
                    </div>
                </div>
                <span class="line-style"></span>
                <div class="user-related-info-item d-flex align-items-center gap-10">
                    <div class="contents">
                        <x-common.tyne-badge :data="['warning' => 'warning']" :status="'Warning'" variant="warning" class="fw-semibold" />
                        <p class="paragraph mb-0 text-subtitle text-14">Some values may need formatting or conversion</p>
                    </div>
                </div>
                <span class="line-style"></span>
                <div class="user-related-info-item d-flex align-items-center gap-10">
                    <div class="contents">
                        <x-common.tyne-badge :data="['incompatible' => 'destructive']" :status="'Incompatible'" variant="destructive"
                            class="fw-semibold" />
                        <p class="paragraph mb-0 text-subtitle text-14">Data appears incompatible with the field type</p>
                    </div>
                </div>
            </div>

            <div class="border-top mt-16 pt-16">
                <p class="text-subtitle mb-0">{{ _trans('common.Select "Skip" to ignore a column.') }}</p>
            </div>
        </div>

        {{-- Main Form --}}
        <form action="{{ route('bulk-import.import') }}" method="POST">
            @csrf
            <input type="hidden" name="file_path" value="{{ $filePath }}">
            <input type="hidden" name="model" value="{{ $model }}">
            @foreach ($csvHeadings as $index => $heading)
                <input type="hidden" name="csv_headings[{{ $index }}]" value="{{ $heading }}">
            @endforeach

            {{-- Header Option --}}
            <div class="ot-card mb-3">
                <div class="d-flex align-items-center gap-15">
                    <div class="check-box">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="1" id="hasHeader" name="has_header"
                                checked>
                            <label class="form-check-label cursor-pointer text-title fw-medium" for="hasHeader">
                                <x-common.icons name="task-check" class="text-info" size="18" stroke-width="1.5" />
                                {{ _trans('common.First row contains headers') }}
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Field Mapping Grid --}}
            <div class="row g-y-24 mb-4">
                {{-- @dd($csvHeadings) --}}
                @foreach ($csvHeadings as $index => $heading)
                    {{-- @dump($heading) --}}
                    <div class="col-xl-3 col-lg-4 col-md-6">
                        <div class="ot-card border radius-8 p-24 d-flex flex-column h-100">
                            {{-- Column Header --}}
                            <div class="d-flex align-items-center gap-10 mb-16">
                                <x-common.icons name="grid" class="text-primary" size="20" stroke-width="1.5" />
                                <h4 class="text-16 fw-semibold mb-0">
                                    {{ !is_numeric($heading) ? ucfirst($heading) : "Column {$heading}" }}
                                </h4>
                            </div>

                            {{-- Field Selection --}}
                            <div class="form-group mb-16">
                                <label class="form-label text-14 fw-medium text-subtitle">
                                    @if (isset($columnMapping) && !empty($columnMapping))
                                        Map to Employee Field
                                    @else
                                        Map to Database Field
                                    @endif
                                </label>

                                <select name="mapping[{{ $index }}]" class="form-select ot-input field-mapping"
                                    data-column-index="{{ $index }}">
                                    <option value="">-- {{ _trans('common.Skip') }} --</option>
                                    @if (isset($columnMapping) && !empty($columnMapping))
                                        {{-- For employee imports, show user-friendly field names --}}
                                        @foreach ($columnMapping as $userFriendlyName => $dbField)
                                            @php
                                                // Get the CSV header for this column
                                                $csvHeader = $csvRows[0][$index] ?? '';
                                                // Check if user-friendly name matches CSV header
                                                $isSelected =
                                                    strtolower(trim($userFriendlyName)) ===
                                                    strtolower(trim($csvHeader));
                                            @endphp
                                            <option value="{{ $dbField }}"
                                                data-type="{{ $modelFieldsWithTypes[$dbField] ?? 'unknown' }}"
                                                {{ $isSelected ? 'selected' : '' }}>
                                                {{ $userFriendlyName }}
                                            </option>
                                        @endforeach
                                    @else
                                        {{-- For other imports, show database field names --}}
                                        @foreach ($modelFields as $field)
                                            @php
                                                // Get the CSV header for this column
                                                $csvHeader = $csvRows[0][$index] ?? '';
                                                // Check if field name matches CSV header
                                                $isSelected = strtolower(trim($field)) === strtolower(trim($csvHeader));
                                            @endphp
                                            <option value="{{ $field }}"
                                                data-type="{{ $modelFieldsWithTypes[$field] ?? 'unknown' }}"
                                                {{ $isSelected ? 'selected' : '' }}>
                                                {{ $field }}
                                            </option>
                                        @endforeach
                                    @endif
                                </select>
                            </div>

                            {{-- Data Type Info --}}
                            <div class="field-type-info mb-16 d-none">
                                <div class="d-flex align-items-center gap-8 mb-8">
                                    <x-common.icons name="cpu" class="text-subtitle" size="16"
                                        stroke-width="1.5" />
                                    <span class="text-12 fw-semibold text-subtitle">Data Type:</span>
                                    <span class="type-value text-12 fw-bold text-title"></span>
                                </div>
                                <div class="compatibility-badge"></div>
                            </div>

                            {{-- Sample Data Preview --}}
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center gap-8 mb-12">
                                    <x-common.icons name="eye" class="text-subtitle" size="16"
                                        stroke-width="1.5" />
                                    <span class="text-12 fw-semibold text-subtitle">Data Preview</span>
                                </div>

                                <div class="timeline max-height-300 overflow-y-auto hide-scroll mt-0 p-0 min-w-auto mb-0">
                                    {{-- @dd($csvRows) --}}
                                    @foreach (array_slice($csvRows, 0, 10) as $rowIndex => $row)
                                        <div
                                            class="timeline-item mb-8 p-2 border radius-4 border-style-dashed preview-row preview-row-{{ $rowIndex }} {{ $rowIndex === 0 ? 'header-row' : '' }}">
                                            <div class="content m-0 p-0">
                                                <div class="d-flex align-items-center gap-8">
                                                    <span class="tyne-badge" variant="secondary" size="sm">
                                                        {{ $rowIndex + 1 }}
                                                    </span>
                                                    <span
                                                        class="text-14 fw-normal text-title">{{ $row[$index] ?? 'N/A' }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            {{-- Action Buttons --}}
            <div class="ot-card">
                <div class="d-flex align-items-center justify-content-between flex-wrap gap-20">
                    <div class="d-flex align-items-center gap-10">
                        <x-common.icons name="upload" class="text-primary" size="20" stroke-width="1.5" />
                        <div class="contents">
                            <h6 class="title text-16 fw-bold mb-6">Ready to Import</h6>
                            <p class="paragraph mb-0 text-subtitle text-14">Review your field mappings and start the import
                                process</p>
                        </div>
                    </div>

                    <div class="d-flex align-items-center gap-10">
                        <a href="{{ route('bulk-import.index') }}" class="btn-cancel-soft">
                            <x-common.icons name="arrow-left" class="" size="18" stroke-width="1.5" />
                            {{ _trans('common.Cancel') }}
                        </a>
                        <button type="submit" class="btn-primary-fill">
                            <x-common.icons name="upload" class="" size="18" stroke-width="1.5" />
                            {{ _trans('common.Import Data') }}
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </x-container>
@endsection

@pushonce('script')
    <script>
        $(document).ready(function() {
            // Toggle header rows based on checkbox
            function toggleHeaderRows() {
                if ($('#hasHeader').is(':checked')) {
                    $('.header-row').hide();
                } else {
                    $('.header-row').show();
                }
            }

            toggleHeaderRows();
            $('#hasHeader').on('change', toggleHeaderRows);

            // Handle field type display when selecting a field
            $('.field-mapping').on('change', function() {
                const selectedOption = $(this).find('option:selected');
                const columnIndex = $(this).data('column-index');
                const fieldTypeInfo = $(this).closest('.ot-card').find('.field-type-info');
                const compatibilityBadge = fieldTypeInfo.find('.compatibility-badge');

                if (selectedOption.val()) {
                    const dataType = selectedOption.data('type');
                    fieldTypeInfo.find('.type-value').text(dataType);
                    fieldTypeInfo.removeClass('d-none');

                    // Check data compatibility
                    const sampleValues = getSampleValues(columnIndex);
                    const typeCompatibility = checkTypeCompatibility(dataType, sampleValues);

                    // Show compatibility badge
                    let badgeHtml = '';
                    if (typeCompatibility === 'good') {
                        badgeHtml =
                            '<span class="tyne-badge" variant="info"><i class="las la-check-circle"></i> Compatible</span>';
                    } else if (typeCompatibility === 'warning') {
                        badgeHtml =
                            '<span class="tyne-badge" variant="warning"><i class="las la-exclamation-triangle"></i> Needs Formatting</span>';
                    } else {
                        badgeHtml =
                            '<span class="tyne-badge" variant="destructive"><i class="las la-times-circle"></i> Incompatible</span>';
                    }
                    compatibilityBadge.html(badgeHtml);
                } else {
                    fieldTypeInfo.addClass('d-none');
                }
            });

            // Helper function to get sample values from a column
            function getSampleValues(columnIndex) {
                const values = [];
                $(`.field-mapping[data-column-index="${columnIndex}"]`).closest('.ot-card').find('.preview-row')
                    .each(function() {
                        const text = $(this).find('.text-title').text().trim();
                        if (text !== 'N/A') {
                            values.push(text);
                        }
                    });
                return values;
            }

            // Basic type compatibility check
            function checkTypeCompatibility(dataType, sampleValues) {
                if (!sampleValues.length) {
                    return 'good';
                }

                let isCompatible = true;
                let warnings = 0;

                switch (dataType) {
                    case 'int':
                    case 'tinyint':
                    case 'smallint':
                    case 'mediumint':
                    case 'bigint':
                        for (const value of sampleValues) {
                            if (value && isNaN(parseInt(value))) {
                                isCompatible = false;
                                break;
                            }
                        }
                        break;

                    case 'decimal':
                    case 'float':
                    case 'double':
                        for (const value of sampleValues) {
                            if (value && isNaN(parseFloat(value))) {
                                isCompatible = false;
                                break;
                            }
                        }
                        break;

                    case 'date':
                        for (const value of sampleValues) {
                            if (value && !isValidDate(value)) {
                                warnings++;
                            }
                        }
                        break;

                    case 'datetime':
                    case 'timestamp':
                        for (const value of sampleValues) {
                            if (value && !isValidDateTime(value)) {
                                warnings++;
                            }
                        }
                        break;
                }

                if (!isCompatible) {
                    return 'error';
                } else if (warnings > 0) {
                    return 'warning';
                }
                return 'good';
            }

            // Helper for date validation
            function isValidDate(dateStr) {
                const regex = /^\d{4}-\d{2}-\d{2}$|^\d{2}\/\d{2}\/\d{4}$|^\d{2}\.\d{2}\.\d{4}$/;
                return regex.test(dateStr);
            }

            // Helper for datetime validation
            function isValidDateTime(dateStr) {
                const regex =
                    /^\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}(:\d{2})?$|^\d{2}\/\d{2}\/\d{4}\s\d{2}:\d{2}(:\d{2})?$/;
                return regex.test(dateStr);
            }
        });
    </script>
@endpushonce
