<?php

namespace Modules\AssetManagement\Repositories;

use App\Services\FileUploadService;
use Modules\AssetManagement\Entities\AssetCategory;

use function config;

class AssetCategoryRepository
{
    public function __construct(
        protected AssetCategory $model
    ) {}

    public function getPaginateData($request, $fields = ['*'])
    {
        $data = $this->model->select($fields)->latest('id')
            ->when($request->search, function ($query) use ($request) {
                $query->where('title', 'like', '%'.$request->search.'%');
            })
            ->paginate($request->limit ?? 10);

        return $data;
    }

    public function getAll()
    {
        return $this->model->all();
    }

    // Store Promotion
    public function store($data)
    {
        if (isset($data['attachment_file']) && $data['attachment_file']->isValid()) {
            $file = [
                'disk' => config('filesystems.default'),
                'file' => FileUploadService::file($data['attachment_file'], 'asset_category'),
            ];

            $data['attachments'] = $file ? json_encode($file) : null;
        }

        $this->model->create($data);

        return true;
    }

    public function show($id)
    {
        return $this->model->find($id);
    }

    public function update($data, $category)
    {
        $attachments = $category->attachments;

        if (isset($data['attachment_file']) && $data['attachment_file']->isValid()) {
            if (is_array($attachments) && isset($attachments['disk'], $attachments['file'])) {
                FileUploadService::delete($attachments['file']);
            }

            $attachments = [
                'disk' => config('filesystems.default'),
                'file' => FileUploadService::file($data['attachment_file'], 'asset_category'),
            ];
        }

        return $category->update([
            'title' => $data['title'],
            'status' => $data['status'],
            'attachments' => $attachments ? json_encode($attachments) : null,
        ]);
    }

    public function delete($category)
    {
        if ($category->attachments !== null) {
            FileUploadService::delete($category->attachments);

            return $category->delete();
        } else {
            return $category->delete();
        }

        return false;
    }
}
