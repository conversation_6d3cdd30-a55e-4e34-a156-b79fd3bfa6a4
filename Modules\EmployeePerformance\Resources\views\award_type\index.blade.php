@extends('backend.layouts.app')
@section('title', @$title)
@section('content')
    <div class="row">
        <div class="col-lg-9">
            <x-container :title="$title">
                <x-table :bulkAction="true" :exportOption="true" buttonType="modal" modalId="awardTypeForm">
                    <table class="table table-bordered" id="table">
                        <thead class="thead">
                            <tr>
                                <th class="sorting-asc w-60 no-export">
                                    <div class="check-box">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="all_check" />
                                        </div>
                                    </div>
                                </th>
                                <th class="w-90">{{ _trans('common.SL') }}</th>
                                <th>{{ _trans('common.Name') }}</th>
                                <th>{{ _trans('common.Duration') }}</th>
                                <th>{{ _trans('common.Message') }}</th>
                                <th class="no-export">{{ _trans('common.Badge') }}</th>
                                <th>{{ _trans('common.Status') }}</th>
                                <th class="w-90 no-export">{{ _trans('common.Action') }}</th>
                            </tr>

                        </thead>
                        <tbody class="tbody">
                            @forelse ($collection ?? [] as $row)
                                <tr>
                                    <td class="no-export">
                                        <div class="check-box">
                                            <div class="form-check">
                                                <input class="form-check-input column_id" id="column_{{ $row->id }}"
                                                    onclick="columnID({{ $row->id }})" type="checkbox"
                                                    name="column_id[]" value="{{ $row->id }}">
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ @$collection->firstItem() + $loop->index }}</td>
                                    <td>{{ @$row->name }}</td>
                                    <td>
                                        @if (@$row->duration)
                                            {{ @$row->duration }} {{ _trans('common.days') }}
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if (@$row->message)
                                            {{ Str::limit(@$row->message, 50) }}
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td class="no-export">
                                        @if (@$row->badge_icon)
                                            <img src="{{ @$row->badge_icon }}" alt="Badge" class="img-thumbnail"
                                                style="max-width: 40px;">
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        <x-common.badge status="{{ @$row->status }}"></x-common.badge>
                                    </td>
                                    <td class="text-center no-export">
                                        <x-table.action>
                                            @if (hasPermission('award_type_update'))
                                                <a href="{{ route('award_type.index', ['id' => $row->id]) }}"
                                                    class="dropdown-item">
                                                    <x-common.icons name="edit" class="text-title" size="16"
                                                        stroke-width="1.5" />
                                                    {{ _trans('common.Edit') }}
                                                </a>
                                            @endif
                                            @if (hasPermission('award_type_delete'))
                                                <x-table.action.delete url="{{ route('award_type.delete', $row->id) }}">
                                                </x-table.action.delete>
                                            @endif
                                        </x-table.action>
                                    </td>
                                </tr>
                            @empty
                                <x-table.empty :colspan="8" />
                            @endforelse
                        </tbody>
                    </table>
                    <x-table.pagination :data="@$collection"></x-table.pagination>
                </x-table>
            </x-container>
        </div>

        <x-inner-form-layout :title="@$formTitle" modalId="awardTypeForm">
            @include('employeeperformance::award_type.form', [
                'route' => @$award_type ? route('award_type.update', @$award_type->id) : route('award_type.store'),
                'type' => @$award_type ? 'PUT' : 'POST',
                'data' => @$award_type,
            ])
        </x-inner-form-layout>
    </div>

@endsection
