@extends('backend.layouts.app')
@section('title', @$data['title'])
@section('content')
    <div class="row">
        <div class="col-lg-12">
            <x-container :title="$title">
                <x-table :searchBox="true" :searchPlaceholder="'Search By Employee Name'" :bulkAction="false" :exportOption="false">
                    <table class="table table-bordered" id="table">
                        <thead class="thead">
                            <tr>
                                <th class="sorting-asc w-60">
                                    <div class="check-box">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="all_check" />
                                        </div>
                                    </div>
                                </th>
                                <th class="w-90">{{ _trans('common.SL') }}</th>
                                <th>{{ _trans('common.Employee Name') }}</th>
                                <th>{{ _trans('common.Designation') }}</th>
                                <th>{{ _trans('common.Device Information') }}</th>
                                <th>{{ _trans('common.Device Type') }}</th>
                                <th>{{ _trans('common.Date') }}</th>
                                <th class="w-90">{{ _trans('common.Action') }}</th>
                            </tr>
                        </thead>
                        <tbody class="tbody">
                            @forelse ($collection ?? [] as $row)
                                <tr>
                                    <td>
                                        <div class="check-box">
                                            <div class="form-check">
                                                <input class="form-check-input column_id" id="column_{{ $row->id }}"
                                                    onclick="columnID({{ $row->id }})" type="checkbox"
                                                    name="column_id[]" value="{{ $row->id }}">
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ @$collection->firstItem() + $loop->index }}</td>
                                    <td>{{ $row->user->name }}</td>
                                    <td>{{ $row->user->designation->title }}</td>
                                    <td>{{ @$row->device_info }}</td>
                                    <td>{{ @$row->last_login_device }}</td>
                                    <td>{{ formatDateTime(@$row->created_at->format('Y-m-d')) }}</td>
                                    <td class="text-center">
                                        <x-table.action>
                                            @if (hasPermission('leave_request_read'))
                                                <a href="{{ route('user.resetDevice', [$row->user_id, $row->last_login_device]) }}"
                                                    class="dropdown-item">
                                                    <x-common.icons name="trash" class="text-subtitle" size="16"
                                                        stroke-width="1.5" />
                                                    {{ _trans('common.Forget Device') }}
                                                </a>
                                            @endif
                                        </x-table.action>
                                    </td>
                                </tr>
                            @empty
                                <x-table.empty :colspan="10" />
                            @endforelse
                        </tbody>
                    </table>
                    <x-table.pagination :data="$collection"></x-table.pagination>
                </x-table>
            </x-container>
        </div>
    </div>
@endsection
