<?php

namespace App\Http\Middleware;

use Closure;

use function config;
use function explode;
use function last;
use function redirect;
use function response;

class DemoModeMiddleware
{
    public function handle($request, Closure $next)
    {
        if (config('app.style') === 'demo' && ! config('app.debug')) {
            // Check the current route action
            $routeAction = $request->route()->getAction('controller');

            // Check if the action is "destroy"
            $action = last(explode('@', $routeAction));

            if ($action === 'destroy') {
                if ($request->ajax()) {
                    return response()->json([
                        'result' => false,
                        'message' => 'You are not allowed to perform the delete action in demo mode',
                        'error' => 'failed',
                    ], 401);
                } else {
                    return redirect()->back()->with('error', _trans('alert.You are not allowed to perform the delete action in demo mode'));
                }
            }
        }

        return $next($request);
    }
}
