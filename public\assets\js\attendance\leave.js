"use strict";
var url = $("#url").val();
var _token = $('meta[name="csrf-token"]').attr("content");

function leaveBtnHold() {
  let duration = 1600,
    success = (button) => {
      //Success function
      $(".progress").hide();
      button.classList.add("success");
      applyLeave();
    };

  document.querySelectorAll(".leave-button-hold").forEach((button) => {
    button.style.setProperty("--duration", duration + "ms");
    ["mousedown", "touchstart", "keypress"].forEach((e) => {
      button.addEventListener(e, (ev) => {
        if (
          e != "keypress" ||
          (e == "keypress" &&
            ev.which == 32 &&
            !button.classList.contains("process"))
        ) {
          button.classList.add("process");
          button.timeout = setTimeout(success, duration, button);
        }
      });
    });
    ["mouseup", "mouseout", "touchend", "keyup"].forEach((e) => {
      button.addEventListener(
        e,
        (ev) => {
          if (e != "keyup" || (e == "keyup" && ev.which == 32)) {
            button.classList.remove("process");
            clearTimeout(button.timeout);
          }
        },
        false
      );
    });
  });
}

leaveBtnHold();

var applyLeave = () => {
  Toast.fire({
    icon: "success",
    title: "Redirecting to leave application...",
    timer: 1500,
  });
  setTimeout(function () {
    window.location.href =
      window.location.origin + "/admin/leave/request/create";
  }, 1500);
};
