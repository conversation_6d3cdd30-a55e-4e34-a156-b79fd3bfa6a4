<?php

namespace Modules\EmployeeDocuments\Database\Seeders;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;
use Modules\EmployeeDocuments\Entities\UserDocumentRequest;
use Modules\EmployeeDocuments\Entities\UserDocumentType;

class UserDocumentRequestsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Disable foreign key constraints using the same approach as RegularMigrate
        Schema::disableForeignKeyConstraints();

        $currentTimestamp = Carbon::now();
        $documentType = UserDocumentType::first(); // Assuming there is at least one document type

        // Retrieve users with their branch and company information to optimize database queries
        $users = User::select('id', 'branch_id', 'company_id')->get();

        $userDocumentRequests = [];
        foreach ($users as $user) {
            $userDocumentRequests = [
                'user_id' => $user->id,
                'branch_id' => $user->branch_id,
                'company_id' => $user->company_id,
                'informed_to' => $user->id,
                'user_document_type_id' => $documentType->id,
                'response_description' => $documentType->response_template, // Corrected typo
                'response_file' => 'path/to/response_file.pdf', // Replace with actual path or null
                'response_date' => $currentTimestamp,
                'response_by' => 2, // Replace with actual user ID or null
                'request_by' => 1, // Replace with actual user ID
                'request_description' => $documentType->request_template,
                'request_file' => 'path/to/response_file.pdf', // Replace with actual path or null
                'request_date' => $currentTimestamp,
                'status' => 'pending',
                'created_at' => $currentTimestamp,
                'updated_at' => $currentTimestamp,
            ];
            UserDocumentRequest::insert($userDocumentRequests);
            $this->command->info('User document requests seeded successfully.');
        }

        // Re-enable foreign key constraints
        Schema::enableForeignKeyConstraints();
    }
}
