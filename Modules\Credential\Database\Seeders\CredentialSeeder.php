<?php

namespace Modules\Credential\Database\Seeders;

use App\Models\User;
use Faker\Factory as Faker;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use Modules\Credential\Entities\Credential;
use Modules\Credential\Entities\CredentialAccess;
use Modules\Credential\Entities\CredentialGroup;

class CredentialSeeder extends Seeder
{
    public function run()
    {
        $faker = Faker::create();
        $credentialGroups = CredentialGroup::pluck('id', 'slug');
        $groupSlugs = collect([
            'gmail', 'github', 'gitlab', 'google', 'slack', 'trello', 'canva', 'facebook', 'linkedin',
            'notion', 'asana', 'discord', 'zoom', 'meet', 'outlook', 'yahoo', 'figma', 'freepik', 'icloud',
        ])->filter(fn ($slug) => $credentialGroups->has($slug))->values();

        foreach (User::all() as $user) {
            $selectedSlugs = $groupSlugs->shuffle()->take(12);

            foreach ($selectedSlugs as $slug) {
                $credential = Credential::create([
                    'title' => Str::title($slug).' Account',
                    'username' => $faker->unique()->userName,
                    'password' => rand(100000, 999999),
                    'link' => 'https://'.$slug.'.com',
                    'notes' => $faker->sentence,
                    'status' => 'active',
                    'log' => null,
                    'created_by' => $user->id,
                    'credential_group_id' => $credentialGroups[$slug],
                    'company_id' => $user->company_id ?? 1,
                    'branch_id' => $user->branch_id ?? 1,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                CredentialAccess::create([
                    'user_id' => $user->id,
                    'credential_id' => $credential->id,
                    'company_id' => null,
                    'branch_id' => null,
                    'department_id' => null,
                    'is_editable' => 'active',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }
}
