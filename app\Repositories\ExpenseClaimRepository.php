<?php

namespace App\Repositories;

use App\Models\ExpenseClaim;
use App\Services\FileUploadService;
use Illuminate\Support\Facades\Auth;

class ExpenseClaimRepository
{
    public function __construct(
        protected ExpenseClaim $model,
    ) {}

    public function getPaginateData($request, $fields = ['*'])
    {
        $query = $this->model->select($fields);

        if (! empty($request->title)) {
            $query->where('title', 'like', '%'.$request->title.'%');
        }

        if (! empty($request->category)) {
            $query->where('category', $request->category);
        }

        if (! empty($request->payment_method)) {
            $query->where('payment_method', $request->payment_method);
        }

        return $query->paginate($request->limit ?? 10);
    }

    public function show($id)
    {
        return $this->model->find($id);
    }

    public function store($payload)
    {
        $payload['user_id'] = Auth::id();

        if (isset($payload['attachment_file']) && $payload['attachment_file']->isValid()) {
            $file = [
                'disk' => config('filesystems.default'),
                'file' => FileUploadService::file($payload['attachment_file'], 'expenseClaim'),
            ];
            $payload['attachments'] = $file ? json_encode($file) : null;
        }

        return $this->model->create($payload);
    }

    public function update($request, $id)
    {
        $expense = $this->model->findOrFail($id);

        $data = $request->only([
            'title',
            'category',
            'date',
            'amount',
            'description',
            'payment_method',
            'status',
        ]);

        $data['user_id'] = Auth::id();

        if (isset($request->attachment_file) && $request->attachment_file->isValid()) {
            $file = [
                'disk' => config('filesystems.default'),
                'file' => FileUploadService::file($request->attachment_file, 'expenseClaim'),
            ];
            $data['attachments'] = $file ? json_encode($file) : null;

            if ($expense->attachments) {
                FileUploadService::delete($expense->attachments);
            }
        }

        $expense->update($data);

        return $expense;
    }

    public function destroy($id)
    {
        $expense = $this->model->query()->find($id);

        if ($expense) {
            FileUploadService::delete($expense->attachments);

            return $expense->delete();
        }

        return false;

    }
}
