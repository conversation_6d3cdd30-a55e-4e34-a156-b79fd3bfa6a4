@extends('backend.layouts.app')
@section('title', @$data['title'])

@section('content')
    <x-container :title="@$data['title']">


        {{-- Summary Cards using summery-card-two pattern --}}
        <div class="row g-y-16 mb-3">
            <div class="d-flex align-items-start justify-content-between gap-10 mb-0">
                <h5 class="card-title mb-0">{{ _trans('common.Import Statistics') }}</h5>
            </div>
            <div class="col-sm-6 col-xs-6 col-md-4 col-lg-4 col-xl-4 col-xxl-3">
                <div class="summery-card-two h-calc">
                    <x-common.icons name="upload" class="text-primary mb-6" size="24" stroke-width="1.5" />
                    <div class="card-heading">
                        <div class="card-content">
                            <span class="count">{{ $stats['total'] }}</span>
                            <span
                                class="text-subtitle text-14 text-capitalize pb-0">{{ _trans('common.Total Imports') }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-xs-6 col-md-4 col-lg-4 col-xl-4 col-xxl-3">
                <div class="summery-card-two h-calc">
                    <x-common.icons name="time" class="text-warning mb-6" size="24" stroke-width="1.5" />
                    <div class="card-heading">
                        <div class="card-content">
                            <span class="count">{{ $stats['pending'] }}</span>
                            <span
                                class="text-subtitle text-14 text-capitalize pb-0">{{ _trans('common.Pending Queue') }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-xs-6 col-md-4 col-lg-4 col-xl-4 col-xxl-3">
                <div class="summery-card-two h-calc">
                    <x-common.icons name="refresh" class="text-info mb-6" size="24" stroke-width="1.5" />
                    <div class="card-heading">
                        <div class="card-content">
                            <span class="count">{{ $stats['processing'] }}</span>
                            <span
                                class="text-subtitle text-14 text-capitalize pb-0">{{ _trans('common.Active Processing') }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-xs-6 col-md-4 col-lg-4 col-xl-4 col-xxl-3">
                <div class="summery-card-two h-calc">
                    <x-common.icons name="ShieldTick" class="text-success mb-6" size="24" stroke-width="1.5" />
                    <div class="card-heading">
                        <div class="card-content">
                            <span class="count">{{ $stats['completed'] }}</span>
                            <span
                                class="text-subtitle text-14 text-capitalize pb-0">{{ _trans('common.Successfully Completed') }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <x-table buttonTitle="New Import" :bulkAction="false" buttonRoute="{{ route('bulk-import.index') }}"
            permission="bulk_import_create">

            {{-- Filters using existing filter pattern --}}
            <x-slot name="filters">
                {{-- Status Filter --}}
                <div class="form-group w-lg-100">
                    <select name="status" class="form-select select2-input ot-input mb-3 modal_select2">
                        <option value="">{{ _trans('common.All Status') }}</option>
                        @foreach ($statuses as $status)
                            <option {{ request('status') == $status ? 'selected' : '' }} value="{{ $status }}">
                                {{ ucfirst($status) }}
                            </option>
                        @endforeach
                    </select>
                </div>

                {{-- Model Filter --}}
                <div class="form-group w-lg-100">
                    <select name="model_class" class="form-select select2-input ot-input mb-3 modal_select2">
                        <option value="">{{ _trans('common.All Models') }}</option>
                        @foreach ($models as $model)
                            <option {{ request('model_class') == $model ? 'selected' : '' }} value="{{ $model }}">
                                {{ ucwords(str_replace('_', ' ', $model)) }}
                            </option>
                        @endforeach
                    </select>
                </div>

                {{-- Date Filters --}}
                <div class="form-group w-lg-100">
                    <input type="date" name="date_from" class="form-control ot-input mb-3"
                        value="{{ request('date_from') }}" placeholder="{{ _trans('common.From Date') }}">
                </div>
                <div class="form-group w-lg-100">
                    <input type="date" name="date_to" class="form-control ot-input mb-3"
                        value="{{ request('date_to') }}" placeholder="{{ _trans('common.To Date') }}">
                </div>
            </x-slot>

            {{-- Enhanced Table using existing table patterns --}}
            <table class="table table-bordered" id="table">
                <thead class="thead">
                    <tr>
                        <th class="sorting-asc w-40 no-export">
                            <div class="check-box">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="all_check" />
                                </div>
                            </div>
                        </th>
                        <th class="w-90 text-center">
                            <x-common.icons name="grid" class="text-subtitle" size="16" stroke-width="1.5" />
                            {{ _trans('common.ID') }}
                        </th>
                        <th>
                            <x-common.icons name="shapes" class="text-subtitle" size="16" stroke-width="1.5" />
                            {{ _trans('common.Model') }}
                        </th>
                        <th>
                            <x-common.icons name="document-copy" class="text-subtitle" size="16" stroke-width="1.5" />
                            {{ _trans('common.File') }}
                        </th>
                        <th>
                            <x-common.icons name="chart" class="text-subtitle" size="16" stroke-width="1.5" />
                            {{ _trans('common.Status') }}
                        </th>
                        <th>
                            <x-common.icons name="chart-analytics" class="text-subtitle" size="16"
                                stroke-width="1.5" />
                            {{ _trans('common.Progress') }}
                        </th>
                        <th>
                            <x-common.icons name="calendar" class="text-subtitle" size="16" stroke-width="1.5" />
                            {{ _trans('common.Created') }}
                        </th>
                        <th>
                            <x-common.icons name="timer_circle" class="text-subtitle" size="16"
                                stroke-width="1.5" />
                            {{ _trans('common.Duration') }}
                        </th>
                        <th class="no-export">
                            <x-common.icons name="settings" class="text-subtitle" size="16" stroke-width="1.5" />
                            {{ _trans('common.Action') }}
                        </th>
                    </tr>
                </thead>
                <tbody class="tbody">
                    @forelse ($imports as $import)
                        <tr data-import-id="{{ $import->id }}">
                            <td class="w-20 no-export">
                                <div class="check-box">
                                    <div class="form-check">
                                        <input class="form-check-input column_id" id="column_{{ $import->id }}"
                                            onclick="columnID({{ $import->id }})" type="checkbox" name="column_id[]"
                                            value="{{ $import->id }}">
                                    </div>
                                </div>
                            </td>
                            <td class="text-center">
                                <x-common.tyne-badge :data="['id' => 'primary']" :status="'#' . $import->id" variant="primary" />
                            </td>
                            <td>
                                <div class="d-flex align-items-center gap-10">
                                    <x-common.icons name="shapes" class="text-primary" size="18"
                                        stroke-width="1.5" />
                                    <span class="fw-medium">{{ $import->model_display_name }}</span>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex flex-column">
                                    <div class="d-flex align-items-center gap-10">
                                        <x-common.icons name="document-copy" class="text-info" size="16"
                                            stroke-width="1.5" />
                                        <span class="fw-medium"
                                            title="{{ $import->file_path }}">{{ $import->file_name }}</span>
                                    </div>
                                    <small class="text-muted">
                                        <x-common.icons name="cpu" class="text-muted" size="12"
                                            stroke-width="1.5" />
                                        {{ round($import->file_size / 1024, 2) }} KB
                                    </small>
                                </div>
                            </td>
                            <td>
                                @switch($import->status)
                                    @case('pending')
                                        <x-common.tyne-badge :data="['pending' => 'warning']" :status="'Pending'" variant="warning" />
                                    @break

                                    @case('processing')
                                        <x-common.tyne-badge :data="['processing' => 'info']" :status="'Processing'" variant="info" />
                                    @break

                                    @case('completed')
                                        <x-common.tyne-badge :data="['completed' => 'success']" :status="'Completed'" variant="success" />
                                    @break

                                    @case('failed')
                                        <x-common.tyne-badge :data="['failed' => 'destructive']" :status="'Failed'" variant="destructive" />
                                    @break

                                    @case('cancelled')
                                        <x-common.tyne-badge :data="['cancelled' => 'secondary']" :status="'Cancelled'" variant="secondary" />
                                    @break

                                    @default
                                        <x-common.tyne-badge :data="['unknown' => 'secondary']" :status="ucfirst($import->status)" variant="secondary" />
                                @endswitch
                            </td>
                            <td class="progress-cell">
                                @if ($import->total_rows > 0)
                                    @php
                                        $progress = ($import->imported_rows / $import->total_rows) * 100;
                                    @endphp
                                    <div class="d-flex flex-column">
                                        <div class="progress mb-1" style="height: 6px;">
                                            <div class="progress-bar 
                                                @if ($import->status === 'completed') bg-success
                                                @elseif($import->status === 'failed') bg-danger
                                                @elseif($import->status === 'processing') bg-info
                                                @else bg-warning @endif
                                            "
                                                role="progressbar" style="width: {{ $progress }}%"
                                                aria-valuenow="{{ $progress }}" aria-valuemin="0"
                                                aria-valuemax="100">
                                            </div>
                                        </div>
                                        <small class="text-muted">
                                            <span
                                                class="imported-rows fw-medium">{{ number_format($import->imported_rows) }}</span>
                                            /
                                            <span class="total-rows">{{ number_format($import->total_rows) }}</span>
                                            (<span class="progress-percentage fw-bold">{{ round($progress, 1) }}%</span>)
                                        </small>
                                    </div>
                                @else
                                    <div class="d-flex align-items-center gap-6">
                                        <x-common.icons name="time" class="text-muted" size="16"
                                            stroke-width="1.5" />
                                        <span class="text-muted">{{ _trans('common.Not started') }}</span>
                                    </div>
                                @endif
                            </td>
                            <td>
                                <div class="d-flex flex-column">
                                    <span class="fw-medium">{{ $import->created_at->format('M d, Y') }}</span>
                                    <small class="text-muted">{{ $import->created_at->format('h:i A') }}</small>
                                </div>
                            </td>
                            <td class="duration-cell">
                                @if ($import->duration)
                                    <div class="d-flex align-items-center gap-6">
                                        <x-common.icons name="timer_circle" class="text-info" size="16"
                                            stroke-width="1.5" />
                                        <span class="fw-medium">{{ $import->duration }}</span>
                                    </div>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td class="text-center no-export">
                                <x-table.action class="w-30">
                                    <a class="dropdown-item" href="{{ route('bulk-import.history.show', $import->id) }}">
                                        <x-common.icons name="eye" class="text-title" size="16"
                                            stroke-width="1.5" />
                                        {{ _trans('common.View Details') }}
                                    </a>

                                    @if ($import->isInProgress())
                                        <form action="{{ route('bulk-import.history.cancel', $import->id) }}"
                                            method="POST" style="display: inline;">
                                            @csrf
                                            <button type="submit" class="dropdown-item text-warning">
                                                <x-common.icons name="x-circle" class="text-warning" size="16"
                                                    stroke-width="1.5" />
                                                {{ _trans('common.Cancel') }}
                                            </button>
                                        </form>
                                    @endif

                                    @if ($import->isFailed() || $import->status === 'cancelled')
                                        <form action="{{ route('bulk-import.history.retry', $import->id) }}"
                                            method="POST" style="display: inline;">
                                            @csrf
                                            <button type="submit" class="dropdown-item text-primary">
                                                <x-common.icons name="refresh" class="text-primary" size="16"
                                                    stroke-width="1.5" />
                                                {{ _trans('common.Retry') }}
                                            </button>
                                        </form>
                                    @endif

                                    @if (!$import->isInProgress())
                                        <form action="{{ route('bulk-import.history.destroy', $import->id) }}"
                                            method="POST"
                                            onsubmit="return confirm('{{ _trans('common.Are you sure you want to delete this import?') }}')"
                                            style="display: inline;">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="dropdown-item text-danger">
                                                <x-common.icons name="trash" class="text-danger" size="16"
                                                    stroke-width="1.5" />
                                                {{ _trans('common.Delete') }}
                                            </button>
                                        </form>
                                    @endif
                                </x-table.action>
                            </td>
                        </tr>
                        @empty
                            <x-table.empty colspan="9">
                                <div class="text-center py-4">
                                    <x-common.icons name="document-copy" class="text-muted mb-3" size="48"
                                        stroke-width="1" />
                                    <h5 class="text-muted mb-2">{{ _trans('common.No Import History') }}</h5>
                                    <p class="text-muted mb-3">
                                        {{ _trans('common.No import records found. Start by creating your first import.') }}
                                    </p>
                                    <a href="{{ route('bulk-import.index') }}" class="btn-primary-fill">
                                        <x-common.icons name="plus" size="16" stroke-width="1.5" />
                                        {{ _trans('common.Create Import') }}
                                    </a>
                                </div>
                            </x-table.empty>
                        @endforelse
                    </tbody>
                </table>

                <x-table.pagination :data="$imports"></x-table.pagination>
            </x-table>
        </x-container>
    @endsection

    @push('script')
        <script src="{{ asset('vendor/bulk-import/js/import-status.js') }}"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Track all in-progress imports
                const inProgressImports = [];

                @foreach ($imports as $import)
                    @if ($import->isInProgress())
                        inProgressImports.push({
                            id: {{ $import->id }},
                            tracker: null
                        });
                    @endif
                @endforeach

                // Initialize tracking for in-progress imports
                if (inProgressImports.length > 0) {
                    inProgressImports.forEach(function(importData) {
                        importData.tracker = new ImportStatusTracker({
                            importId: importData.id,
                            userId: {{ Auth::id() }},
                            onUpdate: function(data) {
                                // Find elements for this import
                                const statusBadge = document.getElementById(
                                    `import-status-${data.id}`);
                                const progressBar = document.getElementById(
                                    `import-progress-${data.id}`);
                                const importedRows = document.getElementById(
                                    `import-imported-rows-${data.id}`);
                                const totalRows = document.getElementById(
                                    `import-total-rows-${data.id}`);

                                // Update progress
                                if (progressBar) {
                                    progressBar.style.width = `${data.progress_percentage}%`;
                                    progressBar.setAttribute('aria-valuenow', data
                                        .progress_percentage);
                                }

                                // Update row counts
                                if (importedRows) {
                                    importedRows.textContent = data.imported_rows;
                                }

                                if (totalRows) {
                                    totalRows.textContent = data.total_rows;
                                }

                                // Reload page when all imports are complete
                                if (['completed', 'failed', 'cancelled'].includes(data.status)) {
                                    // Remove completed import from tracking
                                    const index = inProgressImports.findIndex(imp => imp.id === data
                                        .id);
                                    if (index !== -1) {
                                        inProgressImports[index].tracker.destroy();
                                        inProgressImports.splice(index, 1);
                                    }

                                    // Reload if no more imports in progress
                                    if (inProgressImports.length === 0) {
                                        setTimeout(() => {
                                            window.location.reload();
                                        }, 1000);
                                    }
                                }
                            }
                        });
                    });

                    // Clean up all trackers when leaving page
                    window.addEventListener('beforeunload', function() {
                        inProgressImports.forEach(function(importData) {
                            if (importData.tracker) {
                                importData.tracker.destroy();
                            }
                        });
                    });
                }
            });

            // Quick filter functions
            function filterByStatus(status) {
                const statusSelect = document.querySelector('select[name="status"]');
                if (statusSelect) {
                    statusSelect.value = status;
                    statusSelect.form.submit();
                }
            }
        </script>
    @endpush
