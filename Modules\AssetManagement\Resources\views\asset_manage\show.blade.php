@extends('backend.layouts.app')
@section('title', @$title)

@section('content')
    <x-container cardClass="bg-transparent p-0" :title="@$title" buttonTitle="Back"
        buttonRoute="{{ route('assets.asset.index') }}">
        <div class="row mt-4">
            <!-- Left Column -->
            <div class="col-lg-3 col-md-6">
                <!-- Assigned User Info -->
                @if (@$show->assigned)
                    <div class="ot-card mb-20">
                        <h3 class="card-title text-16 fw-bold text-capitalize mb-0">{{ _trans('common.Assigned User Info') }}
                        </h3>
                        <div class="border-top mb-16 mt-16"></div>
                        <div class="assigner_user_card d-flex gap-16">
                            <div class="assigner_user_card_img wh-50px radius-4 flex-shrink-0 overflow-hidden">
                                <img class="w-100 h-100 object-fit-cover"
                                    src="{{ @$show->assigned->avatar ?: asset('assets/images/avatars/default.webp') }}"
                                    alt="user">
                            </div>
                            <div class="assign_card_info flex-fill d-flex flex-column gap-16">
                                <div class="assign_item">
                                    <span
                                        class="text-14 fw-normal text-subtitle d-block mb-6 text-uppercase">{{ _trans('common.Name') }}</span>
                                    <h5 class="text-14 fw-semibold text-capitalize text-title mb-0">
                                        {{ @$show->assigned->name }}</h5>
                                </div>
                                <div class="assign_item">
                                    <span
                                        class="text-14 fw-normal text-subtitle d-block mb-6 text-uppercase">{{ _trans('common.Designation') }}</span>
                                    <h5 class="text-14 fw-semibold text-capitalize text-title mb-0">
                                        {{ @$show->assigned->designation->title }}</h5>
                                </div>
                                <div class="assign_item">
                                    <span
                                        class="text-14 fw-normal text-subtitle d-block mb-6 text-uppercase">{{ _trans('common.Phone') }}</span>
                                    <h5 class="text-14 fw-semibold text-capitalize text-title mb-0">
                                        {{ @$show->assigned->phone }}</h5>
                                </div>
                                <div class="assign_item">
                                    <span
                                        class="text-14 fw-normal text-subtitle d-block mb-6 text-uppercase">{{ _trans('common.Email') }}</span>
                                    <h5 class="text-14 fw-semibold text-capitalize text-title mb-0">
                                        {{ @$show->assigned->email }}</h5>
                                </div>
                                <div class="assign_item">
                                    <span
                                        class="text-14 fw-normal text-subtitle d-block mb-6 text-uppercase">{{ _trans('common.Department') }}</span>
                                    <h5 class="text-14 fw-semibold text-capitalize text-title mb-0">
                                        {{ @$show->assigned->department->title }}</h5>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Warranty Expiry -->
                @if ($show->warranty_expiry)
                    <div class="ot-card mb-20">
                        <h3 class="card-title text-16 fw-bold text-capitalize mb-0">
                            {{ _trans('common.Warranty Information') }}</h3>
                        <div class="border-top mb-16 mt-16"></div>
                        <div class="assign_item">
                            <span
                                class="text-14 fw-normal text-subtitle d-block mb-6 text-uppercase">{{ _trans('common.Warranty Expiry Date') }}</span>
                            <h5 class="text-14 fw-semibold text-capitalize text-title mb-0">
                                {{ formatDateTime($show->warranty_expiry) }}</h5>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Middle Column -->
            <div class="col-lg-6 col-md-6">
                <div class="ot-card mb-20 p-2">
                    <ul class="nav primary-tabs" id="myTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="details-tab" data-bs-toggle="tab" data-bs-target="#details"
                                type="button" role="tab" aria-controls="details" aria-selected="true">
                                {{ _trans('common.Asset Details') }}
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="history-tab" data-bs-toggle="tab" data-bs-target="#history"
                                type="button" role="tab" aria-controls="history" aria-selected="false">
                                {{ _trans('common.Asset History') }}
                            </button>
                        </li>
                    </ul>
                </div>

                <!-- Tab Contents -->
                <div class="tab-content" id="myTabContent">
                    <!-- Asset Details Tab -->
                    <div class="tab-pane fade show active" id="details" role="tabpanel" aria-labelledby="details-tab">
                        <div class="ot-card mb-20">
                            <h3 class="card-title text-16 fw-bold text-capitalize mb-0">{{ _trans('common.Information') }}
                            </h3>
                            <div class="border-top mb-16 mt-16"></div>
                            <div class="assest-info-box mb-24">
                                <div class="assign_item">
                                    <span
                                        class="text-14 fw-normal text-subtitle d-block mb-6 text-uppercase">{{ _trans('common.Asset Code') }}</span>
                                    <h5 class="text-14 fw-semibold text-capitalize text-title mb-0">
                                        {{ @$show->asset_code }}</h5>
                                </div>
                                <div class="assign_item">
                                    <span
                                        class="text-14 fw-normal text-subtitle d-block mb-6 text-uppercase">{{ _trans('common.Asset Category') }}</span>
                                    <h5 class="text-14 fw-semibold text-capitalize text-title mb-0">
                                        {{ @$show->category->title }}</h5>
                                </div>
                                <div class="assign_item">
                                    <span
                                        class="text-14 fw-normal text-subtitle d-block mb-6 text-uppercase">{{ _trans('common.Brand') }}</span>
                                    <h5 class="text-14 fw-semibold text-capitalize text-title mb-0">{{ @$show->brand }}
                                    </h5>
                                </div>
                                <div class="assign_item">
                                    <span
                                        class="text-14 fw-normal text-subtitle d-block mb-6 text-uppercase">{{ _trans('common.Model') }}</span>
                                    <h5 class="text-14 fw-semibold text-capitalize text-title mb-0">{{ @$show->model }}
                                    </h5>
                                </div>
                                <div class="assign_item">
                                    <span
                                        class="text-14 fw-normal text-subtitle d-block mb-6 text-uppercase">{{ _trans('common.Asset Status') }}</span>
                                    <h5 class="text-14 fw-semibold text-capitalize text-title mb-0">
                                        <x-common.badge status="{{ ucfirst(@$show->status) }}" variant="success" />
                                    </h5>
                                </div>
                                <div class="assign_item">
                                    <span
                                        class="text-14 fw-normal text-subtitle d-block mb-6 text-uppercase">{{ _trans('common.Condition') }}</span>
                                    <h5 class="text-14 fw-semibold text-capitalize text-title mb-0">
                                        <x-common.badge status="{{ ucfirst(@$show->condition) }}" variant="danger" />
                                    </h5>
                                </div>
                                <div class="assign_item">
                                    <span
                                        class="text-14 fw-normal text-subtitle d-block mb-6 text-uppercase">{{ _trans('common.Maintenance Status') }}</span>
                                    <h5 class="text-14 fw-semibold text-capitalize text-title mb-0">
                                        <x-common.badge status="{{ ucfirst(@$show->maintenance_status) }}"
                                            variant="warning" />
                                    </h5>
                                </div>
                                <div class="assign_item">
                                    <span
                                        class="text-14 fw-normal text-subtitle d-block mb-6 text-uppercase">{{ _trans('common.Created At') }}</span>
                                    <h5 class="text-14 fw-semibold text-capitalize text-title mb-0">
                                        {{ formatDateTime(@$show->created_at) }}</h5>
                                </div>
                                <div class="assign_item">
                                    <span
                                        class="text-14 fw-normal text-subtitle d-block mb-6 text-uppercase">{{ _trans('common.Created By') }}</span>
                                    <h5 class="text-14 fw-semibold text-capitalize text-title mb-0">
                                        {{ @$show->createdBy->name }}</h5>
                                </div>
                                <div class="assign_item">
                                    <span
                                        class="text-14 fw-normal text-subtitle d-block mb-6 text-uppercase">{{ _trans('common.Updated By') }}</span>
                                    <h5 class="text-14 fw-semibold text-capitalize text-title mb-0">
                                        {{ @$show->updatedBy->name }}</h5>
                                </div>
                            </div>
                        </div>

                        <!-- Asset Information -->
                        <div class="ot-card mb-20">
                            <h3 class="card-title text-16 fw-bold text-capitalize mb-0">
                                {{ _trans('common.Assets Information') }}</h3>
                            <div class="border-top mb-16 mt-16"></div>
                            <div class="assest-info-box mb-24">
                                <div class="assign_item">
                                    <span
                                        class="text-14 fw-normal text-subtitle d-block mb-6 text-uppercase">{{ _trans('common.Purchase Date') }}</span>
                                    <h5 class="text-14 fw-semibold text-capitalize text-title mb-0">
                                        {{ formatDateTime(@$show->purchase_date) }}</h5>
                                </div>
                                <div class="assign_item">
                                    <span
                                        class="text-14 fw-normal text-subtitle d-block mb-6 text-uppercase">{{ _trans('common.Purchase Cost') }}</span>
                                    <h5 class="text-14 fw-semibold text-capitalize text-title mb-0">
                                        {{ number_format(@$show->purchase_cost, 2) }}</h5>
                                </div>
                                <div class="assign_item">
                                    <span
                                        class="text-14 fw-normal text-subtitle d-block mb-6 text-uppercase">{{ _trans('common.Warranty Expiry') }}</span>
                                    <h5 class="text-14 fw-semibold text-capitalize text-title mb-0">
                                        {{ formatDateTime(@$show->warranty_expiry) }}</h5>
                                </div>
                                <div class="assign_item">
                                    <span
                                        class="text-14 fw-normal text-subtitle d-block mb-6 text-uppercase">{{ _trans('common.Serial Number') }}</span>
                                    <h5 class="text-14 fw-semibold text-capitalize text-title mb-0">
                                        {{ @$show->serial_number }}</h5>
                                </div>
                                <div class="assign_item">
                                    <span
                                        class="text-14 fw-normal text-subtitle d-block mb-6 text-uppercase">{{ _trans('common.Invoice Number') }}</span>
                                    <h5 class="text-14 fw-semibold text-capitalize text-title mb-0">
                                        #{{ @$show->invoice_number }}</h5>
                                </div>
                                @if (@$show->vendor_location)
                                    <div class="assign_item">
                                        <span
                                            class="text-14 fw-normal text-subtitle d-block mb-6 text-uppercase">{{ _trans('common.Asset Location') }}</span>
                                        <h5 class="text-14 fw-semibold text-capitalize text-title mb-0">
                                            {{ @$show->vendor_location }}</h5>
                                    </div>
                                @endif
                            </div>
                        </div>

                        @if (@$show->description)
                            <div class="ot-card mb-20">
                                <h3 class="card-title text-16 fw-bold text-capitalize mb-0">
                                    {{ _trans('common.Description') }}</h3>
                                <div class="border-top mb-16 mt-16"></div>
                                <div class="assign_item">
                                    <p class="text-14 fw-normal text-subtitle mb-0">{{ @$show->description }}</p>
                                </div>
                            </div>
                        @endif

                    </div>

                    <!-- Asset History Tab -->
                    <div class="tab-pane fade" id="history" role="tabpanel" aria-labelledby="history-tab">
                        <div class="ot-card mb-20">
                            <h3 class="card-title text-16 fw-bold text-capitalize mb-0">
                                {{ _trans('common.Asset History') }}</h3>
                            <div class="border-top mb-16 mt-16"></div>
                            <div class="log_times_box">
                                <div class="appointment-schedule">
                                    <div class="timeline max-height-300 overflow-y-auto hide-scroll mt-0 p-0">
                                        @forelse ($show->allAssigny as $key => $row)
                                            <div class="timeline-item">
                                                <div class="time">
                                                    {{ date('h:i A', strtotime($row->logged_at)) }}<br><span>{{ date('d-m-Y', strtotime($row->logged_at)) }}</span>
                                                </div>
                                                <div class="circle"></div>
                                                <div class="content">
                                                    <div class="user">
                                                        <img src="{{ $row->user->avatar ?: asset('assets/images/avater.png') }}"
                                                            alt="onest-hrm" width="40">
                                                        <div>
                                                            <strong>{{ $row->user->name }}</strong><br />
                                                            <span>{{ $row->user->designation->title ?? 'N/A' }}</span>
                                                        </div>
                                                    </div>
                                                    <div class="status in">{{ $row->remarks ?: 'Assigned' }}</div>
                                                </div>
                                            </div>
                                        @empty
                                            <div class="text-center py-4">
                                                <p class="text-muted">{{ _trans('common.No history found') }}</p>
                                            </div>
                                        @endforelse
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div class="col-lg-3 col-md-6">
                <!-- Asset Image -->
                <div class="ot-card mb-20">
                    <h3 class="card-title text-16 fw-bold text-capitalize mb-0">{{ _trans('common.Asset Image') }}</h3>
                    <div class="border-top mb-16 mt-16"></div>
                    <div class="asset_image_box text-center">
                        <img class="img-fluid"
                            src="{{ @$show->attached_files ?: asset('assets/images/assets/asset_img.png') }}"
                            alt="{{ @$show->name }}">
                    </div>
                </div>

                <!-- QR Code -->
                <div class="ot-card mb-20">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="card-title text-16 fw-bold text-capitalize mb-0">{{ _trans('common.Asset QR Code') }}
                        </h3>
                        <a href="#" id="printQr">
                            <x-common.tyne-badge variant="primary">
                                <x-common.icons name="qr-code" class="text-primary" size="16" stroke-width="2" />
                                <span>{{ _trans('breaks.Print') }}</span>
                            </x-common.tyne-badge>
                        </a>
                    </div>
                    <div class="border-top mb-16 mt-16"></div>
                    <div class="asset_image_box text-center">
                        <div id="qrCodeContainer">
                            {!! QrCode::size(200)->style('square')->generate(route('assets.asset.view', $show->id)) !!}
                            <p class="mt-3 text-center">{{ _trans('common.Asset Code') }}: {{ @$show->asset_code }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </x-container>
@endsection

@push('script')
    <script>
        $('#printQr').on('click', function(e) {
            e.preventDefault();

            var contentHtml = $('#qrCodeContainer').clone(); // Corrected selector

            var printWindow = window.open('', '', 'width=1000,height=800');
            printWindow.document.write('<html><head><title>Print Asset Details</title>');
            printWindow.document.write(
                '<style>body{font-family:sans-serif;padding:20px;} svg{display:block;margin:20px auto;} h6{margin-bottom:20px;}</style>'
            );
            printWindow.document.write('</head><body>');
            printWindow.document.write(contentHtml.html()); // Use cloned content
            printWindow.document.write('</body></html>');

            printWindow.document.close();
            printWindow.focus();

            setTimeout(function() {
                printWindow.print();
                printWindow.close();
            }, 500);
        });
    </script>
@endpush
