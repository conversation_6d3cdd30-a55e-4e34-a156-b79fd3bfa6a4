@extends('backend.layouts.app')
@section('title', @$title)

@section('content')
    <div class="row">
        <div class="col-lg-9">
            <x-container :title="$title">
                <x-table bulk-action-model="assetCategory" :exportOption="true" buttonType="modal" modalId="assetCategory">
                    <table class="table table-bordered  " id="table">
                        <thead class="thead">
                            <tr>
                                <x-table.action.th></x-table.action.th>
                                <th class="w-90">{{ _trans('common.SL') }}</th>
                                <th>{{ _trans('common.Title') }}</th>
                                <th>{{ _trans('common.Date') }}</th>
                                <th>{{ _trans('common.Attachment') }}</th>
                                <th>{{ _trans('common.Status') }}</th>
                                <th class="w-90">{{ _trans('common.Action') }}</th>
                            </tr>

                        </thead>
                        <tbody class="tbody">
                            @forelse ($collection ?? [] as $row)
                                <tr>
                                    <x-table.action.td id="{{ $row->id }}"></x-table.action.td>
                                    <td>{{ @$collection->firstItem() + $loop->index }}</td>
                                    <td>{{ $row->title }}</td>
                                    <td>{{ formatDateTime($row->created_at) }}</td>
                                    <td>
                                        @if ($row->attachments)
                                            <img class="img-fluid rounded-circle" src="{{ $row->attachments }}"
                                                alt="{{ _trans('common.Attachment') }}" style="height: 50px; width:50px">
                                        @else
                                            --
                                        @endif
                                    </td>
                                    <td>
                                        <x-common.badge :status="$row->status" />
                                    </td>
                                    <td class="text-center">
                                        <x-table.action>
                                            @if (hasPermission('asset_category_update'))
                                                <a href="{{ route('assets.category.index', ['id' => $row->id]) }}"
                                                    class="dropdown-item">
                                                    <x-common.icons name="edit" class="text-title" size="16"
                                                        stroke-width="1.5" />
                                                    {{ _trans('common.Edit') }}
                                                </a>
                                            @endif
                                            @if (hasPermission('asset_category_delete'))
                                                <x-table.action.delete
                                                    url="{{ route('assets.category.destroy', $row->id) }}">
                                                    <x-common.icons name="trash" class="text-title" size="16"
                                                        stroke-width="1.5" />
                                                    {{ _trans('common.Delete') }}
                                                </x-table.action.delete>
                                            @endif

                                        </x-table.action>
                                    </td>
                                </tr>
                            @empty
                                <x-table.empty :colspan="7" />
                            @endforelse
                        </tbody>
                    </table>
                    <x-table.pagination :data="@$collection"></x-table.pagination>
                </x-table>
            </x-container>
        </div>

        <x-inner-form-layout :title="@$formTitle" modalId="assetCategoryForm">
            @include('assetmanagement::asset_categories.form', [
                'route' => @$model ? route('assets.category.update', @$model->id) : route('assets.category.store'),
                'type' => @$model ? 'PUT' : 'POST',
                'data' => @$model,
                'groupSelector' => false,
            ])
        </x-inner-form-layout>
    </div>
@endsection
