<?php

namespace Database\Seeders\Admin;

use App\Models\Role;
use App\Models\User;
use App\Models\UserWisePermission;
use Illuminate\Database\Seeder;

class UserWisePermissionSeeder extends Seeder
{
    public function run(): void
    {
        $role = Role::get();

        $adminPermission = json_encode($role->where('slug', 'super-admin')->first()->permissions->permissions);
        $hrPermission = json_encode($role->where('slug', 'hr')->first()->permissions->permissions);
        $staffPermission = json_encode($role->where('slug', 'staff')->first()->permissions->permissions);

        $users = User::select('id', 'role_id')->get();

        $data = [];
        foreach ($users as $user) {
            if ($user->role_id == 1) {
                $data[] = [
                    'user_id' => $user->id,
                    'permissions' => $adminPermission,
                ];
            } elseif ($user->role_id == 2) {
                $data[] = [
                    'user_id' => $user->id,
                    'permissions' => $hrPermission,
                ];
            } else {
                $data[] = [
                    'user_id' => $user->id,
                    'permissions' => $staffPermission,
                ];
            }
        }

        UserWisePermission::insert($data);
    }
}
