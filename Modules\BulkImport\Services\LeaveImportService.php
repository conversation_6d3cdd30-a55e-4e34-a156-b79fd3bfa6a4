<?php

declare(strict_types=1);

namespace Modules\BulkImport\Services;

use App\Models\Leave\LeaveAssign;
use App\Models\Leave\LeaveRequest;
use App\Models\User;
use App\Repositories\Leave\LeaveAssignRepository;
use App\Repositories\Leave\LeaveRequestRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class LeaveImportService
{
    public function __construct(
        protected LeaveRequestRepository $leaveRequestRepository,
        protected LeaveAssignRepository $leaveAssignRepository
    ) {}

    /**
     * Import leave request data following the same pattern as LeaveRequestRepository
     *
     * @param  array  $rows  CSV/Excel rows
     * @param  array  $mapping  Field mapping
     * @param  bool  $hasHeader  Whether file has header
     * @return array Import statistics
     */
    public function importLeaveRequests(array $rows, array $mapping, bool $hasHeader = true): array
    {
        $totalRows = count($rows);
        $importedRows = 0;
        $skippedRows = 0;
        $errorRows = 0;

        // Remove header if present
        if ($hasHeader && ! empty($rows)) {
            array_shift($rows);
            $totalRows--;
        }

        Log::info('Starting leave request import', [
            'total_rows' => $totalRows,
            'mapping' => $mapping,
        ]);

        foreach ($rows as $rowIndex => $row) {
            try {
                // Process the leave request data following LeaveRequestRepository pattern
                $result = $this->processLeaveRequestRow($row, $mapping, $rowIndex);

                if ($result['success']) {
                    $importedRows++;
                    Log::info('Leave request imported successfully', [
                        'row_index' => $rowIndex,
                        'leave_request_id' => $result['leave_request_id'] ?? null,
                    ]);
                } else {
                    $skippedRows++;
                    Log::warning('Leave request row skipped', [
                        'row_index' => $rowIndex,
                        'reason' => $result['reason'] ?? 'Unknown',
                    ]);
                }

            } catch (Exception $e) {
                $errorRows++;
                Log::error("Error importing leave request row {$rowIndex}: ".$e->getMessage(), [
                    'row' => $row,
                    'mapping' => $mapping,
                    'exception' => $e,
                ]);
            }
        }

        return [
            'total' => $totalRows,
            'imported' => $importedRows,
            'skipped' => $skippedRows,
            'errors' => $errorRows,
        ];
    }

    /**
     * Process a single leave request row following LeaveRequestRepository pattern
     */
    protected function processLeaveRequestRow(array $row, array $mapping, int $rowIndex): array
    {
        try {
            // Map CSV columns to database fields
            $mappedData = $this->mapRowData($row, $mapping);

            // Validate required fields
            if (empty($mappedData['user_id']) || empty($mappedData['leave_from']) || empty($mappedData['leave_to'])) {
                return [
                    'success' => false,
                    'reason' => 'Missing required fields (user_id, leave_from, or leave_to)',
                ];
            }

            // Check if user exists
            $user = User::find($mappedData['user_id']);
            if (! $user) {
                return [
                    'success' => false,
                    'reason' => "User with ID {$mappedData['user_id']} not found",
                ];
            }

            // Check if leave assign exists
            if (! empty($mappedData['leave_assign_id'])) {
                $leaveAssign = LeaveAssign::find($mappedData['leave_assign_id']);
                if (! $leaveAssign) {
                    return [
                        'success' => false,
                        'reason' => "Leave assign with ID {$mappedData['leave_assign_id']} not found",
                    ];
                }
            }

            // Validate dates
            try {
                $leaveFrom = Carbon::parse($mappedData['leave_from']);
                $leaveTo = Carbon::parse($mappedData['leave_to']);

                if ($leaveFrom->gt($leaveTo)) {
                    return [
                        'success' => false,
                        'reason' => 'Leave from date cannot be after leave to date',
                    ];
                }
            } catch (Exception $e) {
                return [
                    'success' => false,
                    'reason' => 'Invalid date format for leave_from or leave_to',
                ];
            }

            // Check if substitute exists
            if (! empty($mappedData['substitute_id'])) {
                $substitute = User::find($mappedData['substitute_id']);
                if (! $substitute) {
                    return [
                        'success' => false,
                        'reason' => "Substitute user with ID {$mappedData['substitute_id']} not found",
                    ];
                }
            }

            // Create leave request data
            $leaveRequestData = [
                'leave_assign_id' => $mappedData['leave_assign_id'] ?? null,
                'user_id' => $mappedData['user_id'],
                'apply_date' => $mappedData['apply_date'] ?? now(),
                'leave_from' => $mappedData['leave_from'],
                'leave_to' => $mappedData['leave_to'],
                'days' => $leaveFrom->diffInDays($leaveTo) + 1,
                'reason' => $mappedData['reason'] ?? null,
                'response' => $mappedData['response'] ?? null,
                'substitute_id' => $mappedData['substitute_id'] ?? null,
                'status' => $mappedData['status'] ?? 'pending',
                'referred_to' => $mappedData['referred_to'] ?? null,
                'company_id' => $user->company_id,
                'branch_id' => $user->branch_id,
                'manager_id' => $user->manager_id,
                'force_paid_leave' => $mappedData['force_paid_leave'] ?? false,
            ];

            // Create the leave request
            DB::beginTransaction();
            try {
                $leaveRequest = LeaveRequest::create($leaveRequestData);

                // Add action log
                $this->actionLog($leaveRequest, 'Bulk Import', 'Leave request imported via bulk import');

                DB::commit();

                return [
                    'success' => true,
                    'leave_request_id' => $leaveRequest->id,
                ];

            } catch (Exception $e) {
                DB::rollBack();
                throw $e;
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'reason' => 'Error processing row: '.$e->getMessage(),
            ];
        }
    }

    /**
     * Map CSV row data to database fields
     */
    protected function mapRowData(array $row, array $mapping): array
    {
        $mappedData = [];

        foreach ($mapping as $index => $field) {
            if ($field !== null && $field !== '' && isset($row[$index])) {
                $mappedData[$field] = trim($row[$index]);
            }
        }

        return $mappedData;
    }

    /**
     * Add action log to leave request
     */
    protected function actionLog($model, $action, $message = '', $comment = '', $referred_to = null): void
    {
        // Force JSON decode to associative array
        $history = json_decode($model->action_log, true) ?? [];

        $array = [
            'action_by' => Auth::id(),
            'referred_to' => $referred_to,
            'action_date' => now()->format('Y-m-d'),
            'action_time' => now()->format('H:i:s'),
            'action' => $action,
            'message' => $message,
            'ref_comment' => $comment,
        ];

        $history[] = $array;

        $model->update(['action_log' => json_encode($history)]);
    }

    /**
     * Get leave request fields with their data types
     */
    public function getLeaveRequestFieldsWithTypes(): array
    {
        return [
            'leave_assign_id' => 'integer',
            'user_id' => 'integer',
            'apply_date' => 'date',
            'leave_from' => 'date',
            'leave_to' => 'date',
            'days' => 'integer',
            'reason' => 'text',
            'response' => 'text',
            'substitute_id' => 'integer',
            'status' => 'enum',
            'referred_to' => 'integer',
            'force_paid_leave' => 'boolean',
        ];
    }

    /**
     * Get leave request column mapping for user-friendly display
     */
    public function getLeaveRequestColumnMapping(): array
    {
        return [
            'Leave Assign ID' => 'leave_assign_id',
            'Employee ID' => 'user_id',
            'Apply Date' => 'apply_date',
            'Leave From' => 'leave_from',
            'Leave To' => 'leave_to',
            'Days' => 'days',
            'Reason' => 'reason',
            'Response' => 'response',
            'Substitute ID' => 'substitute_id',
            'Status' => 'status',
            'Referred To' => 'referred_to',
            'Force Paid Leave' => 'force_paid_leave',
        ];
    }

    /**
     * Map user-friendly column names to database fields
     */
    public function mapLeaveRequestColumnsToFields(array $headings): array
    {
        $columnMapping = $this->getLeaveRequestColumnMapping();
        $mappedHeadings = [];

        foreach ($headings as $heading) {
            $mappedHeadings[] = $columnMapping[$heading] ?? $heading;
        }

        return $mappedHeadings;
    }
}
