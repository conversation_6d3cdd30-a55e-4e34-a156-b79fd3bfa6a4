@extends('backend.layouts.app')
@section('title', @$title)
@section('content')
    <x-container :title="@$title">
        <x-table :bulkAction="false" buttonTitle="Add New" buttonRoute="{{ route('assets.asset.create') }}"
            permission="schedule_create">
            <table class="table table-bordered" id="table">
                <thead class="thead">
                    <tr>
                        <th class="sorting-asc w-60 no-export">
                            <div class="check-box">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="all_check" />
                                </div>
                            </div>
                        </th>
                        <th class="w-60">{{ _trans('common.SL') }}</th>
                        <th class="sorting-asc">{{ _trans('common.Asset Name') }}</th>
                        <th class="sorting-asc">{{ _trans('common.Assigned To') }}</th>
                        <th class="sorting-asc">{{ _trans('common.Category') }}</th>
                        <th class="sorting-asc">{{ _trans('common.Serial Number') }}</th>
                        <th class="sorting-asc">{{ _trans('common.Asset Code') }}</th>
                        <th class="sorting-asc mxw-100">{{ _trans('common.Description') }}</th>
                        <th class="sorting-asc no-export">{{ _trans('common.Actions') }}</th>
                    </tr>
                </thead>
                <tbody class="tbody">
                    @forelse ($collection as $key => $row)
                        <tr>
                            <td class="no-export">
                                <div class="check-box">
                                    <div class="form-check">
                                        <input class="form-check-input column_id" id="column_{{ $row->id }}"
                                            onclick="columnID({{ $row->id }})" type="checkbox" name="column_id[]"
                                            value="{{ $row->id }}">
                                    </div>
                                </div>
                            </td>
                            <td>{{ $key + 1 }}</td>
                            <td class="w-40">
                                <div class="d-flex align-items-center gap-2">
                                    <div class="border rounded-circle wh-40px overflow-hidden bg-primary">
                                        <img class="img-fluid" src="{{ $row->attached_files }}" alt="{{ $row->name }}">
                                    </div>
                                    <div>
                                        <div class="d-flex align-items-center gap-1">
                                            <span class="text-12 fw-semibold text-primary">{{ $row->name }}</span>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="w-40">
                                @if ($row->assigned)
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="border rounded-circle wh-40px overflow-hidden bg-primary">
                                            <img class="img-fluid" src="{{ $row->assigned->avatar }}"
                                                alt="{{ $row->assigned->name }}">
                                        </div>
                                        <div>
                                            <div class="d-flex align-items-center gap-1">
                                                <span
                                                    class="text-12 fw-semibold text-primary">{{ $row->assigned->name }}</span>
                                                @if ($row->assigned->is_admin)
                                                    <x-common.badge status="Admin" />
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                @else
                                    --
                                @endif
                            </td>

                            <td>{{ $row->category->title }}</td>
                            <td>{{ $row->serial_number }}</td>
                            <td>{{ $row->asset_code }}</td>
                            <td>{{ \Illuminate\Support\Str::limit($row->description, 15, '...') }}</td>
                            <td class="no-export">
                                <div class="dropdown dropdown-action">
                                    <button type="button" class="btn-dropdown" data-bs-toggle="dropdown"
                                        aria-expanded="false">
                                        <i class="las la-ellipsis-h"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        @if (hasPermission('asset_read'))
                                            <a href="{{ route('assets.asset.view', ['id' => $row->id]) }}"
                                                class="dropdown-item">
                                                <x-common.icons name="eye" class="text-title" size="16"
                                                    stroke-width="1.5" />
                                                {{ _trans('common.show') }}
                                            </a>
                                        @endif
                                        @if (hasPermission('asset_update'))
                                            <a href="{{ route('assets.asset.edit', ['id' => $row->id]) }}"
                                                class="dropdown-item">
                                                <x-common.icons name="edit" class="text-title" size="16"
                                                    stroke-width="1.5" />
                                                {{ _trans('common.Edit') }}
                                            </a>
                                        @endif
                                        @if (hasPermission('asset_delete'))
                                            <x-table.action.delete url="{{ route('assets.asset.destroy', $row->id) }}">
                                            </x-table.action.delete>
                                        @endif
                                    </ul>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <x-table.empty :colspan="9" />
                    @endforelse
                </tbody>
            </table>
        </x-table>
        <div class="ot-pagination">
            {{ $collection->links() }}
        </div>
    </x-container>
@endsection
