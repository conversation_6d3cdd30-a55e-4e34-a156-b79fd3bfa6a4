<?php

namespace Modules\EmployeePerformance\Database\Seeders;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Modules\EmployeePerformance\Entities\Award;
use Modules\EmployeePerformance\Entities\AwardType;

class AwardSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run()
    {
        Award::truncate();
        AwardType::truncate();

        $employees = User::all();
        $awardTypes = collect();
        $awards = [];

        // Generate 60–70 AwardType records
        $totalAwardTypes = rand(60, 70);

        for ($i = 0; $i < $totalAwardTypes; $i++) {
            $name = 'Award Type '.($i + 1);
            $duration = rand(30, 180);
            $message = $name.' Message';

            $awardType = AwardType::create([
                'name' => $name,
                'duration' => $duration,
                'message' => $message,
                'status' => 'active',
                'created_by' => 1,
                'updated_by' => 1,
                'company_id' => 1,
                'branch_id' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            $awardTypes->push($awardType);

            // For each AwardType, generate 1 Award (or you can increase if needed)
            $employee = $employees->random();
            $date = Carbon::now()->subDays(rand(0, 180));
            $expireDate = (clone $date)->addDays($duration);

            $awards[] = [
                'user_id' => $employee->id,
                'award_type_id' => $awardType->id,
                'date' => $date->toDateString(),
                'description' => $message,
                'status' => 'active',
                'show_popup' => true,
                'expire_date' => $expireDate->toDateString(),
                'created_by' => 1,
                'updated_by' => 1,
                'company_id' => $employee->company_id ?? 1,
                'branch_id' => $employee->branch_id ?? 1,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Insert all awards at once
        Award::insert($awards);

        $this->command->info('✅ AwardType records: '.count($awardTypes));
        $this->command->info('✅ Award records: '.count($awards));
    }
}
