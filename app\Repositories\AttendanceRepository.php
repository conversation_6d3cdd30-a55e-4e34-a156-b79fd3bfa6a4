<?php

namespace App\Repositories;

use App\Models\Attendance\Attendance;
use App\Models\Attendance\Weekend;
use App\Models\Holiday;
use App\Models\User;
use App\Traits\LogAttendance;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Throwable;

class AttendanceRepository
{
    use LogAttendance;

    public function __construct(protected Attendance $attendance) {}

    public function getPaginateData($request)
    {
        $departmentId = $request->department_id ?? null;
        $searchKey = $request->search ?? null;
        $paginateSize = $request->limit ?? 15;
        $startDate = $request->from ?? null;
        $endDate = $request->to ?? null;

        $attendances = $this->attendance
            ->when($startDate && $endDate, fn ($query) => $query->whereBetween('check_in', [$startDate, $endDate]))
            ->when($request->user_id, fn ($query) => $query->where('user_id', $request->user_id))
            ->when($departmentId, fn ($query) => $query->whereHas('user.department', fn ($query) => $query->where('id', $departmentId)))
            ->when($searchKey, fn ($query) => $query->whereHas('user', fn ($query) => $query->where('name', 'like', "%$searchKey%")))
            ->orderBy('created_at', 'desc')
            ->with(['user:id,name,department_id', 'user.department:id,title'])
            ->paginate($paginateSize)
            ->through(function ($data) {
                return (object) [
                    'id' => $data->id,
                    'avatar' => @$data->user->avatar,
                    'name' => @$data->user->name,
                    'department' => @$data->user->department->title,
                    'date' => $data->date,
                    'checkin' => formatDateTime($data->check_in, 'g:i A'),
                    'checkout' => formatDateTime($data->check_out, 'g:i A'),
                    'stay_time' => timeInHourMinutes($data->stay_time),
                    'break_time' => timeInHourMinutes($data->break_time),
                    'worked_time' => timeInHourMinutes($data->worked_time),
                    'checkin_image' => getFilePath($data->check_in_image),
                    'checkout_image' => getFilePath($data->check_out_image),
                    'checkin_location' => $this->checkInLocation($data->check_in_info),
                    'checkout_location' => $this->checkOutLocation($data->check_out_info),
                ];
            });

        return $attendances;
    }

    public function checkIn($request)
    {
        $this->syncAttendance('check_in', $request);
    }

    public function breakStart($request = null)
    {
        $this->syncAttendance('break', $request);
    }

    public function breakEnd($request)
    {
        $this->syncAttendance('back', $request);
    }

    public function checkOut($request)
    {
        $this->syncAttendance('check_out', $request);
    }

    public function checkInLocation($checkInInfo)
    {
        return count($checkInInfo) > 0 ? $checkInInfo['location'] : 'N/A';
    }

    public function checkOutLocation($checkOutInfo)
    {
        return count($checkOutInfo) > 0 ? $checkOutInfo['location'] : 'N/A';
    }

    public function absentTable($request)
    {
        try {
            $today = Carbon::parse($request->date);

            // Check for holidays
            $holidayExists = Holiday::query()
                ->whereDate('start_date', '<=', $today)
                ->whereDate('end_date', '>=', $today)
                ->exists();

            if ($holidayExists) {
                return [];
            }

            // Check for weekends
            $todayName = $today->translatedFormat('l');

            $weekends = Weekend::query()
                ->where('is_weekend', true)
                ->pluck('name')
                ->map(fn ($name) => Str::lower($name))
                ->toArray();

            if (in_array(Str::lower($todayName), $weekends)) {
                return [];
            }

            $data = User::query()
                ->with(['department:id,title', 'designation:id,title'])
                ->where('status', 'active')
                ->when($request->search, fn ($q) => $q->where('name', 'like', '%'.$request->search.'%'))
                ->when($request->department, fn ($q) => $q->where('designation_id', $request->department))
                ->whereDoesntHave('leaveRequests', function ($q) use ($request) {
                    $q->whereDate('leave_from', '<=', $request->date)
                        ->whereDate('leave_to', '>=', $request->date)
                        ->whereIn('status_id', [1, 5]);
                })
                ->whereDoesntHave('attendances', function ($q) use ($request) {
                    $q->where('date', $request->date);
                })
                ->select('id', 'company_id', 'role_id', 'department_id', 'designation_id', 'name', 'employee_id');

            $data = $data->orderBy('name', 'asc')->paginate($request->limit ?? 2);

            return [
                'data' => $data->map(function ($data) {
                    return [
                        'id' => $data->id,
                        'date' => request('date'),
                        'name' => $data->name,
                        'employee_id' => $data->employee_id ?? '',
                        'department' => @$data->department->title,
                        'designation' => @$data->designation->title,
                    ];
                }),
                'pagination' => [
                    'total' => $data->total(),
                    'count' => $data->count(),
                    'per_page' => $data->perPage(),
                    'current_page' => $data->currentPage(),
                    'total_pages' => $data->lastPage(),
                    'pagination_html' => $data->links('backend.pagination.custom')->toHtml(),
                ],
            ];
        } catch (Throwable $th) {
            throw $th;
        }
    }
}
