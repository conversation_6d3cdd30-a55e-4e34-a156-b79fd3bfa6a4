@extends('backend.layouts.app')
@section('title', @$title)
@section('content')
    <div class="row">
        <div class="col-lg-12">
            <div class="ot-card mb-3">
                <div class="d-flex align-items-center justify-content-between gap-20 mb-24">
                    <h3 class="card-title d-flex align-items-center gap-10 mb-0">
                        <span>{{ _trans('payroll.Payroll Summary') }}</span>
                    </h3>
                </div>
                <div class="ot-card box-shadow-primary mb-24 border-left-success radius-4">
                    <div class="user-related-info d-flex align-items-center justify-content-between flex-wrap gap-20">
                        <div class="user-related-info-item d-flex align-items-center gap-10">
                            <div class="contents">
                                <h6 class="title text-primary text-20 fw-bold mb-8">
                                    {{ @$collections['payrollSummary']['employee_count'] }}
                                </h6>
                                <p class="paragraph mb-0 text-subtitle">
                                    {{ _trans('payroll.Total Employees') }}</p>
                            </div>
                        </div>
                        <span class="line-style"></span>
                        <div class="user-related-info-item d-flex align-items-center gap-10">
                            <div class="contents">
                                <h6 class="title text-danger text-20 fw-bold mb-8">
                                    ${{ @$collections['payrollSummary']['last_month_payroll'] }}
                                </h6>
                                <p class="paragraph mb-0 text-subtitle">
                                    {{ _trans('payroll.Last Month Payroll') }}
                                </p>
                            </div>
                        </div>
                        <span class="line-style"></span>
                        <div class="user-related-info-item d-flex align-items-center gap-10">
                            <div class="contents">
                                <h6 class="title text-success text-20 fw-bold mb-8">
                                    {{ @$collections['payrollSummary']['pending_approval'] }}
                                </h6>
                                <p class="paragraph mb-0 text-subtitle">
                                    {{ _trans('payroll.Pending Approvals') }}</p>
                            </div>
                        </div>
                        <span class="line-style"></span>
                        <div class="user-related-info-item d-flex align-items-center gap-10">
                            <div class="contents">
                                <h6 class="title text-warning text-20 fw-bold mb-8">
                                    {{ @$collections['payrollSummary']['completed_this_year'] }}
                                </h6>
                                <p class="paragraph mb-0 text-subtitle">
                                    {{ _trans('payroll.Completed This Year') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="row ">
        <div class="col-lg-12">
            <x-container :title="$title">
                <x-table :bulkAction="false" :exportOption="false" class="border-bottom" buttonTitle="Generate Salary"
                    buttonRoute="{{ route('payroll.salary-generates.create') }}">
                    <x-slot name="filters">
                        <div class="form-group">
                            <x-form.select :plain="true" name="department_id" :value="request('department_id')">
                                <option value="">{{ _trans('common.Department') }}</option>
                                @foreach ($departments as $department)
                                    <option value="{{ $department->id }}">{{ $department->title }}</option>
                                @endforeach
                            </x-form.select>
                        </div>
                        <div class="form-group">
                            <x-form.select :plain="true" name="payslip_type" :value="request('payslip_type')">
                                <option value="">{{ _trans('common.Select Payslip Type') }}</option>
                                <option value="weekly">{{ _trans('common.Weekly') }}</option>
                                <option value="monthly">{{ _trans('common.Monthly') }}</option>
                            </x-form.select>
                        </div>

                        <div class="form-group">
                            <x-form.select :plain="true" name="status" :value="request('status')">
                                <option value="">{{ _trans('common.Select Status') }}</option>
                                <option value="draft">{{ _trans('common.Draft') }}</option>
                                <option value="pending">{{ _trans('common.Pending') }}</option>
                                <option value="approved">{{ _trans('common.Approved') }}</option>
                                <option value="rejected">{{ _trans('common.Rejected') }}</option>
                                <option value="proccessed">{{ _trans('common.Proccessed') }}</option>
                            </x-form.select>
                        </div>
                    </x-slot>

                    <div class="table-contents">
                        @forelse (@$collections['items'] as $row)
                            <div class="list-item border-top d-block pt-6 hover-buttons hover-bg cursor-pointer">
                                <div class="d-flex align-items-center justify-content-between flex-wrap gap-20 p-3">
                                    <div class="d-flex gap-6 flex-column">
                                        <div class="d-flex align-items-center gap-10 flex-wrap mb-8">
                                            <h4 class="mb-0 text-14 fw-semibold">{{ $row['title'] }}</h4>
                                            <x-common.tyne-badge :status="$row['status']" class="fw-semibold radius-20 text-12"
                                                :data="[
                                                    'draft' => 'secondary',
                                                    'pending' => 'primary',
                                                    'approved' => 'success',
                                                    'rejected' => 'danger',
                                                    'proccessed' => 'primary',
                                                ]" />
                                            <x-common.tyne-badge :status="$row['payslip_type']" class="fw-semibold radius-20 text-12"
                                                :data="[
                                                    'hourly' => 'primary',
                                                    'daily' => 'secondary',
                                                    'weekly' => 'success',
                                                    'monthly' => 'info',
                                                ]" />
                                        </div>
                                        <div class="d-flex align-items-center gap-16">
                                            <div class="d-flex align-items-center gap-6">
                                                <x-common.icons name="wallet_check" class="text-subtitle" size="16"
                                                    stroke-width="1.5" />
                                                <span class="text-12 text-subtitle">UUID <span
                                                        class="fw-semibold text-title">{{ $row['uuid'] }}</span></span>
                                            </div>
                                            <div class="d-flex align-items-center gap-6">
                                                <x-common.icons name="users" class="text-subtitle" size="16"
                                                    stroke-width="1.5" />
                                                <span class="text-12 text-subtitle"><span
                                                        class="fw-semibold text-title">{{ $row['total_employee'] }}</span>
                                                    Employees</span>
                                            </div>
                                            <div class="d-flex align-items-center gap-6">
                                                <x-common.icons name="calendar" class="text-subtitle" size="16"
                                                    stroke-width="1.5" />
                                                <span class="text-12 text-subtitle">{{ $row['date'] }}</span>
                                            </div>
                                            <div class="d-flex align-items-center gap-6">
                                                <x-common.icons name="help" class="text-subtitle" size="16"
                                                    stroke-width="1.5" />
                                                <span class="text-12 text-subtitle">{{ $row['department'] }}</span>
                                            </div>
                                            <div class="d-flex align-items-center gap-6">
                                                <x-common.icons name="shapes" class="text-subtitle" size="16"
                                                    stroke-width="1.5" />
                                                <span class="text-12 text-subtitle">{{ $row['force_genarated'] }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <span class="text-14 fw-semibold">${{ $row['total_amount'] }}</span>
                                    </div>
                                    <div class="d-flex align-items-center gap-16">
                                        <a href="{{ route('payroll.salary-generates.department-employees', ['dept_id' => $row['department_id']]) }}"
                                            class="text-title">
                                            <x-common.icons name="eye" class="" size="20"
                                                stroke-width="1.5" />
                                        </a>
                                        @if (hasPermission('salary_generate_update'))
                                            <a href="{{ route('payroll.salary-generates.edit', ['id' => $row['id']]) }}"
                                                class="text-warning">
                                                <x-common.icons name="edit" class="text-title" size="20"
                                                    stroke-width="1.5" />
                                            </a>
                                        @endif
                                        @if (hasPermission('salary_generate_delete'))
                                            <x-table.action.delete
                                                url="{{ route('payroll.salary-generates.destroy', ['id' => $row['id']]) }}">
                                            </x-table.action.delete>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @empty
                            <x-table.empty :colspan="5" />
                        @endforelse
                    </div>
                    <x-table.pagination :data="@$collections['items']"></x-table.pagination>
                </x-table>
            </x-container>
        </div>
    </div>
@endsection
