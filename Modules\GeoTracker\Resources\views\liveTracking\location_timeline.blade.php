@extends('backend.layouts.app')

@section('title', $title)

@section('content')
    <div class="ot-card">
        <div class="row align-items-center">
            <div class="col-lg-12">
                <h3 class="card-title mb-0">{{ _trans('live_tracking.Location Timeline') }}</h3>
            </div>

            @if (@globalSetting('google_map_key', 'integration'))
                <div class="col-lg-4">
                    <h2>{{ formatDateTime(request('date')) }} </h2>
                    <hr>
                    <div class="scroll-step">
                        @forelse ($locationLogs  as $log)
                            <div class="step active">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h2 data-step-id="">{{ $log->address }}</h2>
                                    <p>{{ formatDateTime($log->created_at, 'F j, Y g:i A') }}</p>
                                </div>
                                <p class="text-center mt-3">{{ $log->created_at->diffForHumans() }}</p>
                            </div>
                        @empty
                            <div class="no-data-found-wrapper d-flex justify-content-center align-items-center">
                                <div class="text-center m-auto">
                                    <img src="{{ asset('assets/images/empty.png') }}" alt="onest-hrm"
                                        class="mb-primary empty-table">
                                    <h5 class="text-center">{{ _trans('common.No Record Found !') }}</h5>
                                </div>
                            </div>
                        @endforelse

                    </div>
                </div>
                <div class="col-lg-8">
                    <div class="row align-items-end  table-filter-data">
                        <div class="col-lg-12">
                            <form method="GET" action="#">
                                <div class="row align-items-center">
                                    <div class="col-xl-3">
                                        <div class="form-group">
                                            <select name="user" class="form-control select2 mb-3"
                                                onchange="submitForm()">
                                                <option value="">{{ _trans('common.Choose One') }}
                                                </option>
                                                @foreach ($users as $user)
                                                    <option {{ request('user') == $user->id ? 'selected="selected"' : '' }}
                                                        value="{{ $user->id }}">{{ $user->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-xl-3">
                                        <div class="form-group">
                                            <input type="date" id="start" name="date"
                                                class="form-control ot-form-control ot-input mt-3 mb-3"
                                                value="{{ request('date') ?? date('Y-m-d') }}">
                                        </div>
                                    </div>
                                    <div class="col-xl-2">
                                        <div class="form-group">
                                            <button type="submit"
                                                class="btn-primary-fill attendance_table_form height48">{{ _trans('common.Submit Now') }}</button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="row dataTable-btButtons">
                        <div class="col-lg-12">
                            <div class="ltn__map-area">
                                <div class="ltn__map-inner">
                                    <div id="map" class="mapH-500"></div>
                                    <div class="mt-5" id="directions_panel"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @else
                <div class="row p-5">
                    <div class="col-lg-12">
                        <h3 class="danger text-danger"> {{ _trans('error.Please set google map API Key') }}</h3>
                        <a href="{{ route('manage.integrations.view') }}"
                            class="btn btn-primary">{{ _trans('common.Update Google Map API Key') }}
                        </a>
                    </div>
                </div>
                <div class="row mt-3 mb-3  p-5">
                    <h3>Create a Google Cloud Platform (GCP) Project:</h3>
                    <div class="col-lg-12">
                        <ol>
                            <li>Go to the <a href="https://console.cloud.google.com/" target="_blank"
                                    class="text-info">Google Cloud Console</a>.</li>
                            <li>If you're not already signed in, sign in with your Google account.</li>
                            <li>Click the project drop-down at the top left of the page and then click "New
                                Project".
                            </li>
                            <li>Enter a name for your project, select an organization (if applicable), and choose a
                                billing account. Click "Create" to create the project.</li>
                        </ol>

                        <h3>Enable the Google Maps JavaScript API:</h3>
                        <ol>
                            <li>In the GCP Console, navigate to the "APIs &amp; Services" &gt; "Library" section.
                            </li>
                            <li>Search for "Google Maps JavaScript API" and click on it.</li>
                            <li>Click the "Enable" button.</li>
                        </ol>

                        <h3>Create an API Key:</h3>
                        <ol>
                            <li>In the "APIs &amp; Services" &gt; "Credentials" section, click on "Create
                                Credentials".
                            </li>
                            <li>Select "API key" from the drop-down menu.</li>
                            <li>Your API key will be displayed on the next screen. Make sure to restrict the usage
                                of
                                your API key for security purposes (optional but recommended).</li>
                        </ol>
                    </div>

                </div>
            @endif
        </div>
    </div>

    <input type="text" hidden id="data_url"
        value="{{ route('location.log', ['date' => request('date'), 'user' => request('user')]) }}">
@endsection

@push('script')
    <script src="https://maps.googleapis.com/maps/api/js?key={{ @globalSetting('google_map_key', 'integration') }}">
    </script>

    <script src="{{ asset('modules/geotracker/js/location_log.js') }}"></script>

    <script>
        function submitForm() {
            document.querySelector('form').submit();
        }
    </script>
@endpush
