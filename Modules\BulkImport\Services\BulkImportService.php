<?php

declare(strict_types=1);

namespace Modules\BulkImport\Services;

use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;

class BulkImportService
{
    /**
     * Returns a preview of the file (first n rows + headers)
     *
     * @param  string  $path  Full path to the file
     * @param  bool  $hasHeader  Whether the file has a header row
     * @return array Array with headings and rows
     *
     * @throws Exception If file format is not supported or file not found
     */
    public function previewFile(string $path, bool $hasHeader = true): array
    {
        // Check if file exists
        if (! File::exists($path)) {
            throw new Exception("File not found at path: {$path}");
        }

        $extension = Str::lower(pathinfo($path, PATHINFO_EXTENSION));
        $previewRows = config('bulkimport.preview_rows', 20);

        return match ($extension) {
            'xls', 'xlsx' => $this->previewExcel($path, $previewRows),
            'csv' => $this->previewCsv($path, $hasHeader, $previewRows),
            'txt' => $this->previewCsv($path, $hasHeader, $previewRows), // Treat txt as CSV
            default => throw new Exception("Unsupported file format: {$extension}"),
        };
    }

    /**
     * Preview CSV file
     *
     * @param  string  $path  File path
     * @param  bool  $hasHeader  Whether the file has a header row
     * @param  int  $previewRows  Number of rows to preview
     * @return array Array with headings and rows
     *
     * @throws Exception If file cannot be read
     */
    protected function previewCsv(string $path, bool $hasHeader = true, int $previewRows = 5): array
    {
        try {
            $rows = array_map('str_getcsv', file($path));

            if (empty($rows)) {
                return ['headings' => [], 'rows' => []];
            }

            $headings = $hasHeader ? array_shift($rows) : range(0, count($rows[0] ?? []) - 1);

            return ['headings' => $headings, 'rows' => array_slice($rows, 0, $previewRows)];
        } catch (Exception $e) {
            throw new Exception('Error reading CSV file: '.$e->getMessage());
        }
    }

    /**
     * Preview Excel file
     *
     * @param  string  $path  File path
     * @param  int  $previewRows  Number of rows to preview
     * @return array Array with headings and rows
     *
     * @throws Exception If file cannot be read
     */
    protected function previewExcel(string $path, int $previewRows = 5): array
    {
        try {
            $collection = Excel::toCollection(null, $path)->first();

            if ($collection->isEmpty()) {
                return ['headings' => [], 'rows' => []];
            }

            $headings = $collection->first()->keys()->toArray();
            $rows = $collection->skip(1)->take($previewRows)->toArray();

            return ['headings' => $headings, 'rows' => $rows];
        } catch (Exception $e) {
            throw new Exception('Error reading Excel file: '.$e->getMessage());
        }
    }

    /**
     * Get model fields with their data types
     *
     * @param  mixed  $model  An instance of the model
     * @return array Array with field => type mapping
     */
    public function getModelFieldsWithTypes($model): array
    {
        $fieldsWithTypes = [];

        // Try to get the table name
        $table = $model->getTable();

        // Get columns info from database
        try {
            $columns = DB::select("SHOW COLUMNS FROM {$table}");

            // Get fillable fields - handle both $fillable and $guarded
            $fillableFields = $model->getFillable();

            // If no fillable fields defined, get all columns except guarded ones
            if (empty($fillableFields)) {
                $guardedFields = $model->getGuarded();
                foreach ($columns as $column) {
                    if (! in_array($column->Field, $guardedFields)) {
                        $fillableFields[] = $column->Field;
                    }
                }
            }

            foreach ($columns as $column) {
                if (in_array($column->Field, $fillableFields)) {
                    $type = $column->Type;
                    // Extract basic type (e.g., varchar(255) -> varchar)
                    if (strpos($type, '(') !== false) {
                        $type = substr($type, 0, strpos($type, '('));
                    }
                    $fieldsWithTypes[$column->Field] = $type;
                }
            }
        } catch (Exception $e) {
            // If we can't get database info, provide basic types
            $fillableFields = $model->getFillable();
            if (empty($fillableFields)) {
                // Fallback to common user fields if no fillable defined
                $fillableFields = ['name', 'email', 'phone', 'department_id', 'designation_id', 'joining_date', 'status'];
            }
            foreach ($fillableFields as $field) {
                $fieldsWithTypes[$field] = 'unknown';
            }
        }

        return $fieldsWithTypes;
    }

    /**
     * Get employee fields with types for comprehensive import
     *
     * @return array Array with field => type mapping
     */
    public function getEmployeeFieldsWithTypes(): array
    {
        return [
            // Basic Employee Information
            'name' => 'varchar',
            'email' => 'varchar',
            'employee_id' => 'varchar',
            'phone' => 'varchar',
            'status' => 'varchar',

            // Personal Information
            'date_of_birth' => 'date',
            'gender' => 'varchar',
            'address' => 'text',
            'emergency_contact' => 'varchar',
            'marital_status' => 'varchar',
            'nationality' => 'varchar',
            'blood_group' => 'varchar',
            'religion' => 'varchar',

            // Official Information
            'department_id' => 'int',
            'designation_id' => 'int',
            'manager_id' => 'int',
            'role_id' => 'int',
            'joining_date' => 'date',
            'employee_type' => 'varchar',
            'work_location' => 'varchar',
            'reporting_manager' => 'varchar',

            // Document Information
            'passport_number' => 'varchar',
            'driving_license' => 'varchar',
            'pan_number' => 'varchar',
            'aadhar_number' => 'varchar',
            'bank_account' => 'varchar',
            'ifsc_code' => 'varchar',
            'bank_name' => 'varchar',

            // Salary Information
            'basic_salary' => 'decimal',
            'allowances' => 'json',
            'deductions' => 'json',
            'salary_type' => 'varchar',
            'payment_method' => 'varchar',
            'bank_details' => 'json',
        ];
    }

    /**
     * Get mapping from user-friendly column names to database field names
     * Based on the actual form fields from the employee creation process
     *
     * @return array Array with user-friendly name => database field mapping
     */
    public function getEmployeeColumnMapping(): array
    {
        return [
            // Employee Setup (Step 1) - storeEmployeeSetup
            'Employee Name' => 'name',
            'Email Address' => 'email',
            'Phone Number' => 'phone',
            'Employee ID' => 'employee_id',
            'Job Type' => 'job_type',
            'Department' => 'department_id',
            'Designation' => 'designation_id',
            'Role' => 'role_id',
            'Status' => 'status',
            'Joining Date' => 'joining_date',
            'Duty Schedule' => 'duty_schedule_id',
            'Attendance Method' => 'attendance_method',

            // Personal Information (Step 2) - storePersonalInfo
            'Country' => 'country_id',
            'Religion' => 'religion',
            'Blood Group' => 'blood_group',
            'Speak Language' => 'speak_language',
            'Gender' => 'gender',
            'Address' => 'address',
            'Marital Status' => 'marital_status',
            'Date of Birth' => 'birth_date',

            // Official Setup (Step 3) - updateOfficialSetup
            'Manager' => 'manager_id',
            'Weekend' => 'weekend',

            // Personal Documents (Step 4) - updatePersonalDocument
            'Passport Number' => 'passport_number',
            'Driving License' => 'driving_license',
            'PAN Number' => 'tin_number',
            'Aadhar Number' => 'nid_card_number',

            // Salary Configuration (Step 5) - updateSalaryConfiguration
            'Contract Start Date' => 'contract_start_date',
            'Payslip Type' => 'payslip_type',
            'Gross Salary' => 'gross_salary',
            'Basic Salary' => 'basic_salary',
            'Payment Method' => 'payment_method',
            'Bank Name' => 'bank_name',
            'Bank Account Number' => 'bank_account',
        ];
    }

    /**
     * Get attendance fields with types for comprehensive import
     *
     * @return array Array with field => type mapping
     */
    public function getAttendanceFieldsWithTypes(): array
    {
        return [
            'employee_id' => 'varchar',
            'date' => 'date',
            'time' => 'time',
            'type' => 'varchar',
        ];
    }

    /**
     * Get mapping from user-friendly column names to attendance database field names
     *
     * @return array Array with user-friendly name => database field mapping
     */
    public function getAttendanceColumnMapping(): array
    {
        return [
            'Employee ID' => 'employee_id',
            'Date' => 'date',
            'Time' => 'time',
            'Type' => 'type',
        ];
    }

    /**
     * Map user-friendly column names to attendance database field names
     *
     * @param  array  $userColumns  Array of user-friendly column names
     * @return array Array of database field names
     */
    public function mapAttendanceColumnsToFields(array $userColumns): array
    {
        $mapping = $this->getAttendanceColumnMapping();
        $mappedFields = [];

        foreach ($userColumns as $column) {
            $mappedFields[] = $mapping[$column] ?? $column; // Fallback to original if no mapping found
        }

        return $mappedFields;
    }

    /**
     * Map user-friendly column names to database field names
     *
     * @param  array  $userColumns  Array of user-friendly column names
     * @return array Array of database field names
     */
    public function mapUserColumnsToFields(array $userColumns): array
    {
        $mapping = $this->getEmployeeColumnMapping();
        $mappedFields = [];

        foreach ($userColumns as $column) {
            $mappedFields[] = $mapping[$column] ?? $column; // Fallback to original if no mapping found
        }

        return $mappedFields;
    }

    /**
     * Get leave request fields with types for comprehensive import
     *
     * @return array Array with field => type mapping
     */
    public function getLeaveRequestFieldsWithTypes(): array
    {
        return [
            'leave_assign_id' => 'integer',
            'user_id' => 'integer',
            'apply_date' => 'date',
            'leave_from' => 'date',
            'leave_to' => 'date',
            'days' => 'integer',
            'reason' => 'text',
            'response' => 'text',
            'substitute_id' => 'integer',
            'status' => 'enum',
            'referred_to' => 'integer',
            'force_paid_leave' => 'boolean',
        ];
    }

    /**
     * Get mapping from user-friendly column names to leave request database field names
     *
     * @return array Array with user-friendly name => database field mapping
     */
    public function getLeaveRequestColumnMapping(): array
    {
        return [
            'Leave Assign ID' => 'leave_assign_id',
            'Employee ID' => 'user_id',
            'Apply Date' => 'apply_date',
            'Leave From' => 'leave_from',
            'Leave To' => 'leave_to',
            'Days' => 'days',
            'Reason' => 'reason',
            'Response' => 'response',
            'Substitute ID' => 'substitute_id',
            'Status' => 'status',
            'Referred To' => 'referred_to',
            'Force Paid Leave' => 'force_paid_leave',
        ];
    }

    /**
     * Map user-friendly column names to leave request database field names
     *
     * @param  array  $userColumns  Array of user-friendly column names
     * @return array Array of database field names
     */
    public function mapLeaveRequestColumnsToFields(array $userColumns): array
    {
        $mapping = $this->getLeaveRequestColumnMapping();
        $mappedFields = [];

        foreach ($userColumns as $column) {
            $mappedFields[] = $mapping[$column] ?? $column; // Fallback to original if no mapping found
        }

        return $mappedFields;
    }
}
