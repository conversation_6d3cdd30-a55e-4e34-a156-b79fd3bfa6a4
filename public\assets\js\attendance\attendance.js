
var url = $("#url").val();
var reason = '';
var currentStream = null; // global for video stream
var checkUrl;

// ====== HOLD BUTTON LOGIC ======
function btnHold() {
    let duration = 1200;

    document.querySelectorAll(".check-in-out-button-hold").forEach((button) => {
        button.style.setProperty("--duration", duration + "ms");

        ["mousedown", "touchstart", "keypress"].forEach((e) => {
            button.addEventListener(e, (ev) => {

                const modalReasonTextarea = $('.late_or_early_reason_modal');
                const reasonTextarea = $('.late_or_early_reason');
                const $target = (modalReasonTextarea.length && modalReasonTextarea.data('modal')) ? modalReasonTextarea : reasonTextarea;

                if (!validateReason($target)) return false;
                reason = $target.val() ?? '';

                if (e != "keypress" || (e == "keypress" && ev.which == 32 && !button.classList.contains("process"))) {
                    button.classList.add("process");

                    // Hold duration
                    button.timeout = setTimeout(() => {
                        $(".progress").hide();

                        if ($('#selfie_based_attendance').val() == 1) {
                            const $cameraModal = $("#cameraModal");
                            $cameraModal.removeClass("d-none");

                            captureSelfie().then((selfieFile) => {
                                checkIn($("#checkInOrCheckOutUrl").val(), selfieFile);
                            }).catch((err) => {
                                // Toast.fire({ icon: "error", title: err.message });
                                button.classList.remove("success");
                            });
                        } else {
                            checkIn($("#checkInOrCheckOutUrl").val(), null);
                        }
                        button.classList.add("success");
                    }, duration);

                }
            });
        });

        ["mouseup", "mouseout", "touchend", "keyup"].forEach((e) => {
            button.addEventListener(e, (ev) => {
                if (e != "keyup" || (e == "keyup" && ev.which == 32)) {
                    button.classList.remove("process");
                    clearTimeout(button.timeout);
                }
            }, false);
        });
    });
}

// ====== CAPTURE SELFIE ======
function captureSelfie() {
    return new Promise((resolve, reject) => {
        const errorBox = $("#cameraError");
        const video = document.getElementById("videoPreview");
        const canvas = document.getElementById("snapshotCanvas");
        const cameraModal = new bootstrap.Modal(document.getElementById('cameraModal'));
        cameraModal.show();

        // Start webcam
        navigator.mediaDevices.getUserMedia({ video: true })
            .then(stream => {
                video.srcObject = stream;
                video.play();
                currentStream = stream;

                // Capture button click
                $(document).off("click", "#captureBtn").on("click", "#captureBtn", function () {
                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;
                    const ctx = canvas.getContext("2d");
                    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

                    // Stop camera
                    if (currentStream) {
                        currentStream.getTracks().forEach(track => track.stop());
                        currentStream = null;
                    }
                    cameraModal.hide();

                    // Convert canvas to File
                    canvas.toBlob((blob) => {
                        if (!blob) {
                            reject("No selfie captured");
                        } else {
                            const file = new File([blob], 'attendance_' + Date.now() + '.png', { type: 'image/png' });
                            resolve(file);
                        }
                    }, 'image/png');
                });
            })
            .catch(err => {
                errorBox.removeClass("d-none")
                    .html("❌ Unable to access webcam. Please check your camera connection or permissions.");
                reject(err);
            });

        // Close modal cleanup
        $('#cameraModal').off('hidden.bs.modal').on('hidden.bs.modal', function () {
            if (currentStream) {
                currentStream.getTracks().forEach(track => track.stop());
                currentStream = null;
                location.reload();
            }
        });
    });
}

// ====== VALIDATE REASON ======
function validateReason($el) {
    if (!$el.length) return true;

    const val = ($el.val() || '').trim();
    if (!val) {
        $el.removeClass('d-none hidden').show();
        $el.trigger('focus');

        Toast.fire({
            icon: 'error',
            title: 'Please Provide a Reason.',
            didOpen: () => $el.trigger('focus')
        });

        return false;
    }
    return true;
}

// ====== CHECK IN LOGIC ======
var checkIn = (url, selfieFile = null) => {
    checkUrl = url;
    if (navigator?.geolocation) {
        navigator.geolocation.getCurrentPosition(
            (pos) => attendanceStore(pos, selfieFile),
            (err) => positionError(err, selfieFile),
            { timeout: 10000 }
        );
    } else {
        attendanceStore(null, selfieFile);
    }
};

function positionError(error, selfieFile) {
    Toast.fire({
        icon: "error",
        title: error.message ?? "Something went wrong!",
    });
    $(".progress").show();
    $("#check-in-out-button-hold").removeClass("success");

    attendanceStore(null, selfieFile);
}

// ====== ATTENDANCE STORE ======
function attendanceStore(position = null, selfieFile = null) {
    let formData = new FormData();
    formData.append('latitude', position?.coords?.latitude ?? '');
    formData.append('longitude', position?.coords?.longitude ?? '');
    formData.append('reason', reason ?? '');
    if (selfieFile) formData.append('selfie_image', selfieFile);
    formData.append('_token', $('meta[name="csrf-token"]').attr("content"));

    $.ajax({
        type: "POST",
        url: checkUrl,
        data: formData,
        processData: false, // important for FormData
        contentType: false, // important for FormData
        success: function (data) {
            if (data?.success) {
                Toast.fire({ icon: "success", title: data.message, timer: 1500 });
                setTimeout(() => location.reload(), 1500);
            }
        },
        error: function (data) {
            if (data?.responseJSON?.message) {
                Toast.fire({ icon: "error", title: data?.responseJSON?.message ?? "Something went wrong." });
                $(".progress").show();
                $("#check-in-out-button-hold").removeClass("success");
            }
        },
    });
}

btnHold();


