@extends('backend.layouts.app')
@section('title', 'Attendance Record Details')
@section('content')
    <div class="row">
        <div class="col-lg-12">
            <div class="ot-card mb-3">
                <div class="d-flex align-items-center justify-content-between gap-20 mb-24">
                    <h3 class="card-title d-flex align-items-center gap-10 mb-0">
                        <span>{{ _trans('payroll.Employee Review') }}</span>
                    </h3>
                    <a href="#" class="btn-primary-fill">
                        {{ _trans('common.Export') }}
                    </a>
                </div>

                <div class="ot-card box-shadow-primary border-left-success radius-4 p-30">
                    <div class="user-related-info d-flex align-items-center justify-content-between flex-wrap gap-20">
                        <div class="user-related-info-item d-flex align-items-center gap-10">
                            <div class="contents">
                                <h6 class="title text-16 fw-bold mb-8">
                                    {{ @$attendanceRecords['employeeReview']['employee_id'] }}
                                </h6>
                                <p class="paragraph mb-0 text-subtitle fw-semibold ">
                                    {{ _trans('payroll.Employee ID') }}</p>
                            </div>
                        </div>
                        <span class="line-style"></span>
                        <div class="user-related-info-item d-flex align-items-center gap-10">
                            <div class="contents">
                                <h6 class="title text-16 fw-bold mb-8">
                                    {{ @$attendanceRecords['employeeReview']['employee_name'] }}
                                </h6>
                                <p class="paragraph mb-0 text-subtitle fw-semibold">
                                    {{ _trans('payroll.Employee Name') }}
                                </p>
                            </div>
                        </div>
                        <span class="line-style"></span>
                        <div class="user-related-info-item d-flex align-items-center gap-10">
                            <div class="contents">
                                <h6 class="title text-16 fw-bold mb-8">
                                    {{ @$attendanceRecords['employeeReview']['employee_department'] }}
                                </h6>
                                <p class="paragraph mb-0 text-subtitle fw-semibold">
                                    {{ _trans('payroll.Department') }}</p>
                            </div>
                        </div>
                        <span class="line-style"></span>
                        <div class="user-related-info-item d-flex align-items-center gap-10">
                            <div class="contents">
                                <h6 class="title text-16 fw-bold mb-8">
                                    Monthly, Jul 2025
                                </h6>
                                <p class="paragraph mb-0 text-subtitle fw-semibold">
                                    {{ _trans('payroll.Period') }}</p>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <x-container :title="_trans('payroll.Attendance Record Details')">
                <div class="ot-card bg-primary-soft border mb-3">

                    <div class="user-related-info d-flex align-items-center justify-content-between flex-wrap gap-20">

                        <div class="user-related-info-item d-flex align-items-center gap-10">
                            <div class="contents">

                                <h6 class="title text-20 text-primary fw-bold mb-6">
                                    {{ @$attendanceRecords['attendanceDetail']['total_day'] }}
                                </h6>
                                <p class="paragraph mb-0 text-title fw-medium">{{ _trans('payroll.Total Working Days') }}
                                </p>
                            </div>
                        </div>
                        <span class="line-style"></span>
                        <div class="user-related-info-item d-flex align-items-center gap-10">
                            <div class="contents">
                                <h6 class="title text-20 text-success fw-bold mb-6">
                                    {{ @$attendanceRecords['attendanceDetail']['total_hour'] }}
                                </h6>
                                <p class="paragraph mb-0 text-title fw-medium">{{ _trans('payroll.Total Working Hours') }}
                                </p>
                            </div>
                        </div>
                        <span class="line-style"></span>
                        <div class="user-related-info-item d-flex align-items-center gap-10">
                            <div class="contents">
                                <h6 class="title text-20 text-info fw-bold mb-6">
                                    {{ @$attendanceRecords['attendanceDetail']['total_over_time'] }}
                                </h6>
                                <p class="paragraph mb-0 text-title fw-medium">{{ _trans('payroll.Total Overtime') }}</p>
                            </div>
                        </div>
                        <span class="line-style"></span>
                        <div class="user-related-info-item d-flex align-items-center gap-10">
                            <div class="contents">
                                <h6 class="title text-20 text-danger fw-bold mb-6">
                                    {{ @$attendanceRecords['attendanceDetail']['total_tardy'] }}
                                </h6>
                                <p class="paragraph mb-0 text-title fw-medium">{{ _trans('payroll.Total Tardy') }}</p>
                            </div>
                        </div>
                        <span class="line-style"></span>
                        <div class="user-related-info-item d-flex align-items-center gap-10">
                            <div class="contents">
                                <h6 class="title text-20 text-warning fw-bold mb-6">
                                    {{ @$attendanceRecords['attendanceDetail']['total_leave'] }}
                                    {{ _trans('common.Days') }}
                                </h6>
                                <p class="paragraph mb-0 text-title fw-medium">{{ _trans('payroll.Total Leave') }}</p>
                            </div>
                        </div>
                        <span class="line-style"></span>
                        <div class="user-related-info-item d-flex align-items-center gap-10">
                            <div class="contents">
                                <h6 class="title text-20 text-danger fw-bold mb-6">
                                    {{ @$attendanceRecords['attendanceDetail']['total_late'] }}
                                </h6>
                                <p class="paragraph mb-0 text-title fw-medium">{{ _trans('payroll.Total Late') }}</p>
                            </div>
                        </div>

                    </div>
                </div>

                <form action="{{ route('payroll.salary-generates.summary-details') }}" method="POST">
                    @csrf
                    <x-table :bulkAction="false" :exportOption="false" :defaultOption="false" class="border-bottom">
                        <table class="table table-bordered  " id="table">
                            <thead class="thead">
                                <tr>
                                    <th>{{ _trans('common.Sl') }}</th>
                                    <th>{{ _trans('common.Date') }}</th>
                                    <th>{{ _trans('common.Day') }}</th>
                                    <th>{{ _trans('common.Check In') }}</th>
                                    <th>{{ _trans('common.Check Out') }}</th>
                                    <th>{{ _trans('common.Working Hours') }}</th>
                                    <th>{{ _trans('common.Overtime') }}</th>
                                    <th>{{ _trans('common.Status') }}</th>
                                </tr>
                            </thead>
                            <tbody class="tbody">
                                @forelse (@$attendanceRecords['items'] as $row)
                                    <input type="hidden" name="user_id" value="{{ $row['user_id'] }}">
                                    <tr data-id="{{ $row['id'] }}">
                                        <input type="hidden" name="data[{{ $row['id'] }}][id]"
                                            value="{{ $row['id'] }}">
                                        <td>{{ @$attendanceRecords['items']->firstItem() + $loop->index }}</td>
                                        <td>{{ $row['formatted_date'] }}</td>
                                        <td>{{ $row['day'] }}</td>
                                        <td>
                                            <input type="time" class="form-control"
                                                name="data[{{ $row['id'] }}][check_in]"
                                                value="{{ date('H:i', strtotime($row['check_in'])) }}">
                                        </td>
                                        <td>
                                            <input type="time" class="form-control"
                                                name="data[{{ $row['id'] }}][check_out]"
                                                value="{{ date('H:i', strtotime($row['check_out'])) }}">
                                        </td>
                                        <td>{{ $row['worked_time'] ?? '--' }}</td>
                                        <td>{{ $row['over_time'] ?? '--' }}</td>
                                        <td>
                                            <x-common.tyne-badge :status="$row['status'] == 'check_in'
                                                ? 'Check In'
                                                : ($row['status'] == 'check_out'
                                                    ? 'Check Out'
                                                    : 'Break')" class="fw-semibold radius-20 text-12"
                                                :data="[
                                                    'Check In' => 'success',
                                                    'Check Out' => 'primary',
                                                    'Break' => 'info',
                                                ]" />
                                        </td>
                                    </tr>
                                @empty
                                    <x-table.empty :colspan="8" />
                                @endforelse
                            </tbody>
                        </table>

                    </x-table>
                    <div class="d-flex justify-content-end gap-10 mt-3">
                        <button type="submit" class="btn-primary-fill">
                            {{ _trans('common.Calculate Salary') }}
                        </button>
                    </div>
                </form>
            </x-container>
        </div>
    </div>
@endsection
