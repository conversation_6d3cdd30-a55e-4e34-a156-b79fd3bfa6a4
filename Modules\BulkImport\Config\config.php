<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Bulk Import Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for the bulk import functionality.
    |
     */

    // Route prefix for the bulk import routes
    'route_prefix' => 'admin/bulk-import',

    // Route middleware - adjust based on your application's authentication system
    'middleware' => ['web', 'auth'],

    // API route middleware
    'api_middleware' => ['api', 'auth:sanctum'],

    // Available models for import
    'models' => [
        'employee' => 'App\Models\User',
        'attendance' => 'App\Models\Attendance\Attendance',
        'leave' => 'App\Models\Leave\LeaveRequest',
        // Add your models here
        // 'product' => 'App\Models\Product',
        // 'customer' => 'App\Models\Customer',
    ],

    // Maximum number of preview rows
    'preview_rows' => 10,

    // Default disk for storing uploaded files
    'disk' => 'public',

    // Directory within the disk where files will be stored
    'directory' => 'imports',

    // Supported file formats
    'supported_formats' => [
        'csv' => true,
        'xls' => true,
        'xlsx' => true,
        'txt' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Queue Configuration
    |--------------------------------------------------------------------------
    |
    | This package uses Laravel's queue system. These settings can be configured
    | in your application's queue.php config file. The values here are just
    | defaults used by the package when initializing jobs.
    |
     */
    'queue' => [
        // Uses the 'imports' connection from config/queue.php
        'connection' => env('BULK_IMPORT_QUEUE_CONNECTION', 'imports'),

        // The queue name to use within the connection
        'name' => env('BULK_IMPORT_QUEUE_NAME', 'import'),

        // Job timeout and retry settings
        'timeout' => env('BULK_IMPORT_QUEUE_TIMEOUT', 900),
        'tries' => env('BULK_IMPORT_QUEUE_TRIES', 3),
    ],

    /*
    |--------------------------------------------------------------------------
    | Broadcasting Configuration
    |--------------------------------------------------------------------------
    |
    | Configure real-time updates for import processes using Laravel's
    | broadcasting system.
    |
     */
    'broadcasting' => [
        // Whether to broadcast import status updates
        'enabled' => true,

        // Broadcast driver to use (uses application's broadcast_driver by default)
        'driver' => env('BROADCAST_DRIVER', 'reverb'),

        // Channel to use for broadcasting updates
        'channel' => 'import',

        // Whether to use private channels (requires authentication)
        'use_private' => true,
    ],
];
