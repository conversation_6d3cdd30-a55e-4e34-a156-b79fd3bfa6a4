@php use Carbon\Carbon; @endphp
@extends('backend.layouts.app')
@section('title', @$title)
@section('content')
    <x-container :title="@$data['title']" buttonTitle="Back" buttonRoute="{{ route('meetings.index') }}">
        <div class="row g-y-20">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">{{ _trans('common.Meeting Details') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>{{ _trans('common.Title') }}:</strong> {{ @$data['meeting']->title }}</p>
                                <p><strong>{{ _trans('common.Status') }}:</strong>
                                    <x-common.badge :data="[
                                        'scheduled' => 'info',
                                        'in_progress' => 'warning',
                                        'completed' => 'success',
                                        'cancelled' => 'destructive',
                                    ]" :status="@$data['meeting']->status" />
                                </p>
                                <p><strong>{{ _trans('common.Start Date & Time') }}:</strong>
                                    {{ Carbon::parse($data['meeting']->start_at)->format('Y-m-d h:i:A') }}</p>
                                @if ($data['meeting']->end_at)
                                    <p><strong>{{ _trans('common.End Date & Time') }}:</strong>
                                        {{ Carbon::parse($data['meeting']->end_at)->format('Y-m-d h:i:A') }}</p>
                                @endif
                            </div>
                            <div class="col-md-6">
                                <p><strong>{{ _trans('common.Duration') }}:</strong> {{ @$data['meeting']->duration }}
                                    {{ _trans('common.minutes') }}</p>
                                <p><strong>{{ _trans('common.Location') }}:</strong>
                                    {{ @$data['meeting']->location ?? _trans('common.Not specified') }}</p>
                                <p><strong>{{ _trans('common.Created At') }}:</strong>
                                    {{ Carbon::parse($data['meeting']->created_at)->format('Y-m-d h:i:A') }}</p>
                            </div>
                        </div>

                        @if ($data['meeting']->description)
                            <div class="mt-3">
                                <h6>{{ _trans('common.Description') }}</h6>
                                <p>{{ $data['meeting']->description }}</p>
                            </div>
                        @endif

                        @if ($data['meeting']->agenda)
                            <div class="mt-3">
                                <h6>{{ _trans('common.Agenda') }}</h6>
                                <p>{{ $data['meeting']->agenda }}</p>
                            </div>
                        @endif

                        @if ($data['meeting']->notes)
                            <div class="mt-3">
                                <h6>{{ _trans('common.Notes') }}</h6>
                                <p>{{ $data['meeting']->notes }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">{{ _trans('common.Actions') }}</h5>
                    </div>
                    <div class="card-body">
                        @if (hasPermission('meeting_update'))
                            <a href="{{ route('meetings.edit', ['id' => $data['meeting']->id]) }}"
                                class="btn btn-primary btn-sm w-100 mb-2">
                                <x-common.icons name="edit" size="16" />
                                {{ _trans('common.Edit Meeting') }}
                            </a>
                        @endif

                        @if (hasPermission('meeting_delete'))
                            <x-table.action.delete url="{{ route('meetings.destroy', $data['meeting']->id) }}"
                                class="w-100">
                            </x-table.action.delete>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </x-container>
@endsection
