@extends('backend.layouts.app')
@section('title', @$data['title'])
@section('content')

    <div class="ot-card">
            <h4 class="card-title"> {{ _trans('common.Request for') }} {{ @$data['document']->userDocumentType->name }}
            </h4>
        <div class="ot-card">
            <div>
                <p class="lead">{!! @$data['document']->request_description !!} </p>
            </div>

            <hr>
            <div class="row">
                <div class="col-md-6">
                    <h5>{{ _trans('common.Applicant') }}</h5>
                    <p>
                    <h4>{{ @$data['document']->requestBy->name }}</h4>
                    {{ _trans('common.Email') }} : {{ @$data['document']->requestBy->email }} <br>
                    {{ _trans('common.Designation') }} : {{ @$data['document']->requestBy->designation->title }} <br>
                    {{ _trans('common.Department') }} : {{ @$data['document']->requestBy->department->title }} <br>
                    </p>
                </div>
                <div class="col-md-3">
                    <h5>{{ _trans('common.Request Date') }}</h5>
                    <p>{{ @$data['document']->request_date }}</p>
                </div>
                <div class="col-md-3">
                    <h5>{{ _trans('common.Request File') }}</h5>
                    @if ($data['document']->request_file && file_exists(@$data['document']->request_file))
                        <a href="{{ @$data['document']->request_file }}" target="_blank" class="text-primary">
                            <x-common.icons name="eye" class="text-subtitle" size="20" stroke-width="1.5" />
                            {{ _trans('common.View') }}
                        </a>
                    @else
                        <p>{{ _trans('common.No File') }}</p>
                    @endif
                </div>
            </div>


            <form action="{{ route('document-request.approve.store', @$data['document']->id) }}" method="POST"
                enctype="multipart/form-data">
                @csrf
                <div class="row mt-3 mb-3">
                    <div class="col-lg-12 mt-5 mb-3">
                        <h5 class="mb-3">{{ _trans('common.Manage Reply Letter') }}</h5>
                        <div class="row mb-3">
                            <div class="col-lg-12">
                                <div class="form-group mb-3">
                                    <textarea name="response_description" class="form-control" rows="10" cols="50">
                                    {!! @$data['document']->userDocumentType->response_template !!}
                                    </textarea>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-lg-4">
                                <div class="form-group mb-3">
                                    <label for="authorized_person"
                                        class="form-label mb-3">{{ _trans('common.Authorized Person') }}</label>
                                    <select name="authorized_person" id="authorized_person" class="form-control">
                                        <option value="">{{ _trans('common.Select Authorized Person') }}</option>
                                        @foreach ($data['authorized_persons'] ?? [] as $authorized_person)
                                            <option value="{{ $authorized_person->id }}"
                                                {{ $data['document']->authorized_person == $authorized_person->id ? 'selected' : '' }}>
                                                {{ $authorized_person->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <div class="col-lg-4">
                                <div class="form-group">
                                    <label for="official_seal"
                                        class="form-label mb-3">{{ _trans('common.Official Seal') }}</label>
                                    <input type="file" name="official_seal" id="official_seal" class="form-control"
                                        accept="image/*">
                                </div>
                            </div>
                            @if (!$data['document']->rejected_by)
                                <div class="col-lg-4">
                                    <div class="form-group">
                                        <label for="signature"
                                            class="form-label mb-3">{{ _trans('common.Signature') }}</label>
                                        <input type="file" name="digital_signature" id="signature" class="form-control"
                                            accept="image/*" required>
                                    </div>
                                </div>
                            @endif

                            <div class="col-lg-4">
                                <div class="form-group">
                                    <label for="statusSelect" class="form-label">{{ _trans('common.Status') }}</label>
                                    <select name="status" id="statusSelect" class="form-control mt-1">
                                        <option value="">{{ _trans('common.Select Status') }}</option>
                                        <option value="approved_by_admin"
                                            {{ $data['document']->status == 'approved_by_admin' ? 'selected' : '' }}>
                                            {{ _trans('common.Approved') }}</option>
                                        <option value="rejected_by_admin"
                                            {{ $data['document']->status == 'rejected_by_admin' ? 'selected' : '' }}>
                                            {{ _trans('common.Rejected') }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <input type="hidden" name="document_request_id" value="{{ @$data['document']->id }}">
                        <input type="hidden" name="user_id" value="{{ @$data['document']->user_id }}">
                        <input type="hidden" name="document_type_id" value="{{ @$data['document']->document_type_id }}">
                    </div>
                </div>
                @if (!in_array(@$data['document']->status, ['approved_by_admin', 'rejected_by_admin']))
                    <div class="row mt-3">
                        <div class="col-lg-8">
                            <button type="submit" class="btn-primary-fill mr-3 text-right">
                                {{ _trans('common.Submit') }}
                            </button>
                        </div>
                    </div>
                @endif
            </form>
        </div>
    </div>
@endsection
