@extends('backend.layouts.app')
@section('title', @$data['title'])
@section('content')
    <div class="row">
        <div class="col-lg-9">
            <x-container :title="$data['title']">
                <x-table :bulkAction="true" buttonTitle="Add New" buttonType="modal" modalId="document_type_create">
                    <x-slot name="filters">
                        <div class="form-group h-42 w-lg-100">
                            <select class="form-select ot-input select2" id="statusFilter" name="status">
                                <option value="">{{ _trans('common.Select Status') }}</option>
                                <option value="active" {{ @$data['input']['status'] == 'active' ? 'selected' : '' }}>
                                    {{ _trans('common.Active') }}</option>
                                <option value="inactive" {{ @$data['input']['status'] == 'inactive' ? 'selected' : '' }}>
                                    {{ _trans('common.Inactive') }}</option>
                            </select>
                        </div>
                    </x-slot>

                    <!--  table start -->
                    <table class="table table-bordered" id="table">
                        <thead class="thead">
                            <tr>
                                <th class="sorting-asc w-60 no-export">
                                    <div class="check-box">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="all_check" />
                                        </div>
                                    </div>
                                </th>
                                <th class="w-90">{{ _trans('common.SL') }}</th>
                                <th>{{ _trans('common.Document Type') }}</th>
                                <th class="w-90">{{ _trans('common.Status') }}</th>
                                <th class="w-90 no-export">{{ _trans('common.Action') }}</th>
                            </tr>
                        </thead>
                        <tbody class="tbody">
                            @forelse (@$data['collections'] ?? [] as $key => $row)
                                <tr>
                                    <td class="no-export">
                                        <div class="check-box">
                                            <div class="form-check">
                                                <input class="form-check-input column_id" id="column_{{ $row->id }}"
                                                    onclick="columnID({{ $row->id }})" type="checkbox"
                                                    name="column_id[]" value="{{ $row->id }}">
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ $key + 1 }}</td>
                                    <td>{{ $row->name }}</td>
                                    <td>
                                        <x-common.tyne-badge :status="$row->status" />
                                    </td>
                                    <td class="text-center no-export">
                                        <x-table.action>
                                            @if (hasPermission('official_document_type_edit'))
                                                <a href="{{ route('documents.types.index', ['id' => $row->id]) }}"
                                                    class="dropdown-item">
                                                    <x-common.icons name="edit" class="text-title" size="16"
                                                        stroke-width="1.5" />
                                                    {{ _trans('common.Edit') }}
                                                </a>
                                                @if (hasPermission('official_document_type_delete'))
                                                    <x-table.action.delete
                                                        url="{{ route('documents.types.destroy', $row->id) }}">
                                                    </x-table.action.delete>
                                                @endif
                                            @endif
                                        </x-table.action>
                                    </td>
                                </tr>
                            @empty
                                <x-table.empty :colspan="5" />
                            @endforelse
                        </tbody>
                    </table>
                    <x-table.pagination :data="@$collection"></x-table.pagination>
                </x-table>
            </x-container>
        </div>
        <x-inner-form-layout :title="@$data['formTitle']" modalId="document_type_create">
            @include('employeedocuments::user_document_types.form', [
                'type' => 'post',
                'route' => @$data['edit']
                    ? route('documents.types.update', @$data['edit']->id)
                    : route('documents.types.store'),
                'data' => @$data['edit'],
            ])
        </x-inner-form-layout>
    </div>
@endsection
