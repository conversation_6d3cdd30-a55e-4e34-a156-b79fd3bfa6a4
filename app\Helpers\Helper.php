<?php

use App\Models\Company;
use App\Models\Setting\AddonSetting;
use App\Models\Setting\BrandSetting;
use App\Models\Setting\IntegrationSetting;
use App\Models\Setting\Setting;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Modules\Notification\Services\MailService;
use <PERSON>bauman\Location\Facades\Location;
use Symfony\Component\HttpFoundation\Response;

if (! function_exists('menuActiveByRoute')) {
    function menuActiveByRoute($routes, $activeClass = 'active')
    {
        $routes = (array) $routes;

        foreach ($routes as $route) {
            if (request()->routeIs($route)) {
                return $activeClass;
            }
        }

        return '';
    }
}

if (! function_exists('globalSetting')) {
    function globalSetting($key, $form = 'addon')
    {
        try {
            switch ($form) {
                case 'base':
                    $cacheKey = 'base_settings';
                    $settings = Cache::rememberForever($cacheKey, function () {
                        return Setting::pluck('value', 'key')->toArray();
                    });
                    break;

                case 'brand':
                    $cacheKey = 'brand_settings';
                    $settings = Cache::rememberForever($cacheKey, function () {
                        return BrandSetting::pluck('value', 'key')->toArray();
                    });
                    break;

                case 'addon':
                    $cacheKey = 'addon_settings';
                    $settings = Cache::remember($cacheKey, 3600, function () {
                        return AddonSetting::pluck('value', 'key')->toArray();
                    });
                    break;

                case 'integration':
                    $cacheKey = 'integration_settings';
                    $settings = Cache::remember($cacheKey, 3600, function () {
                        return IntegrationSetting::pluck('value', 'key')->toArray();
                    });
                    break;
            }

            $value = $settings[$key] ?? '';

            // Try to decode JSON
            $decoded = json_decode($value, true);

            if (
                is_array($decoded)
                && isset($decoded['disk'], $decoded['files'])
                && is_array($decoded['files'])
            ) {
                $fileUrls = [];

                foreach ($decoded['files'] as $filePath) {
                    $fileUrls[] = Storage::disk($decoded['disk'])->url($filePath);
                }

                return count($fileUrls) === 1 ? $fileUrls[0] : $fileUrls; // Return string or array
            }

            return $value;
        } catch (Exception $e) {
            return null;
        }
    }
}

if (! function_exists('getFilePath')) {
    function getFilePath($json)
    {
        $decoded = json_decode($json, true);

        if (is_array($decoded)) {
            if (is_array(@$decoded['files'])) {
                $fileUrls = [];

                foreach ($decoded['files'] as $filePath) {
                    $fileUrls[] = Storage::disk($decoded['disk'])->url($filePath);
                }

                return $fileUrls;
            } else {
                return Storage::disk($decoded['disk'])->url($decoded['file']);
            }
        }
    }
}

if (! function_exists('isModuleActive')) {
    function isModuleActive($moduleName)
    {
        $modules = json_decode(file_get_contents(base_path('modules_statuses.json')), true) ?? [];

        if (empty($modules[$moduleName])) {
            return false;
        }

        $module_json = base_path("Modules/{$moduleName}/module.json");

        return file_exists($module_json);
    }
}

if (! function_exists('hasPermission')) {
    function hasPermission(string $permission, $user = null): bool
    {
        $permissions = getUserPermission($user);

        return in_array($permission, $permissions);
    }
}

if (! function_exists('hasAnyPermission')) {
    function hasAnyPermission(array $permissions, $user = null): bool
    {
        $userPermissions = getUserPermission($user);

        return ! empty(array_intersect($permissions, $userPermissions));
    }
}

if (! function_exists('hasAllPermissions')) {
    function hasAllPermissions(array $permissions, $user = null): bool
    {
        $userPermissions = getUserPermission($user);

        return empty(array_diff($permissions, $userPermissions));
    }
}

if (! function_exists('getUserPermission')) {
    function getUserPermission($user = null): array
    {
        $user = $user ?? Auth::user();

        if (! $user) {
            return [];
        }

        static $cachedPermissions = [];

        if (! isset($cachedPermissions[$user->id])) {
            $permissions = Cache::flexible("user_permissions_{$user->id}", [3600, 7200], function () use ($user) {
                return optional($user->permissions)->permissions ?? [];
            });

            $cachedPermissions[$user->id] = is_array($permissions) ? $permissions : json_decode($permissions ?? '[]', true);
        }

        return $cachedPermissions[$user->id];
    }
}

if (! function_exists('showAmount')) {
    function showAmount($amount)
    {
        return globalSetting('currency_symbol', 'base').' '.$amount;
    }
}

if (! function_exists('formatDateTime')) {
    function formatDateTime($dateTime, $format = 'F j, Y')
    {
        if ($format === 'humanDiff') {
            return $dateTime ? Carbon::parse($dateTime)->diffForHumans() : null;
        } elseif ($dateTime) {
            return Carbon::parse($dateTime)->format($format);
        }

        return '';
    }
}

if (! function_exists('timeInHourMinutes')) {
    function timeInHourMinutes($time)
    {
        if ($time instanceof \Illuminate\Support\Carbon) {
            $time = $time->format('H:i:s');
        }

        if (! $time || $time === '00:00:00') {
            return '0h 0m';
        }

        [$hours, $minutes, $seconds] = explode(':', $time);

        return (int) $hours.'h '.(int) $minutes.'m';
    }
}

if (! function_exists('calculatePercentage')) {
    function calculatePercentage($part, $total)
    {
        if ($total == 0) {
            return 0;
        }

        return round(($part / $total) * 100);
    }
}

if (! function_exists('attendanceStatus')) {
    function attendanceStatus($status = null): string
    {
        return Auth::user()->attendanceStatus($status);
    }
}

if (! function_exists('getUserAddress')) {
    function getUserAddress($latitude = null, $longitude = null)
    {
        $apiKey = @globalSetting('google_map_key', 'integration');

        if (! $apiKey || ! $latitude || ! $longitude) {
            $location = Location::get(getUserIp());

            return $location ? $location->cityName : null;
        }

        dd('sdfdsf');

        try {
            $response = Http::get('https://maps.googleapis.com/maps/api/geocode/json', [
                'latlng' => "{$latitude},{$longitude}",
                'key' => $apiKey,
            ])->json();

            if (! empty($response['results'])) {
                // Find the longest formatted address
                $largestFormattedAddress = collect($response['results'])
                    ->pluck('formatted_address')
                    ->filter()
                    ->sortByDesc(fn ($address) => strlen($address))
                    ->first();

                return $largestFormattedAddress ?? '';
            }
        } catch (Exception $exception) {
            info('Geocode API error: '.$exception->getMessage());
        }
    }
}

if (! function_exists('getUserIp')) {
    function getUserIp()
    {
        $ipAddress = null;

        if (isset($_SERVER['HTTP_CLIENT_IP'])) {
            $ipAddress = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ipAddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } elseif (isset($_SERVER['HTTP_X_FORWARDED'])) {
            $ipAddress = $_SERVER['HTTP_X_FORWARDED'];
        } elseif (isset($_SERVER['HTTP_FORWARDED_FOR'])) {
            $ipAddress = $_SERVER['HTTP_FORWARDED_FOR'];
        } elseif (isset($_SERVER['HTTP_FORWARDED'])) {
            $ipAddress = $_SERVER['HTTP_FORWARDED'];
        } elseif (isset($_SERVER['REMOTE_ADDR'])) {
            $ipAddress = $_SERVER['REMOTE_ADDR'];
        }

        if ($ipAddress == '127.0.0.1') {
            $ipAddress = Http::get('https://api.ipify.org/?format=json')->json()['ip'];
        }

        return $ipAddress;
    }
}

if (! function_exists('getCompanyId')) {
    function getCompanyId()
    {
        if (Session::has('session_company_id')) {
            return Session::get('session_company_id');
        }

        $companyId = Company::where(function ($q) {
            $segments = $segments = explode('.', request()->getHost());

            $q->where('subdomain', request()->getHost())
                ->orWhere('subdomain', array_shift($segments));
        })->first()?->id ?? Company::first()?->id;

        Session::put('session_company_id', $companyId);

        return $companyId;
    }
}

if (! function_exists('getBranchId')) {
    function getBranchId()
    {
        $branchId = (int) Session::get('session_branch_id');

        if ($branchId) {
            return $branchId;
        }

        $branchId = Auth::user()->branch_id ?? 1;

        return $branchId;
    }
}

if (! function_exists('formatTitleCase')) {
    function formatTitleCase($keyWord)
    {
        return ucwords(str_replace('_', ' ', strtolower($keyWord)));
    }
}

if (! function_exists('throwException')) {
    function throwException($message, $code = 400)
    {
        throw new Exception($message, $code);
    }
}

if (! function_exists('catchHandler')) {
    function catchHandler($exception)
    {
        $isJson = request()->expectsJson();
        $isDebug = config('app.debug');

        $statusCode = (int) $exception->getCode();
        if ($statusCode < 100 || $statusCode > 599) {
            $statusCode = 500;
        }

        if ($isJson) {
            return response()->json([
                'result' => false,
                'message' => $isDebug ? $exception->getMessage() : _trans('alert.Something went wrong'),
                'error' => $isDebug ? $exception->getMessage() : null,
            ], $statusCode);
        }

        if ($isDebug) {
            return redirect()->back()->with('error', $exception->getMessage());
        }

        return redirect()->back()->with('error', _trans('alert.Something went wrong'));
    }
}

if (! function_exists('notification')) {
    function notification()
    {
        return app('notification');
    }
}

if (! function_exists('sendMail')) {
    function sendMail()
    {
        return app(MailService::class);
    }
}

if (! function_exists('successResponse')) {
    function successResponse(string $message, $data = null, int $statusCode = Response::HTTP_OK)
    {
        $response = [
            'success' => true,
            'message' => $message,
        ];

        if ($data) {
            $response['data'] = $data;
        }

        return response()->json($response, $statusCode);
    }
}

if (! function_exists('errorResponse')) {
    function errorResponse(string $message, $errors = null, int $statusCode = Response::HTTP_BAD_REQUEST)
    {
        $response = [
            'success' => false,
            'message' => $message,
        ];

        if ($errors) {
            $response['errors'] = $errors;
        }

        return response()->json($response, $statusCode);
    }
}

if (! function_exists('_trans')) {
    function _trans($value)
    {
        try {
            $local = app()->getLocale();

            $langPath = resource_path('lang/'.$local.'/');
            if (! file_exists($langPath)) {
                mkdir($langPath, 0777, true);
            }

            // Determine the file name and translation key
            if (str_contains($value, '.')) {
                [$file_name, $trans_key] = explode('.', $value);
                $file_path = $langPath.$file_name.'.json';
            } else {
                $trans_key = $value;
                $file_path = resource_path('lang/'.$local.'/'.$local.'.json');
            }

            // Ensure the file exists and initialize if necessary
            if (! file_exists($file_path)) {
                file_put_contents($file_path, json_encode([]));
            }

            // Read the existing translations
            $file_data = json_decode(file_get_contents($file_path), true) ?? [];

            // Add the translation key if it doesn't exist
            if (! array_key_exists($trans_key, $file_data)) {
                $file_data[$trans_key] = $trans_key; // Default value can be the key itself
                file_put_contents($file_path, json_encode($file_data, JSON_PRETTY_PRINT));
            }

            return $file_data[$trans_key];
        } catch (Exception $exception) {
            return $value;
        }
    }
}

if (! function_exists('formatToHoursMinutes')) {
    function formatToHoursMinutes(string $time): string
    {
        [$h, $m, $s] = array_pad(explode(':', $time), 3, 0);

        return (int) $h.'h '.(int) $m.'m';
    }
}

if (! function_exists('isRTL')) {
    function isRTL()
    {
        $rtlLanguages = ['ar', 'he', 'fa', 'ur', 'ps', 'ckb', 'sd', 'ug', 'dv', 'yi'];

        return in_array(app()->getLocale(), $rtlLanguages);
    }
}

if (! function_exists('isEnableSelfieBaseAttendance')) {
    function isEnableSelfieBaseAttendance()
    {
        $methods = auth()->user()->attendanceConfig->attendance_method;

        return isModuleActive('SelfieBasedAttendance') && in_array('selfie_based_attendance', $methods);
    }
}
