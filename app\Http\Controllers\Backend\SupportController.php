<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\ExpenseClaimRequest;
use App\Http\Requests\SupportTicketRequest;
use App\Models\Hrm\Support\SupportTicket;
use App\Repositories\ExpenseClaimRepository;
use App\Repositories\SupportTicketRepository;
use Exception;
use Illuminate\Http\Request;

class SupportController extends Controller
{
    public function __construct(
        protected SupportTicketRepository $repository,
        protected ExpenseClaimRepository $expenseClaimRepository
    ) {}

    public function index(Request $request)
    {
        $data['title'] = _trans('support.Support ticket');
        $data['collection'] = $this->repository->getTicketPaginateData($request);
        $data['ticketType'] = $this->repository->ticketType;
        $data['ticketPriority'] = $this->repository->ticketPriority;

        return view('backend.support.index')->with($data);
    }

    public function self(Request $request)
    {
        $data['title'] = _trans('support.Support ticket');
        $data['formTitle'] = _trans('support.Create Support ticket');
        $data['collection'] = $this->repository->getPaginateData($request);
        $data['ticketType'] = $this->repository->ticketType;
        $data['ticketPriority'] = $this->repository->ticketPriority;

        if ($request->filled('id')) {
            $data['formTitle'] = _trans('support.Edit Support ticket');
            $data['supportTicket'] = $this->repository->show($request->id);
        }

        return view('backend.support.self')->with($data);
    }

    public function ticketConversation($ticketId)
    {
        $data['title'] = _trans('support.Ticket Conversation');
        $data['formTitle'] = _trans('support.Create Ticket Conversation');
        $data['model'] = $this->repository->getByCode($ticketId);
        $data['ticketType'] = $this->repository->ticketType;
        $data['ticketPriority'] = $this->repository->ticketPriority;
        $data['ticketStatus'] = $this->repository->ticketStatus;
        $data['employees'] = $this->repository->getEmployees();

        return view('backend.support.conversation')->with($data);
    }

    public function store(SupportTicketRequest $request)
    {
        try {
            $this->repository->store($request->validated());

            return redirect()->back()->with('success', _trans('alert.Support ticket created successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function update(Request $request, $id)
    {
        try {
            $this->repository->update($request, $id);

            return redirect()->back()->with('success', _trans('alert.Support ticket updated successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function ticketReplyStore(Request $request, SupportTicket $ticket)
    {
        try {
            $ticket = $this->repository->ticketReply($request, $ticket);

            return redirect()->back()->with('success', _trans('alert.Support ticket replied successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function expense(Request $request)
    {
        $data['title'] = _trans('support.Expense Claim');
        $data['formTitle'] = _trans('support.Create Expense Claim');
        $data['collection'] = $this->expenseClaimRepository->getPaginateData($request);

        if ($request->filled('id')) {
            $data['formTitle'] = _trans('support.Edit Expense Claim');
            $data['expenseClaim'] = $this->expenseClaimRepository->show($request->id);
        }

        return view('backend.expense_claim.index')->with($data);
    }

    public function storeExpense(ExpenseClaimRequest $request)
    {
        try {
            $this->expenseClaimRepository->store($request->validated());

            return redirect()->back()->with('success', _trans('alert.Expense claim created successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function updateExpense(Request $request, $id)
    {
        try {
            $this->expenseClaimRepository->update($request, $id);

            return redirect()->route('supportTicket.expense')->with('success', _trans('alert.Expense claim updated successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function deleteExpense($id)
    {
        try {
            $this->expenseClaimRepository->destroy($id);

            return redirect()->back()->with('success', _trans('alert.Expense claim deleted successfully'));
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }
}
