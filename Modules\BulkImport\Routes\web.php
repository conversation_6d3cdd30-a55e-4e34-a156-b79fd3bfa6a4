<?php

use Illuminate\Support\Facades\Route;
use Modules\BulkImport\Http\Controllers\BulkImportController;
use Modules\BulkImport\Http\Controllers\ImportHistoryController;

// Regular bulk import routes with authentication
Route::group([
    'prefix' => config('bulkimport.route_prefix', 'bulk-import'),
    'middleware' => config('bulkimport.middleware', ['web', 'auth']),
    'as' => 'bulk-import.',
], function () {
    // Main routes
    Route::get('/', [BulkImportController::class, 'index'])->name('index');
    Route::post('upload', [BulkImportController::class, 'upload'])->name('upload');
    Route::post('import', [BulkImportController::class, 'import'])->name('import');

    // Import History routes
    Route::get('/history', [ImportHistoryController::class, 'index'])->name('history');
    Route::get('/history/{id}', [ImportHistoryController::class, 'show'])->name('history.show');
    Route::post('/history/{id}/cancel', [ImportHistoryController::class, 'cancel'])->name('history.cancel');
    Route::post('/history/{id}/retry', [ImportHistoryController::class, 'retry'])->name('history.retry');
    Route::delete('/history/{id}', [ImportHistoryController::class, 'destroy'])->name('history.destroy');

    // Redirect GET requests to upload back to index
    Route::get('upload', function () {
        return redirect()->route('bulk-import.index')->with('error', 'Please upload a file using the form.');
    });

    // Generate employee template
    Route::get('/generate-employees', [BulkImportController::class, 'generateEmployeeTemplate'])->name('generate-employees');

    // Generate attendance template
    Route::get('/generate-attendance', [BulkImportController::class, 'generateAttendanceTemplate'])->name('generate-attendance');

    // Generate leave template
    Route::get('/generate-leave', [BulkImportController::class, 'generateLeaveTemplate'])->name('generate-leave');
});
