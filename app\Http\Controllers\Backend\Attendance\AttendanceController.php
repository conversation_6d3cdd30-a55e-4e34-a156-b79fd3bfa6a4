<?php

namespace App\Http\Controllers\Backend\Attendance;

use App\Http\Controllers\Controller;
use App\Models\Attendance\Attendance;
use App\Models\User;
use App\Repositories\AttendanceRepository;
use App\Repositories\DepartmentRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AttendanceController extends Controller
{
    protected $attendanceRepo;

    protected $departmentRepository;

    public function __construct(
        AttendanceRepository $attendanceRepo,
        DepartmentRepository $departmentRepository,
    ) {
        $this->attendanceRepo = $attendanceRepo;
        $this->departmentRepository = $departmentRepository;
    }

    public function index(Request $request)
    {
        $data['checkbox'] = true;
        $data['title'] = _trans('attendance.Attendance list');
        $data['collections'] = $this->attendanceRepo->getPaginateData($request);
        $data['departments'] = $this->departmentRepository->getActiveAll();

        return view('backend.attendance.attendance.index', compact('data'));
    }

    public function checkinModal()
    {
        try {
            $data['title'] = _trans('common.Check In');
            $data['todayAttendance'] = Attendance::whereDate('date', now()->toDateString())
                ->where('user_id', Auth::id())
                ->first();

            return view('backend.attendance.attendance.check_in_modal', $data);
        } catch (Exception $exception) {
            return catchHandler($exception);
        }
    }

    public function checkin(Request $request)
    {
        try {
            $this->attendanceRepo->checkIn($request);

            return successResponse('Check in successfully');
        } catch (Exception $exception) {
            return catchHandler($exception);
        }
    }

    public function checkOutModal()
    {
        try {
            $data['title'] = _trans('common.Check Out');

            $data['todayAttendance'] = Attendance::whereDate('date', now()->toDateString())
                ->where('user_id', Auth::id())
                ->first();

            if (! $data['todayAttendance']) {
                throwException('Attendance not found', 404);
            }

            return view('backend.attendance.attendance.check_in_modal', $data);
        } catch (Exception $e) {
            return catchHandler($e);
        }
    }

    public function checkOut(Request $request)
    {
        try {
            $this->attendanceRepo->checkOut($request);

            return successResponse('Check Out Successfully');
        } catch (Exception $exception) {
            return catchHandler($exception);
        }
    }

    public function breakStart(Request $request)
    {
        try {
            $this->attendanceRepo->breakStart($request);

            session()->put('break_start', now());

            return successResponse('Break Started');
        } catch (Exception $exception) {
            return catchHandler($exception);
        }
    }

    public function breakEnd(Request $request)
    {
        try {
            $this->attendanceRepo->breakEnd($request);

            session()->forget('break_start');

            return successResponse('Break End Successfully');
        } catch (Exception $exception) {
            return catchHandler($exception);
        }
    }

    public function manualCreate()
    {
        $data['title'] = _trans('common.Manual Attendance');
        $data['employees'] = User::isActive()->select('id', 'name', 'employee_id', 'department_id')
            ->where('company_id', getCompanyId())
            ->where('branch_id', getBranchId())
            ->with(['department:id,title'])
            ->get();

        return view('backend.attendance.attendance.manual_create', compact('data'));
    }

    public function manualCheckIn(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'date' => 'required|date',
            'check_in_time' => 'required|date_format:H:i',
        ]);

        try {
            $request->merge(['time' => $request->check_in_time]);

            $this->attendanceRepo->checkIn($request);

            return redirect()->back()->withSuccess('Check in successfully');
        } catch (Exception $exception) {
            return catchHandler($exception);
        }
    }

    public function manualCheckOut(Request $request)
    {
        try {
            $request->validate([
                'user_id' => 'required',
                'date' => 'required|date',
                'check_out_time' => 'required|date_format:H:i',
            ]);

            $request->merge(['time' => $request->check_out_time]);

            $this->attendanceRepo->checkOut($request);

            return redirect()->back()->withSuccess('Check out successfully');
        } catch (Exception $exception) {
            return catchHandler($exception);
        }
    }

    public function manualBreak(Request $request)
    {
        try {
            $request->validate([
                'user_id' => 'required|exists:users,id',
                'date' => 'required|date',
                'break_in_time' => 'required|date_format:H:i',
                'break_out_time' => 'required|date_format:H:i|after:break_in_time',
            ]);

            // Normalize request times
            $breakIn = Carbon::createFromFormat('H:i', $request->break_in_time)->format('H:i:s');
            $breakOut = Carbon::createFromFormat('H:i', $request->break_out_time)->format('H:i:s');

            // Get attendance record for this user/date
            $attendance = Attendance::where('user_id', $request->user_id)
                ->whereDate('date', $request->date)
                ->first();

            if ($attendance && $attendance->attendance_log) {
                $logs = $attendance->attendance_log;

                if (is_string($logs)) {
                    $logs = json_decode($logs, true);
                }

                $logs = $logs ?? [];

                foreach ($logs as $log) {
                    $logStart = Carbon::createFromFormat('H:i:s', $log['start_time']);
                    $logEnd = Carbon::createFromFormat('H:i:s', $log['end_time']);

                    // swap if bad data (start > end)
                    if ($logStart->gt($logEnd)) {
                        [$logStart, $logEnd] = [$logEnd, $logStart];
                    }

                    $newStart = Carbon::createFromFormat('H:i:s', $breakIn);
                    $newEnd = Carbon::createFromFormat('H:i:s', $breakOut);

                    if ($newStart->lt($logEnd) && $newEnd->gt($logStart)) {
                        $breakStart = formatDateTime($logStart, 'h:i A');
                        $breakEnd = formatDateTime($logEnd, 'h:i A');

                        return redirect()->back()
                            ->withError("Break already exists from $breakStart to $breakEnd.")
                            ->withInput();
                    }
                }
            }

            // Add break start
            $request->merge(['time' => $request->break_in_time]);
            $this->attendanceRepo->breakStart($request);

            // Add break end
            $request->merge(['time' => $request->break_out_time]);
            $this->attendanceRepo->breakEnd($request);

            return redirect()->back()->withSuccess('Break added successfully');
        } catch (Exception $exception) {
            return catchHandler($exception);
        }
    }
}
