<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class ExpenseClaim extends Model
{
    protected $guarded = ['id'];

    public function getAttachmentsAttribute($value)
    {
        if (is_string($value)) {
            $value = json_decode($value, true);
        }

        if (is_array($value) && isset($value['disk'], $value['file'])) {
            return Storage::disk($value['disk'])->url($value['file']);
        }

        return null;
    }
}
