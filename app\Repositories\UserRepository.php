<?php

namespace App\Repositories;

use App\Models\Hrm\Attendance\Holiday;
use App\Models\Role;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Modules\AreaBasedAttendance\Entities\LocationBind;
use Modules\IpBasedAttendance\Entities\UserIpBind;
use Modules\Notify\Services\NotificationService;
use Modules\SpecialAttendance\Entities\DutyCalendar;
use Throwable;

class UserRepository
{
    public function __construct(protected User $model) {}

    public function getAll()
    {
        return $this->model->get();
    }

    public function getActiveAll()
    {
        return $this->model
            ->select('id', 'name', 'email', 'phone', 'employee_id', 'status_id')
            ->isActive()
            ->get();
    }

    public function getById($id)
    {
        return $this->model->find($id);
    }

    public function getUserByKeywords($request)
    {
        $where = [];
        if ($request->has('department_id')) {
            $where = ['department_id' => $request->get('department_id')];
        }
        if ($request->has('params')) {
            $where = array_merge($where, $request->get('params'));
        }

        return $this->model
            ->isActive()
            ->where($where)
            ->where('name', 'LIKE', "%$request->term%")
            ->select('id', 'name', 'phone', 'employee_id')
            ->take(10)
            ->get();
    }

    public function getByIdWithDetails($id)
    {
        return $this->model->with('department', 'designation', 'role', 'shift')->where('id', $id)->first();
    }

    public function permission_update($request, $id)
    {
        DB::beginTransaction();
        try {
            $user = $this->model->find($id);
            $user->permissions = $request->permissions;
            $user->save();
            $this->updatedBy($user);
            DB::commit();

            return successResponse(_trans('response.User Permission update successfully.'), $user);
        } catch (Throwable $th) {
            return errorResponse($th->getMessage(), [], 400);
        }
    }

    public function save($request)
    {
        DB::beginTransaction();
        try {
            $user = $this->updateOrCreateEmployee($request);
            $user = $this->updateOrCreateEmployeeHolidayWeekend($request, $user);

            DB::commit();

            return successResponse(_trans('message.Employee successfully Created.'), $user);
        } catch (Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    public function updateOrCreateEmployee($request, $id = null)
    {
        $user = $id ? $this->model->find($id) : $this->model;
        $user->name = $request->name;
        $user->email = $request->email;
        $user->phone = $request->phone;

        if ($request->avatar) {
        }

        $user->gender = $request->gender;
        $user->country_id = $request->country_id;
        $user->religion = $request->religion;
        $user->marital_status = $request->marital_status;
        $user->blood_group = $request->blood_group;
        $user->birth_date = $request->birth_date ? Carbon::parse($request->birth_date)->format('Y-m-d') : null;
        $user->employee_id = $request->employee_id;
        $user->department_id = $request->department_id;
        $user->designation_id = $request->designation_id;
        $user->duty_schedule_id = $request->duty_schedule_id;
        $user->joining_date = $request->joining_date ? Carbon::parse($request->joining_date)->format('Y-m-d') : null;
        $user->manager_id = $request->manager_id;
        $user->role_id = $request->role_id;
        $user->permissions = Role::where('id', $request->role_id)->first()?->permissions;
        $user->attendance_method = $request->attendance_method ? array_fill_keys($request->attendance_method, 1) : null;
        $user->is_free_location = $request->is_free_location ?? 1;
        $user->is_free_ip = $request->is_free_ip ?? 1;
        $user->time_zone = $request->time_zone;
        $user->address = $request->address;
        $user->speak_language = $request->speak_language;

        // This password working only for user create
        $password = $request->password ? $request->password : '12345678';

        if ($id) {
            if ($request->password) {
                $user->password = Hash::make($request->password);
            }
        } else {
            $user->password = Hash::make($password);
        }

        $user->save();

        // User ip bind
        if ($request->ip_addresses) {
            $ipAddresses = array_map(function ($ip) {
                return preg_replace('/^\s+|\s+$/u', '', $ip);
            }, explode(',', $request->ip_addresses));

            UserIpBind::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'company_id' => getCompanyId(),
                    'branch_id' => getBranchId(),
                ],
                [
                    'ip_addresses' => $ipAddresses,
                ],
            );
        }

        // User location bind
        if ($request->locations) {
            foreach ($request->locations as $location) {
                LocationBind::create([
                    'user_id' => $user->id,
                    'latitude' => $location['latitude'],
                    'longitude' => $location['longitude'],
                    'address' => $location['address'],
                    'distance' => $location['distance'],
                ]);
            }
        }

        try {
            if (! $id) {
                (new NotificationService)->storeEmployeeChangeTemporaryPasswordNotification($user);
            }
        } catch (Throwable $th) {
        }

        return $user;
    }

    public function updateOrCreateEmployeeHolidayWeekend($request, $user)
    {
        $holidays = [];

        foreach ($request->holidays ?? [] as $holiday_id) {
            $holiday = Holiday::find($holiday_id);
            $dates = generateDateRange($holiday->start_date, $holiday->end_date);

            foreach ($dates ?? [] as $date) {
                DutyCalendar::updateOrCreate([
                    'employee_id' => $user->id,
                    'date' => $date,
                ], [
                    'duty_schedule_id' => null,
                    'holiday_id' => $holiday->id,
                ]);

                $holidays[$date] = $holiday->id;
            }
        }

        foreach ($request->weekends ?? [] as $weekend) {
            DutyCalendar::where('employee_id', $user->id)->where('date', '>', date('Y-m-d'))->update(['is_weekend' => 0, 'duty_schedule_id' => $user->duty_schedule_id]);
            DutyCalendar::where('employee_id', $user)->where('date', '>', date('Y-m-d'))->whereRaw('DAYNAME(date) = ?', [$weekend])->update(['is_weekend' => 1, 'duty_schedule_id' => null]);
        }

        $user->weekends = json_encode($request->weekends);
        $user->holidays = json_encode($holidays);

        $user->save();

        return $user;
    }

    public function update($request, $id)
    {
        try {
            DB::beginTransaction();

            $user = $this->updateOrCreateEmployee($request, $id);
            $user = $this->updateOrCreateEmployeeHolidayWeekend($request, $user);

            DB::commit();

            return successResponse(_trans('response.User update successfully.'), $user);
        } catch (Throwable $th) {
            throw $th;
        }
    }

    public function updateProfile($request)
    {
        DB::beginTransaction();
        try {
            $user = $this->model->find($request->id);
            $user->name = $request->name;
            $user->email = $request->email;
            $user->phone = $request->phone;
            $user->gender = $request->gender;
            $user->birth_date = $request->birth_date;

            DB::commit();
            $user->save();
            // author info update here

            return redirect()->back()->with('success', _trans('response.User update successfully.'));
        } catch (Exception $e) {
            DB::rollBack();

            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    public function delete($user)
    {
        $user->update(['status_id' => 4]);
        $user->save();
        $user->delete();

        return true;
    }

    public function deletePermanently($id)
    {
        User::where('id', $id)->forceDelete();

        return true;
    }

    public function changeStatus($user, $status)
    {
        $user->update([
            'status_id' => $status,
        ]);
        (new NotificationService)->storeEmployeeStatusActiveInactiveNotification($user);

        return true;
    }

    public function makeHR($user_id)
    {
        try {
            $user = $this->model->find($user_id);
            if ($user->is_hr == 1) {
                $user->is_hr = 0;
                $user->update();
                $message = _trans('message.HR role removed successfully');
            } else {
                $user->is_hr = 1;
                $user->update();
                $message = _trans('message.HR role updated successfully');
            }

            return successResponse($message, $user);
        } catch (Throwable $th) {
            return errorResponse($th->getMessage(), [], 400);
        }

        return true;
    }

    public function isWithinTimeLimit($givenDatetime)
    {
        $givenDatetime = strtotime($givenDatetime);
        $currentDatetime = time();

        $timeDifferenceInSeconds = abs($currentDatetime - $givenDatetime);

        return $timeDifferenceInSeconds <= 5;
    }

    public function employeeLocationHistory($request, $id = null)
    {
        $logs = DB::table('location_logs')
            ->select('latitude', 'longitude', 'address as start_location', 'id', 'created_at')
            ->where('user_id', $request->user)
            ->where('date', 'LIKE', $request->date.'%')
            ->get();

        $data = [];
        $total = $logs->count();
        foreach ($logs as $key => $value) {
            if ($total > 25 ? ($key % ceil($total / 25)) == 0 || $key == 0 || $key == $total - 1 : true) {
                // Format the created_at timestamp
                $formattedCreatedAt = date('j F Y, h:i a', strtotime($value->created_at));
                $value->created_at = $formattedCreatedAt;

                array_push($data, $value);
            }
        }

        return $data;
    }

    public function trashedTable($request)
    {
        $data = $this->model->onlyTrashed()
            ->select('id', 'company_id', 'role_id', 'department_id', 'designation_id', 'face_image', 'name', 'email', 'phone', 'status_id', 'is_free_location', 'is_hr', 'is_admin');

        $where = [];
        if ($request->search) {
            $where[] = ['name', 'like', '%'.$request->search.'%'];
        }
        if (@$request->designation) {
            $where[] = ['designation_id', $request->designation];
        }
        if (@$request->userStatus) {
            $where[] = ['status_id', $request->userStatus];
        }

        $data = $data
            ->where($where)
            ->paginate($request->limit ?? 2);

        return [
            'data' => $data->map(function ($data) {
                $action_button = '';
                $delete = _trans('common.Delete Permanently');
                $restore = _trans('common.Restore');
                if (hasPermission('user_unbanned')) {
                    $restoreUrl = route('user.restore', $data->id);
                    $action_button .= actionButton($restore, "RestoreEmployee('".$restoreUrl."')", 'approve');
                }
                if (hasPermission('user_delete')) {
                    $action_button .= actionButton($delete, `__globalDelete('{{ route("user.delete-permanently", $data->id) }}')`, 'delete');
                }

                $button = ' <div class="dropdown dropdown-action">
                                <button type="button" class="btn-dropdown" data-bs-toggle="dropdown"
                                    aria-expanded="false">
                                    <i class="las la-ellipsis-h"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                '.$action_button.'
                                </ul>
                            </div>';
                if (@$data->is_free_location == 1) {
                    $location = '<br>[<small class="text-info">'._trans('common.Free Location').'</small>]';
                }

                if (@$data->is_hr == 1) {
                    $hr = '<br>[<small class="text-success">'._trans('common.Acting as HR').'</small>]';
                }
                $user_image = '';
                $user_image .= '<img data-toggle="tooltip" data-placement="top" title="'.$data->name.'" src="'.$data->avatar_id.'" class="staff-profile-image-small" >';
                $registered_face = '<img data-toggle="tooltip" data-placement="top" title="'.$data->name.'" src="'.$data->face_image.'" class="staff-profile-image-small" >';

                return [
                    'name' => $data->name.''.@$location.''.@$hr,
                    'avatar' => $user_image,
                    'registered_face' => $registered_face,
                    'email' => $data->email,
                    'phone' => $data->phone,
                    'department' => @$data->department->title,
                    'designation' => @$data->designation->title,
                    'role' => @$data->role->name,
                    'shift' => $data->shifts ? $data->all_shifts()->pluck('name')->implode(', ') : Auth::user()->shift->name,
                    'id' => $data->id,
                    'status' => '<small class="badge badge-'.@$data->status->class.'">'.@$data->status->name.'</small>',
                    'action' => $button,
                ];
            }),
            'pagination' => [
                'total' => $data->total(),
                'count' => $data->count(),
                'per_page' => $data->perPage(),
                'current_page' => $data->currentPage(),
                'total_pages' => $data->lastPage(),
                'pagination_html' => $data->links('backend.pagination.custom')->toHtml(),
            ],
        ];
    }

    public function phoneBookTable($request)
    {
        $data = $this->model
            ->select('id', 'company_id', 'role_id', 'department_id', 'designation_id', 'name', 'email', 'phone', 'status_id', 'created_at');

        if ($request->from && $request->to) {
            $data = $data->whereBetween('created_at', [$request->from, $request->to]);
        }

        $data = $data->orderBy('name', 'asc')->paginate($request->limit ?? 2);

        return [
            'data' => $data->map(function ($data) {
                return [
                    'name' => $data->name,
                    'email' => $data->email,
                    'phone' => $data->phone,
                    'department' => $data->department->title,
                    'designation' => $data->designation->title,
                    'role' => $data->role->name,
                    'status' => '<small class="badge badge-'.@$data->status->class.'">'.@$data->status->name.'</small>',
                ];
            }),
            'pagination' => [
                'total' => $data->total(),
                'count' => $data->count(),
                'per_page' => $data->perPage(),
                'current_page' => $data->currentPage(),
                'total_pages' => $data->lastPage(),
                'pagination_html' => $data->links('backend.pagination.custom')->toHtml(),
            ],
        ];
    }

    public function statusUpdate($request)
    {
        try {
            if (@$request->action == 'active') {
                $userI = $this->model->whereIn('id', $request->ids)->update(['status_id' => 1]);

                return successResponse(_trans('message.User activate successfully.'), $userI);
            }
            if (@$request->action == 'inactive') {
                $userI = $this->model->whereIn('id', $request->ids)->update(['status_id' => 4]);

                return successResponse(_trans('message.User inactivate successfully.'), $userI);
            }

            return errorResponse(_trans('message.User status change failed'), [], 400);
        } catch (Throwable $th) {
            return errorResponse($th->getMessage(), [], 400);
        }
    }

    public function destroyAll($request)
    {
        try {
            if (@$request->ids) {
                $user = $this->model->whereIn('id', $request->ids)->update(['deleted_at' => now()]);

                return successResponse(_trans('message.User activate successfully.'), $user);
            } else {
                return errorResponse(_trans('message.User not found'), [], 400);
            }
        } catch (Throwable $th) {
            return errorResponse($th->getMessage(), [], 400);
        }
    }

    public function phonebookFields()
    {
        return [
            _trans('common.Name'),
            _trans('common.Email'),
            _trans('common.Phone'),
            _trans('common.Designation'),
            _trans('common.Department'),
            _trans('common.Role'),
            _trans('common.Status'),
        ];
    }

    public function sendResetMail($id)
    {
        try {
            $user = $this->model->find($id);
            $password = Str::random(8);
            $user->password = Hash::make($password);
            $user->update();
            if (! $this->sendEmail($user, $password)) {
                return errorResponse(_trans('message.Mail not send.'), [], 400);
            } else {
                return successResponse(_trans('message.Mail send successfully.'), [], 200);
            }
        } catch (Throwable $th) {
            return false;
        }
    }

    public function resetDevice($user_id, $device)
    {
        try {
            $user = $this->model->find($user_id);
            if ($device == 'mobile') {
                $user->app_token = null;
                $user->device_id = null;
                $user->device_info = null;
            } else {
                $user->device_uuid = null;
            }
            $user->last_login_device = null;

            $user->save();

            return true;
        } catch (Throwable $th) {
            return $th->getMessage();

            return false;
        }
    }

    public function restore($id)
    {
        $user = User::withTrashed()->find($id);
        $user->status_id = 1;
        $user->restore();

        return true;
    }
}
